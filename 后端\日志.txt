Connected to tts-api, waiting for logs...
GET https://ttsapi.aispeak.top/api/tts/ws/generate - Ok @ 2025/6/29 13:16:15
  (log) [PROGRESS] efac79b682c64479a5f1177507f11b82aad570f04221a6dba8777df00462e7cc: 任务初始化...
  (log) [SSML-SPLIT] Detected SSML directives, using SSML-aware splitting
  (log) [SSML-SPLIT] Split text with SSML into 2 chunks
  (log) [PROGRESS] efac79b682c64479a5f1177507f11b82aad570f04221a6dba8777df00462e7cc: 文本已分割为 2 个片段
  (log) [PROGRESS] efac79b682c64479a5f1177507f11b82aad570f04221a6dba8777df00462e7cc: 正在生成 2 个音频片段...
  (log) [CONCURRENCY] Processing 2 chunks with dynamic concurrency...
  (log) [CONCURRENCY] Calculated optimal concurrency for 2 chunks: 2 (Strategy: Simple Max) {
  chunkCount: 2,
  maxConcurrency: 6,
  finalConcurrency: 2,
  estimatedSubRequests: 3,
  estimatedRounds: 1,
  performanceGain: 'baseline'
}
  (log) [CONCURRENCY] Using 2 concurrent requests for 2 chunks
  (log) Processing chunk 1/2, length: 300
  (log) [ELEVENLABS-API] 🚀 TTS Request Details: {
  url: 'https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1',
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  voiceId: 'pNInz6obpgDQGcFmaJgB',
  modelId: 'eleven_v3',
  textLength: 300,
  textPreview: '[recounting] It started as a dare, like most bad ideas do. My friends, Mark, Chloe, and I, were hudd...',
  voice_settings: { stability: 0.5 },
  payload: {
    text: "[recounting] It started as a dare, like most bad ideas do. My friends, Mark, Chloe, and I, were huddled in my basement, bored out of our minds on a Friday night. The usual video games and movies felt stale. That's when Mark, always the instigator, brought up the old Blackwood Cemetery. [mischievous]",
    model_id: 'eleven_v3',
    voice_settings: { stability: 0.5 }
  },
  payloadSize: '369 bytes',
  timestamp: '2025-06-29T05:16:15.805Z'
}
  (log) [ELEVENLABS-API] 📤 Sending request (attempt 1/3) to ElevenLabs: {
  attemptNumber: 1,
  totalAttempts: 3,
  remainingRetries: 2,
  url: 'https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1',
  timestamp: '2025-06-29T05:16:15.805Z'
}
  (log) Processing chunk 2/2, length: 336
  (log) [ELEVENLABS-API] 🚀 TTS Request Details: {
  url: 'https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1',
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  voiceId: 'pNInz6obpgDQGcFmaJgB',
  modelId: 'eleven_v3',
  textLength: 336,
  textPreview: '"I bet none of us could LAST an hour in there, at midnight, in this fog," he grinned, gesturing towa...',
  voice_settings: { stability: 0.5 },
  payload: {
    text: `"I bet none of us could LAST an hour in there, at midnight, in this fog," he grinned, gesturing towards the window where a thick, unseasonable mist had descended, blanketing everything outside. [scoffs][disgusted] Chloe scoffed, "That old place? It's JUST a bunch of dead people and overgrown weeds." But even she looked a little uneasy`,
    model_id: 'eleven_v3',
    voice_settings: { stability: 0.5 }
  },
  payloadSize: '409 bytes',
  timestamp: '2025-06-29T05:16:15.805Z'
}
  (log) [ELEVENLABS-API] 📤 Sending request (attempt 1/3) to ElevenLabs: {
  attemptNumber: 1,
  totalAttempts: 3,
  remainingRetries: 2,
  url: 'https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1',
  timestamp: '2025-06-29T05:16:15.805Z'
}
  (log) [ELEVENLABS-API] ✅ Request successful: {
  status: 200,
  statusText: 'OK',
  audioSize: '392508 bytes',
  audioSizeKB: '383.31 KB',
  contentType: 'audio/mpeg',
  textToAudioRatio: '1308.36 bytes/char',
  timestamp: '2025-06-29T05:16:36.694Z'
}
  (log) [ELEVENLABS-API] ✅ Request successful: {
  status: 200,
  statusText: 'OK',
  audioSize: '356564 bytes',
  audioSizeKB: '348.21 KB',
  contentType: 'audio/mpeg',
  textToAudioRatio: '1061.20 bytes/char',
  timestamp: '2025-06-29T05:16:40.564Z'
}
  (log) Processing completed: 2 successful, 0 failed
  (log) Successfully processed 2/2 chunks concurrently
  (log) [PROGRESS] efac79b682c64479a5f1177507f11b82aad570f04221a6dba8777df00462e7cc: 正在合并音频...
  (log) [PROGRESS] efac79b682c64479a5f1177507f11b82aad570f04221a6dba8777df00462e7cc: 正在将文件存入云存储...
  (log) [R2-AUDIO] Storing audio for task efac79b682c64479a5f1177507f11b82aad570f04221a6dba8777df00462e7cc: {
  audioSize: 749072,
  audioType: 'ArrayBuffer',
  bucketName: 'AUDIOS',
  key: 'audios/efac79b682c64479a5f1177507f11b82aad570f04221a6dba8777df00462e7cc.mp3',
  sizeInMB: '0.71'
}
  (log) [R2-AUDIO] ✅ Successfully stored audio for task efac79b682c64479a5f1177507f11b82aad570f04221a6dba8777df00462e7cc: {
  audioSize: 749072,
  uploadTimeMs: 313,
  sizeInMB: '0.71',
  timestamp: '2025-06-29T05:16:40.877Z'
}
  (log) [USAGE-UPDATE] Successfully updated usage for 555. Added: 637, Monthly total: 3345
  (log) [KV-STATUS] Storing status for task efac79b682c64479a5f1177507f11b82aad570f04221a6dba8777df00462e7cc: {
  status: 'complete',
  dataSize: 233,
  key: 'status:efac79b682c64479a5f1177507f11b82aad570f04221a6dba8777df00462e7cc'
}
  (log) [KV-STATUS] ✅ Successfully stored status for task efac79b682c64479a5f1177507f11b82aad570f04221a6dba8777df00462e7cc
  (log) [DO-TASK] Closing all WebSocket sessions for task efac79b682c64479a5f1177507f11b82aad570f04221a6dba8777df00462e7cc.
  (log) [DO-TASK] Task efac79b682c64479a5f1177507f11b82aad570f04221a6dba8777df00462e7cc finished. Scheduling cleanup for 2025-06-30T05:16:42.639Z.
  (log) [DO-TASK] Cleanup alarm set successfully.
GET https://ttsapi.aispeak.top/api/tts/ws/generate - Ok @ 2025/6/29 13:16:14