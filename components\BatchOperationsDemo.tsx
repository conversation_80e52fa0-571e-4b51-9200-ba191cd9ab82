"use client"

import React, { useState } from 'react';
import BatchOperations from './BatchOperations';

// 演示组件，用于测试批量操作功能
const BatchOperationsDemo: React.FC = () => {
  const [dialogueLines, setDialogueLines] = useState([
    { id: 1, voice: "21m00Tcm4TlvDq8ikWAM", text: "你好！今天过得怎么样？" },
    { id: 2, voice: "AZnzlk1XvdvUeBnXmlld", text: "我今天过得很好，谢谢你的关心！" },
    { id: 3, voice: "EXAVITQu4vr4xnSDxMaL", text: "那真是太好了，有什么特别的事情吗？" }
  ]);

  const voices = [
    { id: "21m00Tcm4TlvDq8ikWAM", name: "<PERSON>", gender: "female", description: "美式英语，年轻女性" },
    { id: "AZnzlk1XvdvUeBnXmlld", name: "<PERSON>", gender: "male", description: "美式英语，深沉男性" },
    { id: "EXAVITQu4vr4xnSDxMaL", name: "<PERSON>", gender: "female", description: "美式英语，温柔女性" },
    { id: "ErXwobaYiN019PkySvjV", name: "Antoni", gender: "male", description: "美式英语，温暖男性" },
    { id: "MF3mGyEYCl7XYWbV9V6O", name: "Elli", gender: "female", description: "美式英语，情感丰富" }
  ];

  const handleImport = async (newLines: typeof dialogueLines, mode: 'replace' | 'append') => {
    if (mode === 'replace') {
      setDialogueLines(newLines);
    } else {
      setDialogueLines(prev => [...prev, ...newLines]);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">批量操作功能演示</h1>
      
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-3">当前对话列表</h2>
        <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
          {dialogueLines.map((line, index) => {
            const voice = voices.find(v => v.id === line.voice);
            return (
              <div key={line.id} className="flex items-center gap-3 p-2 bg-white rounded border">
                <span className="text-sm font-medium text-blue-600">{voice?.name}</span>
                <span className="text-sm text-gray-600">→</span>
                <span className="text-sm">{line.text}</span>
              </div>
            );
          })}
        </div>
      </div>

      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-3">批量操作</h2>
        <BatchOperations
          dialogueLines={dialogueLines}
          voices={voices}
          onImport={handleImport}
        />
      </div>

      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">使用说明：</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• <strong>复制对话</strong>：将当前所有对话复制到剪贴板，格式为 "声音名称@对话内容"</li>
          <li>• <strong>导入对话</strong>：从剪贴板导入批量对话，支持替换或追加模式</li>
          <li>• <strong>统计信息</strong>：查看对话的详细统计信息，包括字符数、声音分布等</li>
          <li>• <strong>示例格式</strong>：Rachel@你好！今天过得怎么样？</li>
        </ul>
      </div>

      <div className="mt-6 p-4 bg-green-50 rounded-lg">
        <h3 className="font-semibold text-green-800 mb-2">测试步骤：</h3>
        <ol className="text-sm text-green-700 space-y-1">
          <li>1. 点击"复制对话"按钮，复制当前对话到剪贴板</li>
          <li>2. 点击"导入对话"按钮，粘贴刚才复制的内容</li>
          <li>3. 选择"追加到现有对话"模式，点击确认导入</li>
          <li>4. 观察对话列表是否正确增加了新的对话行</li>
          <li>5. 点击"统计"按钮查看统计信息</li>
        </ol>
      </div>
    </div>
  );
};

export default BatchOperationsDemo;
