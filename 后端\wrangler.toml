# 你的 Cloudflare 账户 ID
account_id = "23b9e456d27237601315e751f0d33059"

# Worker 的基本信息
name = "tts-api"
main = "worker.js"
compatibility_date = "2024-03-20"

# Durable Object 绑定
[durable_objects]
bindings = [
  { name = "TTS_TASK_DO", class_name = "TtsTaskDo" }
]

# 【最终修正】Durable Object 迁移记录
# 直接提供一个字符串数组
[[migrations]]
  tag = "v1"
  new_sqlite_classes = ["TtsTaskDo"]

# KV 命名空间绑定
kv_namespaces = [
  { binding = "TTS_STATUS", id = "0ae5fbcb1ed34dab9357ae1a838b34f3" },
  { binding = "USERS",      id = "f79f4b66ec7c4902aa5e65ba33c2c191" },
  { binding = "CARDS",      id = "69d6e32b35dd4a0bb996584ebf3f5b27" },
  { binding = "VOICE_MAPPINGS", id = "065bf81a6ad347d19709b402659608f5" }
]

# R2 存储桶绑定
r2_buckets = [
  { binding = "AUDIOS",       bucket_name = "audios" },
  { binding = "AUDIO_BUCKET", bucket_name = "preview-audio-kf" }
]

# Analytics Engine 数据集绑定
[[analytics_engine_datasets]]
binding = "DO_ANALYTICS"
dataset = "durable_object_locations"

# 环境变量
[vars]
SES_REGION = "ap-guangzhou"
FROM_EMAIL = "<EMAIL>"
FROM_EMAIL_NAME = "tts.aispeak.top"
VERIFICATION_TEMPLATE_ID = "32699"
DEBUG = false
# 控制是否发送详细进度消息的开关 (true/false)
ENABLE_PROGRESS_MESSAGES = false

# 提醒：请不要忘记使用 `wrangler secret put <KEY>` 命令设置以下机密信息：
# - JWT_SECRET
# - TENCENT_SECRET_ID
# - TENCENT_SECRET_KEY