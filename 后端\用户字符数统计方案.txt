我已经仔细分析了您提供的 worker.js 后端代码。

这是一个非常棒的问题，答案是：完全可以加入字符数监控功能，并且实现起来并不复杂。

您的代码架构设计得很好，特别是使用 Durable Objects (TtsTaskDo) 来处理每个TTS任务，这为我们精确地捕获和统计用量提供了完美的“抓手”。

下面我将为您详细分析可行性、复杂度和具体的实现步骤。

一、可行性与复杂度分析

可行性：100%可行。

数据源清晰：无论是单人配音的 input 字符串，还是多人对话的 dialogue 数组，其文本内容在 TtsTaskDo 中都是直接可获取的。计算字符数就是简单的 text.length 操作。

存储位置明确：您已经有了用户系统和 USERS KV 存储。我们只需要在现有的用户数据结构中增加一个字段来存储字符数用量即可。

更新时机完美：TtsTaskDo 中有明确的任务成功 (status: 'complete') 和失败 (status: 'failed') 的逻辑分支。我们可以在任务 成功完成 后，再对用户的字符数进行累加和更新，确保了计费的准确性（失败的任务不计费）。

复杂度：低。

逻辑简单：核心逻辑就是“获取文本长度 -> 读取用户数据 -> 累加数值 -> 写回用户数据”。

代码侵入性小：我们不需要重构现有流程。主要工作是增加一个用量更新的辅助函数，并在 TtsTaskDo 的任务成功路径上调用这个函数。对现有代码的改动非常少，风险很低。

主要工作量：主要工作集中在 TtsTaskDo 类的修改上，以及可能需要为新注册用户初始化用量字段。

二、核心实现思路

我们的目标是记录每个用户 总共 使用了多少字符，以及 当月 使用了多少字符（用于月度统计或限制）。

抓取点：在 TtsTaskDo 的 runSingleTtsProcess 和 runDialogueTtsProcess 方法内部。

计算逻辑：

对于单人任务，字符数为 input.length。

对于多人对话任务，字符数为所有 speaker.text 长度的总和。

存储点：在 USERS KV 中，每个 user:${username} 的 JSON 数据里。

更新时机：在任务被确认为 complete，音频文件成功存入 R2 之后，更新 KV 中的用户数据。

三、具体实现步骤（含代码示例）
第1步：调整用户数据结构

我们需要在 USERS KV 中存储的用户对象里增加一个 usage 字段。

修改所有创建/更新用户数据的地方，比如 handleAuth 中的注册逻辑，确保新用户有初始的 usage 对象。

修改 /api/auth/register 和 /api/auth/verify-email 中的用户对象：

Generated javascript
// 在 handleAuth 函数中的注册部分
const finalUserData = {
    // ... 其他字段
    emailVerified: true,
    createdAt: userData.createdAt,
    vip: { // 假设新用户有初始vip信息或为空
        expireAt: 0,
        type: null
    },
    // 【新增】初始化用量数据
    usage: {
        totalChars: 0,              // 历史总字符数
        monthlyChars: 0,            // 当月已用字符数
        monthlyResetAt: getNextMonthResetTimestamp() // 下次月度重置的时间戳
    }
};

// ... 同样需要修改 /api/auth/register 中的 userData


为了实现 getNextMonthResetTimestamp()，我们可以在 Utils 部分添加一个辅助函数：

Generated javascript
// ========== Utils 函数 ==========
// ... 之前的函数

function getNextMonthResetTimestamp() {
  const now = new Date();
  // 设置为下个月的第一天的 0 点 0 分 0 秒
  const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
  return nextMonth.getTime();
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
第2步：创建通用的用量更新函数

为了代码复用和逻辑清晰，我们在 TtsTaskDo 类中创建一个专门用于更新用户用量的函数。这个函数会处理月度重置的逻辑。

在 TtsTaskDo 类中添加 updateUserUsage 方法：

Generated javascript
export class TtsTaskDo {
  // ... constructor, alarm, fetch, handleSession 等方法

  /**
   * 【新增】更新用户的字符使用量
   * @param {string} username - 用户名
   * @param {number} charCount - 本次任务使用的字符数
   */
  async updateUserUsage(username, charCount) {
    if (!username || charCount <= 0) {
      return;
    }

    try {
      const userKey = `user:${username}`;
      // 注意：直接从 env.USERS 获取最新数据，而不是依赖可能过时的 taskData
      const userDataString = await this.env.USERS.get(userKey);
      if (!userDataString) {
        console.error(`[USAGE-UPDATE] User not found: ${username}`);
        return;
      }

      const userData = JSON.parse(userDataString);

      // 初始化 usage 对象（向后兼容老用户）
      if (!userData.usage) {
        userData.usage = {
          totalChars: 0,
          monthlyChars: 0,
          monthlyResetAt: getNextMonthResetTimestamp()
        };
      }

      const now = Date.now();
      // 检查月度数据是否需要重置
      if (now >= userData.usage.monthlyResetAt) {
        console.log(`[USAGE-UPDATE] Resetting monthly usage for user: ${username}`);
        userData.usage.monthlyChars = 0;
        userData.usage.monthlyResetAt = getNextMonthResetTimestamp();
      }
      
      // 累加字符数
      userData.usage.totalChars += charCount;
      userData.usage.monthlyChars += charCount;

      // 将更新后的用户数据写回 KV
      await this.env.USERS.put(userKey, JSON.stringify(userData));
      
      console.log(`[USAGE-UPDATE] Successfully updated usage for ${username}. Added: ${charCount}, Monthly total: ${userData.usage.monthlyChars}`);

    } catch (error) {
      console.error(`[USAGE-UPDATE] Failed to update usage for user ${username}:`, error);
      // 即使更新失败，也不应该影响主任务流程，所以只记录错误
    }
  }

  // ... runSingleTtsProcess, runDialogueTtsProcess 等方法
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
第3步：在任务成功后调用用量更新函数

现在，我们只需要在两个TTS处理流程中，任务成功的地方调用 updateUserUsage 即可。

1. 修改 runSingleTtsProcess 方法：

Generated javascript
async runSingleTtsProcess() {
    // ...
    // 从 this.taskData 获取所需参数
    const { taskId, input, voice, stability, similarity_boost, style, speed, model, token } = this.taskData;
    
    try {
        const username = await verifyToken(token, this.env);
        // ... (checkVip, splitText, processChunks, storeAudioFile 等)

        // 【在这里添加调用】
        // 在 storeStatusKV 之后，广播 complete 消息之前
        const charCount = input.length;
        await this.updateUserUsage(username, charCount);

        // 将最终结果存入KV，用于历史记录查询
        await storeStatusKV(this.env, taskId, finalStatus);

        // 通过WebSocket发送最终成功结果
        this.broadcast({ type: 'complete', ...finalStatus });
        
        // ...
    } catch (error) {
        // ... 错误处理
    } finally {
        // ... 清理逻辑
    }
  }
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

2. 修改 runDialogueTtsProcess 方法：

Generated javascript
async runDialogueTtsProcess() {
    // ...
    const { taskId, dialogue, model, stability, similarity_boost, style, speed, token } = this.taskData;

    try {
      const username = await verifyToken(token, this.env);
      // ... (checkVip, 循环处理 dialogue)

      // ... (合并音频，存入R2等)

      // 【在这里添加调用】
      // 在 storeStatusKV 之后，广播 complete 消息之前
      const charCount = dialogue.reduce((sum, speaker) => sum + (speaker.text ? speaker.text.length : 0), 0);
      await this.updateUserUsage(username, charCount);

      await storeStatusKV(this.env, taskId, finalStatus);
      this.broadcast({ type: 'complete', ...finalStatus });

      // ...
    } catch (error) {
        // ... 错误处理
    } finally {
        // ... 清理逻辑
    }
  }
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
第4步（可选，但推荐）：提供用量查询接口

为了让用户能看到自己的用量，您可以增加一个 API 接口。

在 handleRequest 函数中添加新路由：

Generated javascript
// ... 在 /api/user/quota 路由之后

  if (url.pathname === '/api/user/usage') {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '');
    if (!token) {
      return createAuthErrorResponse(new Error('Invalid token type'));
    }

    try {
      const username = await verifyToken(token, env);
      const userDataString = await env.USERS.get(`user:${username}`);
      if (!userDataString) {
          return new Response(JSON.stringify({ error: 'User not found' }), {
            status: 404, headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
          });
      }
      const userData = JSON.parse(userDataString);
      
      // 确保老用户也能正确返回
      const usage = userData.usage || {
          totalChars: 0,
          monthlyChars: 0,
          monthlyResetAt: getNextMonthResetTimestamp()
      };

      // 检查月度重置
      if (Date.now() >= usage.monthlyResetAt) {
          usage.monthlyChars = 0;
          usage.monthlyResetAt = getNextMonthResetTimestamp();
      }

      return new Response(JSON.stringify(usage), {
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    } catch (error) {
      return createAuthErrorResponse(error);
    }
  }
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
四、潜在问题与优化

并发/竞争条件：如果同一个用户在极短时间内发起多个任务，可能会有多个 TtsTaskDo 实例同时读写同一个 user:${username} 的 KV 数据，导致用量统计不完全精确（后写入的覆盖先写入的）。对于字符统计这种非关键性金融数据，这种微小的误差通常可以接受。如果需要绝对精确，则需要引入更复杂的锁机制，但会增加系统复杂度和成本。以您当前的架构，保持简单可能是最好的选择。

成本：每次任务成功都会增加一次 KV 的 GET 和 PUT 操作。考虑到Cloudflare KV的免费额度很高，这在初期基本不是问题，但需要知晓。

历史数据迁移：对于已经存在的老用户，他们的 usage 字段是空的。上面的代码已经做了兼容处理（如果!userData.usage 则创建），所以老用户在下次使用时会自动开始统计，历史总数从0开始。

总结

总的来说，为您的应用添加字符数监控功能是一个低复杂度、高价值的增强。

您只需要：

在用户数据结构中增加 usage 对象。

在 TtsTaskDo 中添加一个 updateUserUsage 的辅助方法来处理数据更新和月度重置。

在 runSingleTtsProcess 和 runDialogueTtsProcess 的成功路径上，计算字符数并调用该方法。

（可选）增加一个 /api/user/usage 接口供前端查询。

整个过程对现有稳定逻辑的改动极小，可以安全、快速地部署。