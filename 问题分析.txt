现在看 processChunks 最后抛出错误的地方：
// worker.js L2527
if (failureRate >= 0.5) {
    // 如果失败率 >= 50%，抛出错误（可能是系统性问题）
    const representativeError = failedResults[0]?.error || new Error('Unknown processing error');
    representativeError.message = `Too many chunks failed...`; // 覆盖message
    throw representativeError; // 抛出带有原始属性的代表性错误！
}

你需要修改这里，让它抛出一个能保留 isDataCenterRetryable 标志的错误。一个好的方法是找到第一个可重试的错误，并把它作为“代表性错误”抛出。
建议修改 processChunks 的末尾部分：
// ... after separating successful and failed results

if (failedResults.length > 0) {
    // 检查是否有任何一个失败是可重试的
    const firstRetryableError = failedResults.find(r => r.error?.isDataCenterRetryable)?.error;

    if (firstRetryableError) {
        // 如果找到了可重试的错误，就优先把它抛出去，并附加上下文信息
        const overallError = new Error(`Data center retryable error detected: ${firstRetryableError.message}`);
        overallError.isDataCenterRetryable = true; // 明确传递标志
        overallError.originalStatus = firstRetryableError.status;
        throw overallError;
    }

    // 如果失败率很高，但没有一个是可重试的错误，则抛出通用错误
    const failureRate = failedResults.length / chunks.length;
    if (failureRate >= 0.5) {
        throw new Error(`Too many chunks failed (${failedResults.length}/${chunks.length}). This may indicate a non-retryable system issue.`);
    }
}

// ... 后续的重试逻辑可以保持不变

修正 generateSpeech (重要！)
修正从 response.json() 中提取错误消息的方式。
// worker.js L2385
} else {
    const errorData = await response.json();

    // 从 detail 对象中提取 message
    const errorMessage = errorData?.detail?.message || JSON.stringify(errorData) || 'Failed to generate speech';

    const enhancedError = new Error(errorMessage);
    enhancedError.status = response.status;
    enhancedError.isDataCenterRetryable = isDataCenterRetryableError(enhancedError, response.status);
    enhancedError.originalError = errorData;

    throw enhancedError;
}