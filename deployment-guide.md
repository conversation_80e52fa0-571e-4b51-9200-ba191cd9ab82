# 声音配置文件迁移部署指南

## 🎯 部署目标

将 `lib/voices.json` 文件上传到腾讯云COS，实现动态加载，提升中国大陆用户的访问速度。

## 📋 部署步骤

### 1. 准备voices.json文件

当前的 `lib/voices.json` 文件已经包含了完整的声音配置数据，可以直接使用。

**文件位置**: `d:\ai-tts-workspace\lib\voices.json`
**文件大小**: 约23KB
**数据条目**: 75+个声音配置

### 2. 上传到腾讯云COS

#### 方式一：通过腾讯云控制台上传

1. 登录腾讯云控制台
2. 进入对象存储COS服务
3. 选择存储桶：`my-tts-config-1256990318`（广州地域）
4. 上传文件：
   - 文件名：`voices.json`
   - 路径：根目录
   - 访问权限：公有读

#### 方式二：通过命令行工具

```bash
# 使用腾讯云CLI工具
coscli cp lib/voices.json cos://my-tts-config-1256990318/voices.json
```

### 3. 验证上传结果

上传完成后，访问以下URL验证文件可正常访问：

```
https://my-tts-config-1256990318.cos.ap-guangzhou.myqcloud.com/voices.json
```

**预期结果**：
- 返回JSON格式的声音配置数据
- 响应头包含正确的Content-Type: application/json
- 文件大小约23KB

### 4. 配置CORS（如果需要）

如果遇到跨域问题，需要在COS控制台配置CORS规则：

```json
{
  "CORSRules": [
    {
      "AllowedOrigins": ["*"],
      "AllowedMethods": ["GET"],
      "AllowedHeaders": ["*"],
      "MaxAgeSeconds": 3600
    }
  ]
}
```

### 5. 环境变量配置

确认 `.env.local` 文件中的配置正确：

```env
NEXT_PUBLIC_VOICES_JSON_URL=https://my-tts-config-1256990318.cos.ap-guangzhou.myqcloud.com/voices.json
```

## 🧪 测试验证

### 1. 本地测试

```bash
# 启动开发服务器
npm run dev

# 访问应用，观察：
# - 是否显示加载动画
# - 声音数据是否正常加载
# - 所有功能是否正常工作
```

### 2. 网络测试

```bash
# 测试COS文件访问
curl -I https://my-tts-config-1256990318.cos.ap-guangzhou.myqcloud.com/voices.json

# 预期响应：
# HTTP/1.1 200 OK
# Content-Type: application/json
# Content-Length: ~23000
```

### 3. 错误处理测试

1. **网络错误测试**：
   - 临时修改环境变量为无效URL
   - 确认显示网络错误提示

2. **重试机制测试**：
   - 点击重试按钮
   - 确认能够重新加载数据

## 🔄 回滚方案

如果遇到问题，可以快速回滚到静态导入方式：

1. 注释掉useVoices hook的使用
2. 恢复静态导入：`import voicesData from "@/lib/voices.json"`
3. 恢复原有的声音图标映射逻辑

## 📊 性能对比

### 迁移前（静态导入）
- 首次加载：voices.json打包在bundle中
- 后续访问：无额外网络请求
- 更新方式：需要重新部署应用

### 迁移后（动态加载）
- 首次加载：额外的网络请求（~23KB）
- 后续访问：浏览器缓存（可配置）
- 更新方式：直接更新COS文件，无需重新部署

### 中国大陆用户优势
- 腾讯云COS广州节点，延迟更低
- 国内CDN加速，访问速度更快
- 避免Cloudflare海外回源的延迟

## ✅ 部署完成检查清单

- [ ] voices.json文件已上传到COS
- [ ] 文件URL可正常访问
- [ ] CORS配置正确（如需要）
- [ ] 环境变量配置正确
- [ ] 本地测试通过
- [ ] 所有功能正常工作
- [ ] 错误处理机制正常
- [ ] 性能表现符合预期

## 🚀 后续优化建议

1. **缓存策略**：添加localStorage缓存，减少重复请求
2. **版本控制**：在URL中添加版本参数，便于缓存更新
3. **监控告警**：设置COS访问监控，及时发现问题
4. **备份方案**：保留静态文件作为fallback选项
