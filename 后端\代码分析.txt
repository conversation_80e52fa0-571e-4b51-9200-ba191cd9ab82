您是对的！非常感谢您的指正，这让我重新审视了代码并找到了真正的问题所在。我为之前的错误分析表示歉意。您指出的路径问题是正确的，/api/tts/ws/dialogue/generate 确实不以 /api/tts/ws/generate 开头。

我的第一个诊断虽然方向正确（路由问题），但定位的具体 if 判断是错误的。现在，我们来追踪正确的执行路径，找到真正的根本原因。

1. 重新审视问题和日志

用户操作: 前端在“多人对话”模式下点击“生成音频”。

前端请求: new WebSocket("wss://ttsapi.aispeak.top/api/tts/ws/dialogue/generate")，这会发起一个 GET 请求，并携带 Upgrade: websocket 请求头。

后端日志: GET https://ttsapi.aispeak.top/api/tts/ws/dialogue/generate - Ok

前端报错: “与服务器的实时连接发生错误，请检查网络并重试。”

这个日志中的 Ok (HTTP 200) 仍然是关键。它证明了请求被当作一个普通的 HTTP 请求成功处理了，而不是被升级为 WebSocket (HTTP 101)。现在我们需要找到为什么它被当作普通 HTTP 请求处理了。

2. 定位真正的代码缺陷

让我们仔细追踪 worker.js 中 handleRequest 函数的路由判断顺序：

Generated javascript
// worker.js -> handleRequest 函数的逻辑流
async function handleRequest(request, env, event = null) {
  // ...
  const url = new URL(request.url);

  // 1. 判断是否为单人WebSocket
  if (url.pathname.startsWith('/api/tts/ws/generate')) {
    // ...
  }

  // 2. 判断是否为认证路由
  if (url.pathname.startsWith('/api/auth/')) {
    // ...
  }

  // 3. 【【【真正的问题在这里】】】
  // 判断是否为通用TTS路由
  if (url.pathname.startsWith('/api/tts/')) {
    // ... 认证 ...
    try {
        const username = await verifyToken(token, env);
        // 将请求传递给 handleTTS 函数处理
        const response = await handleTTS(request, username, env, event);
        // ... 返回响应
        return new Response(response.body, { ... });
    } catch (error) {
        // ... 错误处理
    }
  }

  // ... 其他路由判断 ...

  // 6. 判断是否为多人对话WebSocket (永远不会执行到这里)
  if (url.pathname === '/api/tts/ws/dialogue/generate') {
    // ...
  }
}


这是问题的真相：

一个多人对话的请求，其路径是 /api/tts/ws/dialogue/generate。

这个路径确实满足了第 3 个判断条件：url.pathname.startsWith('/api/tts/')。

因此，这个 WebSocket 升级请求被错误地路由到了为普通 HTTP 请求设计的 handleTTS 函数中。

在 handleTTS 函数内部，并没有处理 WebSocket 升级的逻辑，也没有匹配 GET /api/tts/ws/dialogue/generate 的路由。它最终会走到函数末尾，返回一个 Not Found (404) 响应。

然而，外层的 handleRequest 捕获了这个响应，并可能因为某些原因（比如默认的成功日志或CORS头处理）最终在日志中显示为 Ok，但返回给浏览器的状态码绝对不是 101 Switching Protocols。

由于没有收到 101 响应，前端的 WebSocket 客户端连接失败，从而触发了您看到的错误。

最后那个为多人对话准备的 if (url.pathname === '/api/tts/ws/dialogue/generate') 判断，因为被前面的通用判断拦截了，所以永远没有机会被执行，变成了“死代码”。

3. 解决方案：调整路由判断的优先级

解决这个问题的唯一方法是调整 handleRequest 函数中 if 判断的顺序。必须将最具体的 WebSocket 路由判断，放在更通用的 HTTP 路由判断之前。

请按如下方式修改 worker.js 中的 handleRequest 函数：

修改前 (有问题的代码):

Generated javascript
// worker.js (修改前)
async function handleRequest(request, env, event = null) {
  // ...
  if (url.pathname.startsWith('/api/tts/ws/generate')) {
    // ... 单人WS逻辑
  }

  if (url.pathname.startsWith('/api/auth/')) {
    // ... 认证逻辑
  }

  // 这个判断太宽泛，错误地捕获了WS请求
  if (url.pathname.startsWith('/api/tts/')) {
    // ... 通用HTTP TTS逻辑
  }

  // ...

  // 这段代码永远不会被执行
  if (url.pathname === '/api/tts/ws/dialogue/generate') {
    // ... 多人WS逻辑
  }
  // ...
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

修改后 (修复后的代码):

将所有 WebSocket 路由处理逻辑提升到通用 HTTP 路由之前。

Generated javascript
// worker.js (修改后)
async function handleRequest(request, env, event = null) {
  // 处理 CORS 预检请求
  if (request.method === 'OPTIONS') {
    return handleOptions(request);
  }

  const url = new URL(request.url);

  // 处理预览音频请求
  if (url.pathname.startsWith('/preview-audio-kf/')) {
    // ... (保持不变)
  }

  // --- START: WebSocket 路由处理 (高优先级) ---

  // 【重要修复】使用精确匹配，并放在前面
  if (url.pathname === '/api/tts/ws/generate') {
    const taskId = generateUUID();
    const doId = env.TTS_TASK_DO.idFromName(taskId);
    const stub = env.TTS_TASK_DO.get(doId);
    return stub.fetch(request);
  }

  // 【重要修复】将多人对话的WS处理逻辑移到这里
  if (url.pathname === '/api/tts/ws/dialogue/generate') {
    const doId = env.TTS_TASK_DO.idFromName(generateUUID());
    const stub = env.TTS_TASK_DO.get(doId);
    return stub.fetch(request);
  }

  // --- END: WebSocket 路由处理 ---


  // --- START: HTTP API 路由处理 (低优先级) ---

  if (url.pathname.startsWith('/api/auth/')) {
    // ... (保持不变)
  }

  // 【重要修复】这个宽泛的判断现在不会错误捕获WS请求了
  if (url.pathname.startsWith('/api/tts/')) {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '');
    if (!token) {
        return new Response(JSON.stringify({
          error: 'Unauthorized',
          code: 'NO_TOKEN'
        }), { status: 401, headers: { ...corsHeaders(), 'Content-Type': 'application/json' } });
    }

    try {
        const username = await verifyToken(token, env);
        // 【注意】这里不再需要判断 /generate，因为旧的HTTP生成方式已废弃
        if (request.method === 'POST' && request.url.endsWith('/generate')) {
            return new Response(JSON.stringify({ error: 'This endpoint is deprecated. Please use WebSocket connection.' }), {
                status: 400, headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
            });
        }

        // 保留 handleTTS 以处理 /status 和 /download
        const response = await handleTTS(request, username, env, event);
        const headers = {...corsHeaders(), ...response.headers};
        return new Response(response.body, { status: response.status, headers: headers });
    } catch (error) {
        if (error.cause === 'quota') {
          return new Response(JSON.stringify({ error: error.message, type: 'quota' }), { status: 403, headers: { ...corsHeaders(), 'Content-Type': 'application/json' } });
        }
        if (isAuthError(error)) { return createAuthErrorResponse(error); }
        return new Response(JSON.stringify({ error: 'Unauthorized' }), { status: 401, headers: { ...corsHeaders(), 'Content-Type': 'application/json' } });
    }
  }

  if (url.pathname === '/api/card/use') {
    // ... (保持不变)
  }

  if (url.pathname === '/api/user/quota') {
    // ... (保持不变)
  }

  // 这段代码现在可以删除了，因为它已经被移到前面
  // if (url.pathname === '/api/tts/ws/dialogue/generate') { ... }

  return new Response('Not Found', {
    status: 404,
    headers: corsHeaders()
  });
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
总结

您是对的，问题的根源比我最初想的更深一层，但本质仍然是路由逻辑缺陷。

根本原因：过于宽泛的 if (url.pathname.startsWith('/api/tts/')) 判断，在路由顺序上优先于更具体的多人对话 WebSocket 判断，导致 WebSocket 请求被错误地当作普通 HTTP 请求处理。

解决方案：调整 handleRequest 函数中的路由判断顺序，确保所有需要特殊处理的、路径更具体的 WebSocket 路由 (/api/tts/ws/generate 和 /api/tts/ws/dialogue/generate) 先被检查，然后再检查通用的 /api/tts/ HTTP 路由。

请应用上述代码修改，问题即可解决。