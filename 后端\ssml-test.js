// SSML功能测试脚本
// 用于验证新的splitText函数是否正确处理SSML指令

// 模拟splitText函数的核心逻辑（用于测试）
async function splitTextWithSSML(text, maxLength) {
  const parts = text.split(/(\[.*?\])/g).filter(Boolean);
  const chunks = [];
  let currentChunk = "";

  for (const part of parts) {
    if (currentChunk.length + part.length > maxLength) {
      if (currentChunk.trim()) {
        chunks.push(currentChunk.trim());
      }
      
      if (part.length > maxLength) {
        if (/^\[.*?\]$/.test(part.trim())) {
          console.warn('Warning: SSML directive exceeds maxLength:', part.substring(0, 50) + '...');
          chunks.push(part.trim());
          currentChunk = "";
        } else {
          // 这里应该调用smartSplitLongText，但为了测试简化处理
          chunks.push(part.trim());
          currentChunk = "";
        }
      } else {
        currentChunk = part;
      }
    } else {
      currentChunk += part;
    }
  }

  if (currentChunk.trim()) {
    chunks.push(currentChunk.trim());
  }
  
  if (chunks.length === 0 && text.length > 0) {
    chunks.push(text);
  }

  return chunks;
}

// 测试用例
async function runTests() {
  console.log('=== SSML功能测试开始 ===\n');

  // 测试1: 基本SSML指令
  console.log('测试1: 基本SSML指令');
  const test1 = "[calmly] Hello world. [whispering] This is a secret.";
  const result1 = await splitTextWithSSML(test1, 490);
  console.log('输入:', test1);
  console.log('输出:', result1);
  console.log('块数:', result1.length);
  console.log('');

  // 测试2: 长文本与SSML混合
  console.log('测试2: 长文本与SSML混合');
  const test2 = "[calmly] In the ancient land of Eldoria, where skies shimmered and forests [whispering] whispered secrets to the wind, lived a dragon named Zephyros. [sarcastic] Not the 'burn it all down' kind! [exhales] [thoughtful] he was gentle, WISE, with eyes like old stars. [softly] [reverent] Even the birds fell silent when he passed through the emerald valleys, carrying stories of forgotten times and wisdom beyond mortal understanding.";
  const result2 = await splitTextWithSSML(test2, 200); // 使用较小的maxLength来测试分割
  console.log('输入:', test2);
  console.log('输出:');
  result2.forEach((chunk, index) => {
    console.log(`  块${index + 1} (${chunk.length}字符): ${chunk}`);
  });
  console.log('');

  // 测试3: 无SSML的普通文本（应该不受影响）
  console.log('测试3: 无SSML的普通文本');
  const test3 = "This is a normal text without any SSML directives. It should be processed normally.";
  const hasSSML = /\[.*?\]/.test(test3);
  console.log('输入:', test3);
  console.log('检测到SSML:', hasSSML);
  console.log('');

  // 测试4: 空文本和边界情况
  console.log('测试4: 边界情况');
  const test4a = "";
  const test4b = "[only-ssml]";
  const test4c = "只有普通文本";
  
  console.log('空文本检测SSML:', /\[.*?\]/.test(test4a));
  console.log('纯SSML检测:', /\[.*?\]/.test(test4b));
  console.log('纯文本检测SSML:', /\[.*?\]/.test(test4c));
  console.log('');

  // 测试5: 验证SSML指令完整性
  console.log('测试5: SSML指令完整性验证');
  const test5 = "[very-long-ssml-directive-that-might-cause-issues] Short text. [another] More text here.";
  const result5 = await splitTextWithSSML(test5, 50);
  console.log('输入:', test5);
  console.log('输出:');
  result5.forEach((chunk, index) => {
    console.log(`  块${index + 1}: ${chunk}`);
    // 验证SSML指令是否完整
    const ssmlMatches = chunk.match(/\[.*?\]/g);
    if (ssmlMatches) {
      console.log(`    包含SSML指令: ${ssmlMatches.join(', ')}`);
    }
  });

  console.log('\n=== SSML功能测试完成 ===');
}

// 运行测试
runTests().catch(console.error);
