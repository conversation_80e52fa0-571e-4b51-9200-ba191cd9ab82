# API切换功能测试说明

## 🎯 测试目的

验证API自动切换功能是否正常工作，确保：
1. ✅ 失败检测和切换逻辑正确
2. ✅ 不影响现有代码逻辑
3. ✅ 性能表现良好
4. ✅ 边界情况处理正确

## 📁 测试文件

### 1. **核心测试脚本**
- `scripts/test-api-switching.js` - 核心测试逻辑和模拟API管理器
- `scripts/run-api-tests.js` - Node.js命令行测试运行器
- `test-api-switching.html` - 浏览器交互式测试页面

### 2. **测试覆盖范围**

#### **基础功能测试**
- ✅ 初始状态验证
- ✅ 失败记录和切换逻辑
- ✅ 成功状态重置
- ✅ 所有API用尽处理
- ✅ 重置到主API功能
- ✅ 切换冷却机制

#### **边界情况测试**
- ✅ 空API列表处理
- ✅ 无效API URL处理
- ✅ 极高失败计数处理

#### **性能测试**
- ✅ 大量失败记录性能
- ✅ 状态获取性能
- ✅ 内存使用情况

#### **集成测试**
- ✅ 模拟真实使用场景
- ✅ 随机成功/失败模式
- ✅ 长期运行稳定性

## 🚀 运行测试

### **方法1: 命令行测试（推荐）**

```bash
# 进入项目目录
cd d:\ai-tts-workspace

# 运行完整测试套件
node scripts/run-api-tests.js
```

**输出示例：**
```
🚀 API切换功能测试开始
==================================================

📋 基础功能测试
------------------------------
✅ PASS: 初始API索引应为0
✅ PASS: 初始API应为主API
✅ PASS: 总API数量应为3
...

📊 测试总结
==================================================
⏱️  总耗时: 45ms
📈 测试结果: 15/15 通过
🎉 所有测试通过！API切换功能正常工作
✅ 确认不会影响现有代码逻辑
```

### **方法2: 浏览器交互式测试**

1. 直接在浏览器中打开 `test-api-switching.html`
2. 点击"🚀 运行所有测试"按钮
3. 观察实时日志和状态变化
4. 使用各种按钮进行手动测试

**功能按钮：**
- 🚀 运行所有测试 - 执行完整测试套件
- 🔧 单项测试 - 运行单个测试用例
- 💥 模拟失败 - 手动触发失败记录
- ✅ 模拟成功 - 手动触发成功记录
- 🔄 重置状态 - 重置API管理器到初始状态
- 🗑️ 清空日志 - 清除测试日志

### **方法3: 集成到现有项目**

```javascript
// 在浏览器控制台中运行
const tests = new ApiSwitchingTests();
tests.runAllTests().then(() => {
  console.log('测试完成');
});
```

## 📊 测试结果解读

### **成功标准**
- ✅ 所有基础功能测试通过
- ✅ 边界情况处理正确
- ✅ 性能指标在可接受范围内
- ✅ 集成测试模拟场景正常

### **关键指标**
- **切换准确性**: 3次失败后必须切换
- **状态重置**: 成功后失败计数归零
- **性能要求**: 10000次操作 < 1000ms
- **内存稳定**: 长期运行无内存泄漏

### **失败排查**
如果测试失败，检查：
1. 环境变量配置是否正确
2. API地址格式是否有效
3. 切换阈值设置是否合理
4. 浏览器控制台是否有错误

## 🔧 自定义测试

### **修改测试配置**
编辑 `scripts/test-api-switching.js` 中的 `mockEnv` 对象：

```javascript
const mockEnv = {
  NEXT_PUBLIC_API_URL: 'https://your-main-api.com',
  NEXT_PUBLIC_BACKUP_API_URLS: 'https://backup1.com,https://backup2.com',
  NEXT_PUBLIC_API_SWITCH_THRESHOLD: '5' // 修改切换阈值
}
```

### **添加自定义测试用例**
在 `ApiSwitchingTests` 类中添加新的测试方法：

```javascript
testCustomScenario() {
  this.log('开始自定义测试')
  // 你的测试逻辑
  this.assert(condition, '测试描述')
  this.log('自定义测试完成')
}
```

## 🛡️ 安全性验证

### **数据隔离**
- ✅ 测试使用模拟数据，不影响生产环境
- ✅ 不会发起真实的网络请求
- ✅ 不会修改实际的API配置

### **代码隔离**
- ✅ 测试代码独立于主应用代码
- ✅ 使用模拟的API管理器
- ✅ 不会影响现有功能逻辑

## 📈 持续集成

### **自动化测试**
可以将测试集成到CI/CD流程中：

```yaml
# GitHub Actions 示例
- name: Run API Switching Tests
  run: node scripts/run-api-tests.js
```

### **定期验证**
建议定期运行测试以确保：
- 新代码变更不破坏切换功能
- 配置更新后功能正常
- 性能保持在可接受范围内

## 🎉 测试完成确认

当看到以下输出时，表示API切换功能完全正常：

```
🎉 所有测试通过！API切换功能正常工作
✅ 确认不会影响现有代码逻辑
```

这意味着：
- ✅ API切换逻辑正确实现
- ✅ 失败检测机制工作正常
- ✅ 状态管理功能完善
- ✅ 性能表现良好
- ✅ 不会破坏现有功能

## 📞 问题反馈

如果测试过程中遇到问题：
1. 查看浏览器控制台错误信息
2. 检查环境变量配置
3. 确认API地址格式正确
4. 运行单项测试定位具体问题

测试脚本确保了API切换功能的可靠性和稳定性，为生产环境的使用提供了充分的信心保证。
