好的，我们来深入分析你提供的 worker.js 后端代码。这是一个非常典型的 Cloudflare Worker + KV + Durable Objects 的应用架构。要添加PRO套餐，需要修改几个关键点。

你的系统设计得相当不错，特别是将套餐配置、卡密使用和权限检查分离开来，这使得扩展变得相对容易。

下面我将分步为你剖析需要修改的地方，并提供具体的代码建议。

核心分析：修改逻辑

数据源头： 首先要在后端定义什么是“PRO套餐”。你的代码中有一个 PACKAGES 常量，这是所有套餐类型的“唯一真实来源”（Single Source of Truth）。我们需要在这里添加PRO套餐的定义。

卡密生成： 你的代码逻辑是通过卡密（卡密）来充值的。因此，你需要一种方式来生成对应PRO套餐的新卡密。虽然生成卡密的代码不在这里，但我会说明你需要做什么。

卡密兑换逻辑 (useCard)： 用户输入卡密后，useCard 函数会验证并为用户账户应用套餐。我们需要确保这个函数能正确处理新的PRO套餐类型。

权限检查 (checkVip)： 当用户使用TTS等核心功能时，checkVip 函数会检查其会员身份。目前它只检查是否是“VIP”，但未来你可能需要区分“标准VIP”和“PRO VIP”的权限。我会提供一个更具扩展性的方案。

前端状态同步 (/api/user/quota)： 前端需要知道用户当前的套餐状态（是否是PRO，何时到期）来显示正确的UI。我们需要确保这个接口返回了足够的信息。

详细修改步骤
1. 定义新的PRO套餐 (核心修改)

这是最关键的一步。在你的代码底部，找到 PACKAGES 常量。我们需要在这里添加三个PRO套餐的定义。为了区分，我们可以用 PM (Pro Month), PQ (Pro Quarter), PH (Pro Half-year)作为套餐类型标识。

找到这段代码 (约在 3000 行左右):

Generated javascript
// 卡密套餐配置
const PACKAGES = {
  'M': { days: 30, price: 15 },     // 月套餐
  'Q': { days: 90, price: 42 },     // 季度套餐
  'H': { days: 180, price: 82 },    // 半年套餐
  'T': { days: 0.0208, price: 0 } // 隐藏的30分钟测试套餐
};


修改后建议：
在这里加入PRO套餐的定义。价格可以根据你的业务来定，这里我先用你UI图上的价格（月度PRO为¥25，季度PRO为¥55，半年PRO为¥99，这里假设你后台也用此价格做记录）。

Generated javascript
// 卡密套餐配置
const PACKAGES = {
  // --- 标准套餐 ---
  'M': { days: 30, price: 15 },     // 月套餐
  'Q': { days: 90, price: 42 },     // 季度套餐
  'H': { days: 180, price: 82 },    // 半年套餐
  
  // --- 新增：PRO套餐 ---
  'PM': { days: 30, price: 25 },    // 月度PRO套餐
  'PQ': { days: 90, price: 55 },    // 季度PRO套餐
  'PH': { days: 180, price: 99 },   // 半年PRO套餐

  // --- 特殊套餐 ---
  'T': { days: 0.0208, price: 0 } // 隐藏的30分钟测试套餐
};
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

分析：

我们添加了 PM, PQ, PH 三个新的 key。

useCard 和其他逻辑会直接从这个对象中读取天数和价格，因此这一步是所有后续修改的基础。


你需要确保你的卡密生成工具现在可以创建类型为 PM, PQ, 和 PH 的卡密。

3. 检查卡密兑换逻辑 (useCard)

我们来检查 useCard 函数。它的作用是当用户兑换卡密时，更新用户的会员到期时间。

原始代码 (约在 3020 行左右):

Generated javascript
// 使用卡密
async function useCard(code, username, env) {
  // ... (省略了卡密验证和状态检查部分)
  try {
    const userData = JSON.parse(await env.USERS.get(`user:${username}`));

    // 更新会员数据结构 - 使用当前时间和过期时间中的较大值作为基准
    const baseTime = Math.max(userData.vip?.expireAt || 0, Date.now());
    userData.vip = {
      // 从基准时间开始添加新购买的天数
      expireAt: baseTime + (PACKAGES[card.t].days * 86400000),
      type: card.t
    };
    
    // ... (省略了后续的保存逻辑)
  } catch (error) {
    // ...
  }
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

分析与结论：
这部分代码写得非常好！它是通用的。

它通过 card.t 获取套餐类型（例如，现在会是 'PM'）。

然后用 PACKAGES[card.t].days 从我们第一步修改的 PACKAGES 对象中查找对应的天数。

它还将套餐类型 card.t 保存到了 userData.vip.type 中。这个字段至关重要，我们后面会用到。

因此，useCard 函数本身不需要任何修改。 只要 PACKAGES 对象更新了，它就能自动处理新的PRO套餐。

4. 升级权限检查逻辑 (checkVip)

checkVip 是决定用户能否使用付费功能的“守门员”。

原始代码 (约在 2915 行左右):

Generated javascript
async function checkVip(username, env) {
  const userData = JSON.parse(await env.USERS.get(`user:${username}`));
  if (!userData.vip || Date.now() > userData.vip.expireAt) {
    throw new Error('请先开通会员', { cause: 'quota' });
  }
  // ... (省略了测试套餐的逻辑)
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

分析与建议：
目前的检查逻辑很简单：只要是vip且未过期就行。对于所有TTS功能都需要付费的场景，这没问题。

但是，如果未来PRO套餐有专属功能（比如更高质量的模型、专属声音等），这个简单的检查就不够了。我强烈建议你现在就将它升级，使其更具扩展性。

修改后建议（可扩展方案）：
我们可以给 checkVip 增加一个可选参数 requiredTier，用于指定需要哪个级别的会员。

Generated javascript
/**
 * 升级版VIP权限检查
 * @param {string} username - 用户名
 * @param {object} env - Cloudflare环境变量
 * @param {('STANDARD'|'PRO')} [requiredTier='STANDARD'] - 要求最低的会员等级 ('STANDARD' 或 'PRO')
 */
async function checkVip(username, env, requiredTier = 'STANDARD') {
  const userData = JSON.parse(await env.USERS.get(`user:${username}`));

  // 1. 基础检查：是否有会员资格且未过期
  if (!userData.vip || Date.now() > userData.vip.expireAt) {
    throw new Error('请先开通会员', { cause: 'quota' });
  }

  const userTier = userData.vip.type;

  // 2. 等级检查：如果要求PRO权限
  if (requiredTier === 'PRO') {
    // 检查套餐类型是否以 'P' 开头 (PM, PQ, PH)
    if (!userTier || !userTier.startsWith('P')) {
      throw new Error('此功能需要PRO会员权限', { cause: 'quota' });
    }
  }

  // 3. 测试套餐的特殊逻辑 (保持不变)
  if (userData.vip.type === 'T') {
    const remainingTime = Math.max(0, userData.vip.expireAt - Date.now()) / 1000;
    if (remainingTime <= 0) {
      throw new Error('测试时间已用完，请充值', { cause: 'quota' });
    }
    console.log(`测试套餐剩余时间: ${remainingTime.toFixed(1)}秒`);
  }
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

如何使用这个新函数：

对于所有会员都能用的功能，像以前一样调用：
await checkVip(username, env); 或 await checkVip(username, env, 'STANDARD');

对于只有PRO会员才能用的专属功能，这样调用：
await checkVip(username, env, 'PRO');

这个改动让你未来能轻松地区分不同等级的权限，而不需要再重构代码。

5. 检查前端状态同步接口 (/api/user/quota)

这个接口是前端显示“月度会员”、“季度PRO会员”等状态的依据。

原始代码 (约在 4060 行左右):

Generated javascript
if (url.pathname === '/api/user/quota') {
    // ... (省略token验证)
    try {
      const username = await verifyToken(token, env);
      const userData = JSON.parse(await env.USERS.get(`user:${username}`));
      const vip = userData.vip || { expireAt: 0 };

      const response = {
        isVip: Date.now() < vip.expireAt,
        expireAt: vip.expireAt,
        type: vip.type, // <-- 这个字段已经有了！
        // ... (省略剩余时间逻辑)
      };

      return new Response(JSON.stringify(response), {
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    } catch (error) {
      // ...
    }
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

分析与结论：
你的代码已经返回了 type 字段！这非常好。前端可以根据这个 type 的值（如 'M', 'Q', 'H', 'PM', 'PQ', 'PH'）来判断用户的具体套餐类型。

例如，前端拿到这个响应后，可以这样判断：

if (response.type.startsWith('P')) { // 是PRO用户 }

else if (response.type) { // 是标准用户 }

else { // 非会员 }

因此，这个接口后端部分也不需要修改。

总结与行动清单

你的后端代码架构良好，添加PRO套餐的核心工作量不大。

【必须】修改 PACKAGES 常量： 这是你唯一必须修改的代码。在 worker.js 文件中找到 PACKAGES 对象，并添加 PM, PQ, PH 三个PRO套餐的定义。


【推荐】升级 checkVip 函数： 为了未来的可扩展性，建议你采纳新的 checkVip 函数设计，增加 requiredTier 参数。这让你能轻松为PRO用户提供专属功能。

【无需修改】useCard 和 /api/user/quota： 这两部分代码已经足够健壮，可以自动适应新的套餐类型，无需改动。

完成以上步骤后，你的后端就已经完全支持PRO套餐了。前端可以根据 /api/user/quota 接口返回的 type 字段来展示不同的UI，并引导用户购买。