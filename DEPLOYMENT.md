# Cloudflare Pages 部署指南

## 📋 部署前检查清单

### ✅ 前端项目准备
- [x] Next.js 配置文件 (`next.config.js`) 已创建
- [x] 环境变量配置 (`.env.local`) 已设置
- [x] API 服务层已集成
- [x] 邮箱验证功能已实现

### ✅ 后端项目准备
- [x] Cloudflare Worker (`后端/worker.js`) 已部署
- [x] 腾讯云 SES 环境变量已配置
- [x] API 接口已测试

## 🚀 部署步骤

### 方法一：Git 连接部署（推荐）

1. **推送代码到Git仓库**
   ```bash
   git add .
   git commit -m "Ready for Cloudflare Pages deployment"
   git push origin main
   ```

2. **在Cloudflare Dashboard创建Pages项目**
   - 访问 https://dash.cloudflare.com
   - 进入 "Pages" → "Create a project"
   - 选择 "Connect to Git"
   - 选择你的仓库

3. **配置构建设置**
   ```
   Framework preset: Next.js
   Build command: npm run build
   Build output directory: out
   Root directory: /
   Environment variables:
     NEXT_PUBLIC_API_URL = https://your-worker.your-subdomain.workers.dev
   ```

### 方法二：手动上传部署

1. **本地构建**
   ```bash
   cd d:\ai-tts-workspace
   npm install
   npm run build
   ```

2. **上传构建文件**
   - 在Cloudflare Pages选择 "Upload assets"
   - 上传 `out/` 目录中的所有文件

## ⚙️ 环境变量配置

在Cloudflare Pages项目设置中添加：

```
NEXT_PUBLIC_API_URL=https://your-worker.your-subdomain.workers.dev
```

**注意：** 替换为你实际的Cloudflare Worker域名

## 🔧 构建配置详情

### Next.js 配置 (`next.config.js`)
```javascript
output: 'export'           // 静态导出
images: { unoptimized: true }  // 禁用图片优化
trailingSlash: true        // URL末尾斜杠
```

### 构建命令
```bash
npm run build              # 构建项目
```

### 输出目录
```
out/                       # 静态文件输出目录
```

## 🌐 域名配置

### 自定义域名（可选）
1. 在Cloudflare Pages项目中点击 "Custom domains"
2. 添加你的域名
3. 配置DNS记录

### SSL证书
- Cloudflare自动提供SSL证书
- 支持HTTP/2和HTTP/3

## 🔍 部署后验证

### 功能测试清单
- [ ] 页面正常加载
- [ ] 登录功能正常
- [ ] 注册功能正常
- [ ] 邮箱验证功能正常
- [ ] API调用正常
- [ ] 响应式设计正常

### 常见问题排查

**1. API调用失败**
- 检查 `NEXT_PUBLIC_API_URL` 环境变量
- 确认Cloudflare Worker已部署
- 检查CORS配置

**2. 页面404错误**
- 确认 `next.config.js` 中 `output: 'export'` 配置
- 检查构建输出目录是否正确

**3. 样式丢失**
- 确认 `images: { unoptimized: true }` 配置
- 检查静态资源路径

## 📊 性能优化

### Cloudflare Pages优势
- 全球CDN加速
- 自动缓存优化
- 边缘计算支持
- 无服务器架构

### 建议配置
- 启用Brotli压缩
- 配置缓存策略
- 使用Cloudflare Analytics

## 🔄 更新部署

### Git连接项目
```bash
git add .
git commit -m "Update features"
git push origin main
```
自动触发重新部署

### 手动上传项目
1. 本地重新构建
2. 上传新的构建文件

## 📞 技术支持

如遇到部署问题，可以：
1. 查看Cloudflare Pages构建日志
2. 检查浏览器开发者工具
3. 验证环境变量配置
4. 确认API接口连通性
