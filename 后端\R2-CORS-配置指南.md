# R2 CORS配置指南

## 🚨 问题描述

R2直链下载失败，控制台显示CORS错误：
```
Access to fetch at 'https://r2-assets.aispeak.top/audios/xxx.mp3' 
from origin 'http://localhost:3000' has been blocked by CORS policy: 
No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

## 🎯 解决方案

### 方案1：配置R2自定义域名CORS（推荐）

#### 步骤1：登录Cloudflare Dashboard
1. 访问 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 选择您的账户和域名

#### 步骤2：进入R2存储桶设置
1. 点击左侧菜单 "R2 Object Storage"
2. 选择您的存储桶（应该是 "audios"）
3. 点击 "Settings" 标签

#### 步骤3：配置CORS规则
在CORS配置区域添加以下规则：

```json
[
  {
    "AllowedOrigins": [
      "https://ttsapi.aispeak.top",
      "http://localhost:3000",
      "https://localhost:3000",
      "https://your-frontend-domain.com"
    ],
    "AllowedMethods": ["GET", "HEAD"],
    "AllowedHeaders": ["*"],
    "ExposeHeaders": ["Content-Length", "Content-Type", "Content-Range"],
    "MaxAgeSeconds": 3600
  }
]
```

#### 步骤4：应用配置
1. 点击 "Save" 保存配置
2. 等待几分钟让配置生效

### 方案2：通过自定义域名配置CORS

如果R2存储桶没有直接的CORS配置选项，可以通过自定义域名配置：

#### 步骤1：创建Worker脚本
创建一个新的Cloudflare Worker，添加CORS头：

```javascript
export default {
  async fetch(request, env) {
    const url = new URL(request.url)
    
    // 构建R2存储桶URL
    const r2Url = `https://your-r2-bucket-url${url.pathname}`
    
    // 转发请求到R2
    const response = await fetch(r2Url, {
      method: request.method,
      headers: request.headers,
      body: request.body
    })
    
    // 添加CORS头
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': '*',
      'Access-Control-Max-Age': '86400',
    }
    
    // 处理预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: corsHeaders
      })
    }
    
    // 返回带CORS头的响应
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        ...response.headers,
        ...corsHeaders
      }
    })
  }
}
```

#### 步骤2：绑定自定义域名
将 `r2-assets.aispeak.top` 绑定到这个Worker

### 方案3：临时解决方案（开发环境）

如果暂时无法配置CORS，可以：

#### 选项1：使用浏览器禁用CORS（仅开发）
启动Chrome时添加参数：
```bash
chrome.exe --user-data-dir="C:/Chrome dev session" --disable-web-security --disable-features=VizDisplayCompositor
```

#### 选项2：使用代理服务器
在开发环境中配置代理，将R2请求转发到本地代理。

## 🧪 测试CORS配置

### 测试脚本
```javascript
// 在浏览器控制台中运行
fetch('https://r2-assets.aispeak.top/audios/test.mp3', {
  method: 'HEAD',
  mode: 'cors'
})
.then(response => {
  console.log('✅ CORS配置成功:', response.status)
  console.log('响应头:', [...response.headers.entries()])
})
.catch(error => {
  console.error('❌ CORS配置失败:', error)
})
```

### 验证步骤
1. 配置CORS后等待5-10分钟
2. 在浏览器控制台运行测试脚本
3. 检查是否返回成功响应
4. 重新测试TTS生成功能

## 📋 配置检查清单

- [ ] R2存储桶CORS规则已添加
- [ ] 包含了所有需要的域名（localhost、生产域名）
- [ ] 允许了GET和HEAD方法
- [ ] 配置已保存并等待生效
- [ ] 使用测试脚本验证CORS
- [ ] TTS功能测试通过

## 🎯 预期结果

配置成功后，控制台应该显示：
```
[TTS-POLLING] 🚀 Attempting R2 direct link download
[TTS-DOWNLOAD] 🚀 Using R2 direct link download: https://r2-assets.aispeak.top/audios/xxx.mp3
[TTS-DOWNLOAD] ✅ R2 direct download completed: {
  url: "https://r2-assets.aispeak.top/audios/xxx.mp3",
  size: "X.XX MB",
  downloadMethod: "R2_DIRECT_LINK"
}
```

## 🚨 注意事项

1. **安全性**：生产环境不要使用 `"*"` 作为AllowedOrigins
2. **缓存**：CORS配置可能需要几分钟才能生效
3. **测试**：配置后务必在不同环境中测试
4. **回退**：即使CORS失败，系统会自动回退到Worker代理下载

## 📞 技术支持

如果配置过程中遇到问题：
1. 检查Cloudflare Dashboard中的CORS配置
2. 使用浏览器开发者工具查看网络请求
3. 查看控制台错误信息
4. 确认R2存储桶和自定义域名配置正确
