# 后端认证错误处理优化说明

## 问题描述
前端在处理token过期错误时，无法正确识别后端返回的"Token expired"错误消息，导致会话过期时显示"请检查网络连接"而不是"会话已过期，正在跳转到登录页面"。

## 解决方案
采用文档建议的方案二：改进后端，发送更结构化的错误信息，增加错误码字段便于前端统一识别。

## 修改内容

### 1. 新增统一的认证错误处理函数

```javascript
// 统一的认证错误处理函数
function createAuthErrorResponse(error) {
  let errorCode = 'AUTH_ERROR';
  let errorMessage = error.message;

  if (error.message === 'Token expired') {
    errorCode = 'TOKEN_EXPIRED';
  } else if (error.message === 'Invalid token' || error.message === 'Invalid signature') {
    errorCode = 'TOKEN_INVALID';
  } else if (error.message === 'Invalid token type') {
    errorCode = 'TOKEN_TYPE_INVALID';
  }

  return new Response(JSON.stringify({
    error: errorMessage,
    code: errorCode // 新增错误码字段
  }), {
    status: 401,
    headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
  });
}

// 判断是否为认证相关错误的辅助函数
function isAuthError(error) {
  return error.message === 'Token expired' || 
         error.message === 'Invalid token' || 
         error.message === 'Invalid signature' ||
         error.message === 'Invalid token type';
}
```

### 2. 修改的接口

#### `/api/user/quota` 接口
- **修改前**: 返回 `{"error": "Token expired"}`
- **修改后**: 返回 `{"error": "Token expired", "code": "TOKEN_EXPIRED"}`

#### `/api/card/use` 接口
- **修改前**: 认证错误返回 `{"error": "Token expired"}`
- **修改后**: 认证错误返回 `{"error": "Token expired", "code": "TOKEN_EXPIRED"}`
- **保持**: 业务错误（如卡密无效）仍返回400状态码

#### `/api/tts/*` 路由
- **修改前**: 认证错误返回 `{"error": "Unauthorized"}`
- **修改后**: 认证错误返回结构化错误信息

#### `/api/auth/change-password` 接口
- **修改前**: 认证错误返回 `{"error": "登录已过期，请重新登录"}`
- **修改后**: 使用统一的认证错误处理函数

### 3. 错误码映射

| 错误消息 | 错误码 | 说明 |
|---------|--------|------|
| Token expired | TOKEN_EXPIRED | Token已过期 |
| Invalid token | TOKEN_INVALID | Token无效 |
| Invalid signature | TOKEN_INVALID | Token签名无效 |
| Invalid token type | TOKEN_TYPE_INVALID | Token类型无效 |
| 其他认证错误 | AUTH_ERROR | 通用认证错误 |

## 兼容性保证

1. **保持HTTP状态码不变**: 所有认证错误仍返回401状态码
2. **保持错误消息不变**: `error`字段内容保持原样
3. **新增错误码字段**: 添加`code`字段，不影响现有前端逻辑
4. **业务错误不变**: 非认证错误（如卡密无效）保持原有状态码和格式

## 前端适配建议

前端可以通过以下方式识别认证错误：

```typescript
// 方式1: 检查错误码（推荐）
if (error.code === 'TOKEN_EXPIRED' || error.code === 'TOKEN_INVALID') {
  // 处理认证过期
}

// 方式2: 检查错误消息（兼容现有逻辑）
if (error.message?.includes('Token expired')) {
  // 处理认证过期
}
```

## 测试验证

建议测试以下场景：
1. 使用过期token访问 `/api/user/quota`
2. 使用无效token访问 `/api/card/use`
3. 使用过期token修改密码
4. 确保业务错误（如卡密无效）仍正常返回400状态码
