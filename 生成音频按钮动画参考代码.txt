"use client"

import { useState } from "react"

export default function Component() {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-900">
      <button
        className="relative overflow-hidden rounded-2xl px-8 py-4 text-white font-semibold text-lg transition-transform duration-300 hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-400/50"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        style={{
          background: "linear-gradient(45deg, #1e3a8a, #3b82f6, #06b6d4, #10b981, #8b5cf6)",
          backgroundSize: "300% 300%",
        }}
      >
        {/* Aurora Background Layers */}
        <div className="absolute inset-0 aurora-layer-1"></div>
        <div className="absolute inset-0 aurora-layer-2"></div>
        <div className="absolute inset-0 aurora-layer-3"></div>
        <div className="absolute inset-0 aurora-layer-4"></div>

        {/* Button Content */}
        <span className="relative z-10 drop-shadow-lg">Aurora Button</span>

        {/* Hover Overlay */}
        <div
          className={`absolute inset-0 bg-white/10 transition-opacity duration-300 ${
            isHovered ? "opacity-100" : "opacity-0"
          }`}
        ></div>
      </button>

      <style jsx>{`
        .aurora-layer-1 {
          background: linear-gradient(
            45deg,
            rgba(59, 130, 246, 0.8),
            rgba(16, 185, 129, 0.6),
            rgba(139, 92, 246, 0.7),
            rgba(6, 182, 212, 0.5)
          );
          background-size: 400% 400%;
          animation: aurora-flow-1 8s ease-in-out infinite;
        }
        
        .aurora-layer-2 {
          background: linear-gradient(
            -45deg,
            rgba(16, 185, 129, 0.6),
            rgba(139, 92, 246, 0.4),
            rgba(59, 130, 246, 0.7),
            rgba(236, 72, 153, 0.5)
          );
          background-size: 350% 350%;
          animation: aurora-flow-2 12s ease-in-out infinite reverse;
        }
        
        .aurora-layer-3 {
          background: linear-gradient(
            90deg,
            rgba(6, 182, 212, 0.5),
            rgba(139, 92, 246, 0.6),
            rgba(16, 185, 129, 0.4),
            rgba(59, 130, 246, 0.7)
          );
          background-size: 300% 300%;
          animation: aurora-flow-3 10s ease-in-out infinite;
        }
        
        .aurora-layer-4 {
          background: radial-gradient(
            ellipse at center,
            rgba(139, 92, 246, 0.3) 0%,
            rgba(6, 182, 212, 0.4) 25%,
            rgba(16, 185, 129, 0.3) 50%,
            rgba(59, 130, 246, 0.2) 75%,
            transparent 100%
          );
          background-size: 200% 200%;
          animation: aurora-pulse 6s ease-in-out infinite alternate;
        }
        
        @keyframes aurora-flow-1 {
          0%, 100% {
            background-position: 0% 50%;
            transform: translateX(0) scale(1);
          }
          25% {
            background-position: 100% 0%;
            transform: translateX(2px) scale(1.02);
          }
          50% {
            background-position: 100% 100%;
            transform: translateX(0) scale(1);
          }
          75% {
            background-position: 0% 100%;
            transform: translateX(-2px) scale(0.98);
          }
        }
        
        @keyframes aurora-flow-2 {
          0%, 100% {
            background-position: 100% 0%;
            opacity: 0.8;
          }
          33% {
            background-position: 0% 100%;
            opacity: 0.6;
          }
          66% {
            background-position: 100% 100%;
            opacity: 0.9;
          }
        }
        
        @keyframes aurora-flow-3 {
          0%, 100% {
            background-position: 0% 0%;
            transform: rotate(0deg);
          }
          50% {
            background-position: 100% 100%;
            transform: rotate(1deg);
          }
        }
        
        @keyframes aurora-pulse {
          0% {
            background-position: 0% 0%;
            opacity: 0.4;
            transform: scale(1);
          }
          50% {
            background-position: 100% 100%;
            opacity: 0.8;
            transform: scale(1.05);
          }
          100% {
            background-position: 0% 0%;
            opacity: 0.4;
            transform: scale(1);
          }
        }
      `}</style>
    </div>
  )
}
