@echo off
echo ========================================
echo    清理构建缓存并重新构建
echo ========================================
echo.

echo [1/4] 清理 .next 目录...
if exist .next (
    rmdir /s /q .next
    echo .next 目录已清理
) else (
    echo .next 目录不存在，跳过
)

echo [2/4] 清理 out 目录...
if exist out (
    rmdir /s /q out
    echo out 目录已清理
) else (
    echo out 目录不存在，跳过
)

echo [3/4] 检查环境变量...
if not exist .env.local (
    echo 警告: .env.local 文件不存在
    echo 请确保配置了 NEXT_PUBLIC_API_URL
)

echo [4/4] 重新构建项目...
echo 开始构建，这可能需要几分钟...
call npm run build

if %errorlevel% equ 0 (
    echo.
    echo ✅ 构建成功！
    echo 构建文件位于: out/ 目录
    echo.
    echo 文件统计:
    dir out /s /-c | find "个文件"
    echo.
    echo 现在可以部署到 Cloudflare Pages
) else (
    echo.
    echo ❌ 构建失败，请检查错误信息
    echo.
    echo 常见解决方案:
    echo 1. 检查 .env.local 文件是否存在
    echo 2. 确保所有依赖已安装: npm install
    echo 3. 检查代码语法错误
)

echo.
pause
