# 前端错误处理优化总结

## 修改概述

为了配合后端新增的结构化错误响应（包含错误码），前端进行了以下同步修改：

## ✅ 已完成的修改

### 1. API客户端增强 (`lib/api.ts`)

**修改内容**：
- `handleResponse` 方法现在提取并保存后端返回的 `code` 字段
- 创建的错误对象包含错误码信息，便于后续识别

**修改前**：
```typescript
throw new Error(errorMessage)
```

**修改后**：
```typescript
const error = new Error(errorMessage)
if (errorCode) {
  (error as any).code = errorCode
}
throw error
```

### 2. 认证服务优化 (`lib/auth-service.ts`)

**修改内容**：
- 新增 `isAuthError` 私有方法，优先检查错误码，兼容错误消息
- `withTokenRefresh` 方法使用新的认证错误判断逻辑

**错误识别优先级**：
1. **错误码检查**（最可靠）：`TOKEN_EXPIRED`, `TOKEN_INVALID`, `TOKEN_TYPE_INVALID`, `NO_TOKEN`
2. **错误消息检查**（兼容性）：包含 `token`, `expired`, `401`, `登录`, `unauthorized`

### 3. 统一错误处理工具 (`lib/error-utils.ts`)

**新增文件**，提供：
- 错误码常量定义
- `isAuthError()` - 判断认证错误
- `isTokenExpiredError()` - 判断token过期
- `getErrorUIState()` - 为UI组件提供错误状态
- `handleAuthError()` - 统一认证错误处理

### 4. 充值页面更新 (`app/recharge/page.tsx`)

**修改内容**：
- 导入并使用 `getErrorUIState` 函数
- 替换原有的字符串匹配逻辑
- 统一错误提示的显示格式

**修改前**：
```typescript
if (error.message?.includes('登录') ||
    error.message?.includes('refresh') ||
    error.message?.includes('401') ||
    error.message?.includes('No refresh token available')) {
  // 手动设置错误信息
}
```

**修改后**：
```typescript
const errorUI = getErrorUIState(error)
toast({
  title: errorUI.toastTitle,
  description: errorUI.toastDescription,
  variant: errorUI.variant,
})
```

## 🔄 兼容性保证

### 向后兼容
- 所有修改都保持向后兼容
- 错误码检查优先，错误消息检查作为fallback
- 现有的错误处理逻辑不会被破坏

### 渐进式增强
- 新的错误码机制提供更可靠的错误识别
- 旧的字符串匹配机制作为兼容性保障
- 可以逐步迁移其他页面使用新的错误处理工具

## 📋 错误处理映射

| 后端错误码 | 前端识别 | UI显示 |
|-----------|---------|--------|
| `TOKEN_EXPIRED` | ✅ 优先识别 | "会话已过期，正在跳转到登录页面..." |
| `TOKEN_INVALID` | ✅ 优先识别 | "会话已过期，正在跳转到登录页面..." |
| `NO_TOKEN` | ✅ 优先识别 | "会话已过期，正在跳转到登录页面..." |
| 包含"卡密"的错误 | ✅ 业务错误 | "卡密无效或已使用，请检查后重试" |
| 其他错误 | ✅ 通用错误 | "请检查网络连接后重试" |

## 🎯 解决的问题

1. **核心问题**：token过期时显示"请检查网络连接"而不是"会话已过期"
2. **识别准确性**：通过错误码提供更可靠的错误类型识别
3. **代码复用**：统一的错误处理逻辑，减少重复代码
4. **用户体验**：更准确的错误提示和处理流程

## 🚀 使用示例

### 在新页面中使用统一错误处理

```typescript
import { getErrorUIState } from "@/lib/error-utils"

try {
  // API调用
} catch (error: any) {
  const errorUI = getErrorUIState(error)
  
  toast({
    title: errorUI.toastTitle,
    description: errorUI.toastDescription,
    variant: errorUI.variant,
  })
}
```

### 检查特定错误类型

```typescript
import { isAuthError, isTokenExpiredError } from "@/lib/error-utils"

if (isAuthError(error)) {
  // 处理认证错误
  router.push('/login')
}

if (isTokenExpiredError(error)) {
  // 处理token过期
  showTokenExpiredDialog()
}
```

## 📝 后续建议

1. **逐步迁移**：其他页面可以逐步采用新的错误处理工具
2. **测试验证**：建议测试各种错误场景确保正常工作
3. **文档更新**：更新开发文档说明新的错误处理规范
