// API配置和服务层
// 兼容 Cloudflare Pages 部署

// API配置
const API_CONFIG = {
  // 生产环境使用你的 Cloudflare Worker 域名
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'https://tk1688-api.aispeak.top',
  TIMEOUT: 120000, // 2分钟超时
  // 【新增】备用API地址配置
  BACKUP_URLS: process.env.NEXT_PUBLIC_BACKUP_API_URLS?.split(',').filter(Boolean) || [
    'https://tk1688-api.aispeak.top',
    'https://tk1688-api.aispeak.top'
  ],
  // 【新增】API切换失败阈值
  SWITCH_THRESHOLD: parseInt(process.env.NEXT_PUBLIC_API_SWITCH_THRESHOLD || '3'),
}

// API端点定义
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    REFRESH: '/api/auth/refresh',
    SEND_VERIFICATION: '/api/auth/send-verification',
    VERIFY_EMAIL: '/api/auth/verify-email',
    CHANGE_PASSWORD: '/api/auth/change-password',
    FORGOT_PASSWORD: '/api/auth/forgot-password',
    RESET_PASSWORD: '/api/auth/reset-password',
  },
  // TTS相关
  TTS: {
    GENERATE: '/api/tts/generate',
    STATUS: '/api/tts/status',
    DOWNLOAD: '/api/tts/download',
  },
  // 用户相关
  USER: {
    QUOTA: '/api/user/quota',
  },
  // 卡密相关
  CARD: {
    USE: '/api/card/use',
  },
}

// 请求类型定义
export interface ApiResponse<T = any> {
  success?: boolean
  data?: T
  error?: string
  message?: string
}

export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  access_token: string
  refresh_token: string
  expires_in: number
}

export interface RegisterRequest {
  username: string
  password: string
}

export interface SendVerificationRequest {
  email: string
  username: string
  password: string
}

export interface SendVerificationResponse {
  message: string
  email: string
}

export interface VerifyEmailRequest {
  username: string
  email: string
  code: string
}

export interface VerifyEmailResponse {
  message: string
  access_token: string
  refresh_token: string
  expires_in: number
}

export interface UserQuotaResponse {
  // 原有字段（保持向后兼容）
  isVip: boolean
  expireAt: number
  type?: string
  remainingTime?: string | null

  // 新增配额相关字段
  quotaChars?: number      // 总配额（老用户为undefined）
  usedChars?: number       // 已用配额（老用户为undefined）
  remainingChars?: number  // 剩余配额（老用户为undefined）
  usagePercentage: number  // 使用百分比
  isLegacyUser: boolean    // 是否为老用户
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}

export interface ChangePasswordResponse {
  message: string
}

export interface ForgotPasswordRequest {
  email: string
}

export interface ForgotPasswordResponse {
  message: string
  email: string
}

export interface ResetPasswordRequest {
  email: string
  code: string
  newPassword: string
}

export interface ResetPasswordResponse {
  message: string
}

// TTS异步任务相关类型
export interface TTSTaskResponse {
  taskId: string
  status: 'processing' | 'complete' | 'failed'
  message?: string
}

export interface TTSStatusResponse {
  taskId: string
  status: 'processing' | 'complete' | 'failed'
  createdAt?: number
  completedAt?: number
  audioUrl?: string
  audioSize?: number
  chunksProcessed?: number
  totalChunks?: number
  error?: string
}

export interface TTSGenerateRequest {
  input: string
  voice: string
  stability?: number
  similarity_boost?: number
  style?: number
  speed?: number
}

// 【新增】API管理器 - 处理多API地址切换
export class ApiManager {
  private static instance: ApiManager
  private currentApiIndex: number = 0
  private apiUrls: string[]
  private failureCounts: Map<string, number> = new Map()
  private switchThreshold: number
  private lastSwitchTime: number = 0
  private readonly SWITCH_COOLDOWN = 30000 // 30秒切换冷却时间

  private constructor() {
    this.apiUrls = [API_CONFIG.BASE_URL, ...API_CONFIG.BACKUP_URLS]
    this.switchThreshold = API_CONFIG.SWITCH_THRESHOLD

    // 初始化失败计数
    this.apiUrls.forEach(url => {
      this.failureCounts.set(url, 0)
    })
  }

  static getInstance(): ApiManager {
    if (!ApiManager.instance) {
      ApiManager.instance = new ApiManager()
    }
    return ApiManager.instance
  }

  getCurrentApiUrl(): string {
    return this.apiUrls[this.currentApiIndex] || API_CONFIG.BASE_URL
  }

  getCurrentApiIndex(): number {
    return this.currentApiIndex
  }

  getAllApiUrls(): string[] {
    return [...this.apiUrls]
  }

  recordFailure(apiUrl: string): boolean {
    const count = (this.failureCounts.get(apiUrl) || 0) + 1
    this.failureCounts.set(apiUrl, count)

    if (count >= this.switchThreshold) {
      return this.switchToNextApi()
    }
    return false
  }

  private switchToNextApi(): boolean {
    const now = Date.now()

    // 检查切换冷却时间
    if (now - this.lastSwitchTime < this.SWITCH_COOLDOWN) {
      return false
    }

    if (this.currentApiIndex < this.apiUrls.length - 1) {
      this.currentApiIndex++
      this.lastSwitchTime = now
      return true
    }

    return false
  }

  recordSuccess(apiUrl: string) {
    this.failureCounts.set(apiUrl, 0)
  }

  resetToFirstApi() {
    if (this.currentApiIndex !== 0) {
      this.currentApiIndex = 0
    }

    // 重置所有失败计数
    this.apiUrls.forEach(url => {
      this.failureCounts.set(url, 0)
    })
  }

  getFailureCount(apiUrl: string): number {
    return this.failureCounts.get(apiUrl) || 0
  }

  getStatus() {
    return {
      currentApi: this.getCurrentApiUrl(),
      currentIndex: this.currentApiIndex,
      totalApis: this.apiUrls.length,
      failureCounts: Object.fromEntries(this.failureCounts),
      lastSwitchTime: this.lastSwitchTime
    }
  }
}

// Token管理
export class TokenManager {
  private static readonly ACCESS_TOKEN_KEY = 'access_token'
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token'
  private static readonly USER_EMAIL_KEY = 'userEmail'

  static getAccessToken(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem(this.ACCESS_TOKEN_KEY)
  }

  static getRefreshToken(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem(this.REFRESH_TOKEN_KEY)
  }

  static getUserEmail(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem(this.USER_EMAIL_KEY)
  }

  static setTokens(accessToken: string, refreshToken: string, email?: string): void {
    if (typeof window === 'undefined') return
    localStorage.setItem(this.ACCESS_TOKEN_KEY, accessToken)
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken)
    if (email) {
      localStorage.setItem(this.USER_EMAIL_KEY, email)
    }
    // 保持兼容性
    localStorage.setItem('isLoggedIn', 'true')
  }

  static clearTokens(): void {
    if (typeof window === 'undefined') return
    localStorage.removeItem(this.ACCESS_TOKEN_KEY)
    localStorage.removeItem(this.REFRESH_TOKEN_KEY)
    localStorage.removeItem(this.USER_EMAIL_KEY)
    // 保持兼容性
    localStorage.removeItem('isLoggedIn')
  }

  static isLoggedIn(): boolean {
    return !!this.getAccessToken()
  }
}

// HTTP客户端类
export class ApiClient {
  private timeout: number
  private apiManager: ApiManager

  constructor() {
    this.timeout = API_CONFIG.TIMEOUT
    this.apiManager = ApiManager.getInstance()
  }



  // 创建请求头
  private createHeaders(includeAuth: boolean = false): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    }

    if (includeAuth) {
      const token = TokenManager.getAccessToken()
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
    }

    return headers
  }

  // 处理响应
  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`
      let errorCode: string | null = null

      try {
        const errorData = await response.json()
        errorMessage = errorData.error || errorData.message || errorMessage
        errorCode = errorData.code || null // 【新增】提取错误码
      } catch {
        // 如果无法解析JSON，使用默认错误消息
      }

      // 【新增】创建带有错误码的自定义错误对象
      const error = new Error(errorMessage)
      if (errorCode) {
        (error as any).code = errorCode
      }

      throw error
    }

    return await response.json()
  }

  // 【增强】通用请求方法 - 支持API自动切换
  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    includeAuth: boolean = false
  ): Promise<T> {
    const currentApiUrl = this.apiManager.getCurrentApiUrl()
    const url = `${currentApiUrl}${endpoint}`
    const headers = this.createHeaders(includeAuth)

    const config: RequestInit = {
      ...options,
      headers: {
        ...headers,
        ...options.headers,
      },
    }

    // 创建AbortController用于超时控制
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), this.timeout)

    try {
      const response = await fetch(url, {
        ...config,
        signal: controller.signal,
      })

      clearTimeout(timeoutId)

      // 【新增】请求成功时记录成功
      this.apiManager.recordSuccess(currentApiUrl)

      return await this.handleResponse<T>(response)
    } catch (error) {
      clearTimeout(timeoutId)

      // 【新增】请求失败时记录失败并尝试切换API
      const shouldSwitch = this.apiManager.recordFailure(currentApiUrl)

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('请求超时，请检查网络连接')
        }

        // 【新增】如果切换了API，在错误信息中提示
        if (shouldSwitch) {
          // 注意：这里不自动重试，让上层调用者决定是否重试
        }

        throw error
      }

      throw new Error('网络请求失败')
    }
  }

  // GET请求
  async get<T>(endpoint: string, includeAuth: boolean = false): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' }, includeAuth)
  }

  // POST请求
  async post<T>(
    endpoint: string,
    data?: any,
    includeAuth: boolean = false
  ): Promise<T> {
    return this.request<T>(
      endpoint,
      {
        method: 'POST',
        body: data ? JSON.stringify(data) : undefined,
      },
      includeAuth
    )
  }

  // PUT请求
  async put<T>(
    endpoint: string,
    data?: any,
    includeAuth: boolean = false
  ): Promise<T> {
    return this.request<T>(
      endpoint,
      {
        method: 'PUT',
        body: data ? JSON.stringify(data) : undefined,
      },
      includeAuth
    )
  }

  // DELETE请求
  async delete<T>(endpoint: string, includeAuth: boolean = false): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' }, includeAuth)
  }
}

// 创建全局API客户端实例
export const apiClient = new ApiClient()

// 【新增】创建全局API管理器实例
export const apiManager = ApiManager.getInstance()
