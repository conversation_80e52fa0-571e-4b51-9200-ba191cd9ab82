@echo off
echo ========================================
echo    构建文件检查脚本
echo ========================================
echo.

echo [1/4] 检查 out 目录是否存在...
if not exist out (
    echo ❌ out 目录不存在，请先运行 npm run build
    pause
    exit /b 1
)
echo ✅ out 目录存在

echo [2/4] 检查关键文件...
if exist out\index.html (
    echo ✅ index.html 存在
) else (
    echo ❌ index.html 不存在
)

if exist out\login\index.html (
    echo ✅ login/index.html 存在
) else (
    echo ❌ login/index.html 不存在
)

if exist out\register\index.html (
    echo ✅ register/index.html 存在
) else (
    echo ❌ register/index.html 不存在
)

if exist out\recharge\index.html (
    echo ✅ recharge/index.html 存在
) else (
    echo ❌ recharge/index.html 不存在
)

echo [3/4] 检查静态资源...
if exist out\_next (
    echo ✅ _next 静态资源目录存在
) else (
    echo ❌ _next 静态资源目录不存在
)

echo [4/4] 文件统计...
echo.
echo 📊 构建文件统计:
dir out /s /-c | find "个文件"
echo.

echo 📁 out 目录内容:
dir out /b
echo.

echo ========================================
echo    部署准备完成！
echo ========================================
echo.
echo 🚀 下一步操作:
echo 1. 访问 https://dash.cloudflare.com
echo 2. 进入 Pages → Create a project
echo 3. 选择 Upload assets
echo 4. 上传整个 out 文件夹的内容
echo 5. 配置环境变量: NEXT_PUBLIC_API_URL
echo.
echo 💡 提示: 直接拖拽 out 文件夹到上传区域即可
echo.
pause
