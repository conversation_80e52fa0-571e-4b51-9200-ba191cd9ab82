// 邮箱验证相关的React Hook
import { useState, useCallback, useEffect } from 'react'
import { auth } from '@/lib/auth-service'
import type { SendVerificationRequest, VerifyEmailRequest } from '@/lib/api'

interface UseEmailVerificationOptions {
  onSuccess?: () => void
  onError?: (error: string) => void
}

interface EmailVerificationState {
  // 发送验证码状态
  isSending: boolean
  sendError: string | null
  isCodeSent: boolean
  
  // 验证状态
  isVerifying: boolean
  verifyError: string | null
  
  // 倒计时状态
  countdown: number
  canResend: boolean
  
  // 临时存储的注册信息
  pendingRegistration: {
    email: string
    username: string
    password: string
  } | null
}

export function useEmailVerification(options: UseEmailVerificationOptions = {}) {
  const [state, setState] = useState<EmailVerificationState>({
    isSending: false,
    sendError: null,
    isCodeSent: false,
    isVerifying: false,
    verifyError: null,
    countdown: 0,
    canResend: true,
    pendingRegistration: null,
  })

  // 倒计时效果
  useEffect(() => {
    let timer: NodeJS.Timeout
    
    if (state.countdown > 0) {
      timer = setTimeout(() => {
        setState(prev => ({
          ...prev,
          countdown: prev.countdown - 1,
          canResend: prev.countdown <= 1,
        }))
      }, 1000)
    }

    return () => {
      if (timer) clearTimeout(timer)
    }
  }, [state.countdown])

  // 发送验证码
  const sendVerificationCode = useCallback(async (data: SendVerificationRequest) => {
    setState(prev => ({
      ...prev,
      isSending: true,
      sendError: null,
    }))

    try {
      const response = await auth.sendVerificationCode(data)
      
      setState(prev => ({
        ...prev,
        isSending: false,
        isCodeSent: true,
        countdown: 60, // 60秒倒计时
        canResend: false,
        pendingRegistration: {
          email: data.email,
          username: data.username,
          password: data.password,
        },
      }))

      return response
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '发送验证码失败'
      
      setState(prev => ({
        ...prev,
        isSending: false,
        sendError: errorMessage,
      }))

      options.onError?.(errorMessage)
      throw error
    }
  }, [options])

  // 重新发送验证码
  const resendVerificationCode = useCallback(async () => {
    if (!state.canResend || !state.pendingRegistration) {
      return
    }

    return await sendVerificationCode(state.pendingRegistration)
  }, [state.canResend, state.pendingRegistration, sendVerificationCode])

  // 验证邮箱并完成注册
  const verifyEmailAndRegister = useCallback(async (code: string) => {
    if (!state.pendingRegistration) {
      throw new Error('没有待验证的注册信息')
    }

    setState(prev => ({
      ...prev,
      isVerifying: true,
      verifyError: null,
    }))

    try {
      const verifyData: VerifyEmailRequest = {
        username: state.pendingRegistration.username,
        email: state.pendingRegistration.email,
        code: code.trim(),
      }

      const response = await auth.verifyEmailAndRegister(verifyData)
      
      setState(prev => ({
        ...prev,
        isVerifying: false,
        pendingRegistration: null,
      }))

      options.onSuccess?.()
      return response
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '验证失败'
      
      setState(prev => ({
        ...prev,
        isVerifying: false,
        verifyError: errorMessage,
      }))

      options.onError?.(errorMessage)
      throw error
    }
  }, [state.pendingRegistration, options])

  // 清除错误
  const clearErrors = useCallback(() => {
    setState(prev => ({
      ...prev,
      sendError: null,
      verifyError: null,
    }))
  }, [])

  // 重置状态
  const reset = useCallback(() => {
    setState({
      isSending: false,
      sendError: null,
      isCodeSent: false,
      isVerifying: false,
      verifyError: null,
      countdown: 0,
      canResend: true,
      pendingRegistration: null,
    })
  }, [])

  // 格式化倒计时显示
  const getCountdownText = useCallback(() => {
    if (state.countdown > 0) {
      return `${state.countdown}秒后可重新发送`
    }
    return '重新发送验证码'
  }, [state.countdown])

  return {
    // 状态
    ...state,
    
    // 方法
    sendVerificationCode,
    resendVerificationCode,
    verifyEmailAndRegister,
    clearErrors,
    reset,
    
    // 辅助方法
    getCountdownText,
    
    // 计算属性
    hasError: !!(state.sendError || state.verifyError),
    isLoading: state.isSending || state.isVerifying,
  }
}
