这个方案有两个关键部分：

准备工作：在您的 R2 存储桶中放置一个用于探测的空文件。

代码修改：用新的、高效的 performHealthCheck 函数替换掉您现有的版本。

第 1 步：准备工作 (只需做一次)

创建一个空文件：在您的本地电脑上，创建一个名为 health-probe.txt 的空文本文件。它的大小应该是 0 字节。

上传到 R2 存储桶：

登录您的 Cloudflare 账号。

进入 "R2" -> "存储桶"。

选择您用于存储音频的那个存储桶（在您的代码中绑定为 AUDIOS 的那个）。

点击 "上传" 按钮，将刚刚创建的 health-probe.txt 文件上传到存储桶的根目录下。

验证：上传后，您应该能在 R2 存储桶的文件列表中看到 health-probe.txt 这个文件。

这个文件将作为健康检查的“探测目标”，它会一直存在，我们只会读取它的元数据，不会修改或删除它。

第 2 步：代码修改

现在，请打开您的 worker.js 文件，找到 performHealthCheck 函数，并将其完全替换为以下版本。

找到您现有的 performHealthCheck 函数 (大约在 1258 行):

// (旧代码，将被替换)
async function performHealthCheck(env, taskId = 'health-check') {
  // ... [此处省略约 30 行的原有逻辑] ...
}


将其替换为下面这个新的、高效的版本：

/**
 * 轻量级、高效的系统健康检查。
 * 通过对一个预置的探测文件执行 head() 操作来验证 R2 连接。
 * @param {object} env - Cloudflare Worker 的环境对象
 * @returns {Promise<object>} - 健康检查结果
 */
async function performHealthCheck(env) {
  const healthMetrics = {
    timestamp: new Date().toISOString(),
    checks: {}
  };
  // 确保这个探测文件已预先上传到 R2 存储桶的根目录
  const probeFileName = 'health-probe.txt';

  // --- R2 连接健康检查 ---
  const r2StartTime = Date.now();
  try {
    const objectMetadata = await env.AUDIOS.head(probeFileName);

    if (objectMetadata === null) {
      // 如果能执行到这里但文件不存在，说明连接是通的，但配置有问题。
      // 这也应被视为一种“不健康”状态，因为它会导致后续操作失败。
      throw new Error(`Health probe file '${probeFileName}' not found in the R2 bucket. Please upload it.`);
    }

    // head() 操作成功，说明连接和权限都正常
    healthMetrics.checks.r2 = {
      status: 'healthy',
      responseTimeMs: Date.now() - r2StartTime
    };
    console.log(`[HEALTH-CHECK] ✅ R2 storage is healthy. Response time: ${healthMetrics.checks.r2.responseTimeMs}ms`);

  } catch (r2Error) {
    // 任何错误 (如网络问题、权限问题、文件不存在) 都表示不健康
    healthMetrics.checks.r2 = {
      status: 'unhealthy',
      error: r2Error.message,
      responseTimeMs: Date.now() - r2StartTime
    };
    console.error(`[HEALTH-CHECK] ❌ R2 storage is unhealthy:`, {
        error: r2Error.message,
        responseTime: healthMetrics.checks.r2.responseTimeMs
    });
  }
  
  // --- 其他健康检查 (如果未来需要添加) ---
  // 例如：检查 KV 存储连接等
  
  return healthMetrics;
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
重要：修改 processAudioAsync 中的调用

您的 processAudioAsync 函数在调用 performHealthCheck 时传递了 taskId。新版的 performHealthCheck 不再需要这个参数。请确保调用处也一并修改。

找到这行代码 (大约在 1288 行):

// (旧代码)
const healthCheck = await performHealthCheck(env, taskId);
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

修改为：

// (新代码)
const healthCheck = await performHealthCheck(env);
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
实施总结

手动上传一个名为 health-probe.txt 的空文件到您的 R2 存储桶根目录。

用上面提供的新代码替换您 worker.js 中的 performHealthCheck 函数。

移除 processAudioAsync 函数中对 performHealthCheck 调用时传递的 taskId 参数。

完成以上步骤后，您的系统就采纳了这个最佳实践。每个 TTS 任务启动时的健康检查将变得极其快速和低成本，同时解决了您之前分析出的所有相关问题。