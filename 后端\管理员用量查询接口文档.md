# 管理员用量查询接口文档

## 🎯 功能概述

新增管理员专用接口，用于获取所有用户的字符使用量数据，支持分页查询和性能优化。

## 🔐 环境变量配置

### 必需配置
在Cloudflare Workers环境变量中添加：

```bash
ADMIN_USERS=admin,superuser,manager
```

**说明**：
- 多个管理员用户名用逗号分隔
- 用户名前后的空格会自动去除
- 如果未配置此环境变量，接口将返回"管理员功能未配置"错误

## 📊 API接口详情

### 接口地址
```
GET /api/admin/users/usage
```

### 请求头
```
Authorization: Bearer <ADMIN_TOKEN>
```

### 查询参数
| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `limit` | number | 否 | 100 | 每页返回的用户数量，最大500 |
| `cursor` | string | 否 | null | 分页游标，用于获取下一页数据 |

### 请求示例
```bash
# 获取前100个用户的用量数据
curl -X GET "https://ttsapi.aispeak.top/api/admin/users/usage" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# 分页查询，每页50个用户
curl -X GET "https://ttsapi.aispeak.top/api/admin/users/usage?limit=50" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# 获取下一页数据
curl -X GET "https://ttsapi.aispeak.top/api/admin/users/usage?limit=50&cursor=NEXT_CURSOR" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

## 📋 响应格式

### 成功响应 (200)
```json
{
  "users": [
    {
      "username": "user1",
      "usage": {
        "totalChars": 1250,
        "monthlyChars": 450,
        "monthlyResetAt": 1735689600000
      },
      "createdAt": 1703123456789,
      "vip": {
        "expireAt": 1735689600000,
        "type": "S"
      }
    },
    {
      "username": "user2",
      "usage": {
        "totalChars": 2100,
        "monthlyChars": 800,
        "monthlyResetAt": 1735689600000
      },
      "createdAt": 1703123456789,
      "vip": {
        "expireAt": 0,
        "type": null
      }
    }
  ],
  "pagination": {
    "limit": 100,
    "hasMore": true,
    "cursor": "eyJrZXkiOiJ1c2VyOnVzZXIxMDAifQ==",
    "total": 2
  },
  "timestamp": 1703123456789
}
```

### 错误响应

#### 401 - 未授权
```json
{
  "error": "Unauthorized",
  "code": "NO_TOKEN"
}
```

#### 403 - 权限不足
```json
{
  "error": "需要管理员权限",
  "code": "ADMIN_PERMISSION_DENIED"
}
```

#### 403 - 管理员功能未配置
```json
{
  "error": "管理员功能未配置",
  "code": "ADMIN_PERMISSION_DENIED"
}
```

#### 500 - 服务器错误
```json
{
  "error": "服务器内部错误",
  "code": "INTERNAL_ERROR"
}
```

## 🔧 数据字段说明

### 用户数据结构
| 字段 | 类型 | 说明 |
|------|------|------|
| `username` | string | 用户名 |
| `usage.totalChars` | number | 历史总字符数 |
| `usage.monthlyChars` | number | 当月已用字符数 |
| `usage.monthlyResetAt` | number | 下次月度重置时间戳 |
| `createdAt` | number | 用户注册时间戳 |
| `vip.expireAt` | number | VIP过期时间戳 |
| `vip.type` | string\|null | VIP类型 (S/P/T/null) |

### 分页信息
| 字段 | 类型 | 说明 |
|------|------|------|
| `limit` | number | 当前页大小 |
| `hasMore` | boolean | 是否还有更多数据 |
| `cursor` | string\|null | 下一页游标 |
| `total` | number | 当前页返回的用户数量 |

## ⚡ 性能特性

### 分页机制
- **默认分页**：每页100个用户
- **最大限制**：每页最多500个用户
- **游标分页**：使用Cloudflare KV的cursor机制，支持大数据量

### 并发优化
- **并行处理**：使用Promise.all并行获取用户数据
- **错误隔离**：单个用户数据获取失败不影响其他用户
- **内存优化**：流式处理，不会一次性加载所有用户数据

### 月度重置优化
- **只读重置**：查询时检测月度重置但不写回KV，避免大量写操作
- **数据一致性**：返回重置后的正确数据

## 🛡️ 安全特性

### 权限验证
1. **Token验证**：验证JWT token有效性
2. **管理员检查**：验证用户是否在ADMIN_USERS列表中
3. **环境变量保护**：管理员列表存储在环境变量中

### 访问控制
- **最小权限原则**：只返回必要的用户数据
- **敏感信息过滤**：不返回密码hash等敏感信息
- **操作日志**：记录管理员访问日志

## 📈 使用场景

### 数据分析后端集成
```javascript
// 示例：获取所有用户用量数据
async function fetchAllUsersUsage() {
  let allUsers = [];
  let cursor = null;
  let hasMore = true;
  
  while (hasMore) {
    const url = cursor 
      ? `/api/admin/users/usage?limit=500&cursor=${cursor}`
      : '/api/admin/users/usage?limit=500';
      
    const response = await fetch(url, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    
    const data = await response.json();
    allUsers.push(...data.users);
    
    hasMore = data.pagination.hasMore;
    cursor = data.pagination.cursor;
  }
  
  return allUsers;
}
```

### 统计分析
```javascript
// 示例：计算用量统计
function calculateUsageStats(users) {
  const stats = {
    totalUsers: users.length,
    totalChars: users.reduce((sum, u) => sum + u.usage.totalChars, 0),
    monthlyChars: users.reduce((sum, u) => sum + u.usage.monthlyChars, 0),
    vipUsers: users.filter(u => u.vip.expireAt > Date.now()).length
  };
  
  return stats;
}
```

## 🚀 部署说明

### 环境变量设置
1. 在Cloudflare Workers控制台中设置环境变量
2. 添加 `ADMIN_USERS` 变量，值为管理员用户名列表
3. 重新部署Worker

### 测试验证
1. 使用管理员账号登录获取token
2. 调用接口验证权限和数据返回
3. 测试分页功能和错误处理

### 监控要点
- 关注 `[ADMIN-CHECK]` 和 `[ADMIN-API]` 日志
- 监控接口响应时间和成功率
- 检查大数据量查询的性能表现

## 🔄 与现有系统的兼容性

### 不影响现有功能
- ✅ 完全独立的新接口，不修改现有逻辑
- ✅ 只读操作，不影响用户数据
- ✅ 错误隔离，接口异常不影响TTS功能

### 向后兼容
- ✅ 自动处理老用户的usage字段缺失
- ✅ 兼容不同的用户数据结构版本
- ✅ 优雅处理数据异常情况
