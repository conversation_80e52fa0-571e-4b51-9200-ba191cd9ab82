"use client"

import React, { useState, useEffect, useCallback } from 'react';
import { Search, Clock, List, X, Download, RefreshCw, AlertCircle, Copy, Check } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";

interface Task {
  taskId: string;
  createdAt: number;
  status?: 'processing' | 'complete' | 'failed' | 'unknown';
  downloadUrl?: string;
  isRefreshing?: boolean;
}

interface TaskCenterProps {
  className?: string;
}

export interface TaskCenterRef {
  addTask: (taskId: string) => void;
  updateTaskStatus: (taskId: string, status: 'processing' | 'complete' | 'failed', downloadUrl?: string) => void;
}

const TaskCenter = React.forwardRef<TaskCenterRef, TaskCenterProps>(({ className = "" }, ref) => {
  const [showTaskCenter, setShowTaskCenter] = useState(false);
  const [taskList, setTaskList] = useState<Task[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredTasks, setFilteredTasks] = useState<Task[]>([]);
  const [copiedTaskId, setCopiedTaskId] = useState<string | null>(null);

  // 本地存储键名
  const STORAGE_KEY = 'tts_task_center_tasks';

  // 从本地存储加载任务列表
  useEffect(() => {
    try {
      const savedTasks = localStorage.getItem(STORAGE_KEY);
      if (savedTasks) {
        const tasks = JSON.parse(savedTasks);
        setTaskList(tasks);
      }
    } catch (error) {
      console.error('Failed to load tasks from localStorage:', error);
    }
  }, []);

  // 保存任务列表到本地存储
  const saveTasksToStorage = useCallback((tasks: Task[]) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(tasks));
    } catch (error) {
      console.error('Failed to save tasks to localStorage:', error);
    }
  }, []);

  // 添加新任务
  const addTask = useCallback((taskId: string) => {
    const newTask: Task = {
      taskId,
      createdAt: Date.now()
    };

    setTaskList(prev => {
      // 检查是否已存在相同的taskId，避免重复添加
      if (prev.some(task => task.taskId === taskId)) {
        return prev;
      }
      
      const updatedTasks = [newTask, ...prev];
      // 限制最多保存100个任务，避免存储过多数据
      const limitedTasks = updatedTasks.slice(0, 100);
      saveTasksToStorage(limitedTasks);
      return limitedTasks;
    });
  }, [saveTasksToStorage]);

  // 清空所有任务
  const clearAllTasks = useCallback(() => {
    setTaskList([]);
    saveTasksToStorage([]);
    setSearchQuery("");
  }, [saveTasksToStorage]);

  // 更新任务状态
  const updateTaskStatus = useCallback((taskId: string, status: 'processing' | 'complete' | 'failed', downloadUrl?: string) => {
    setTaskList(prev => {
      const updatedTasks = prev.map(task =>
        task.taskId === taskId
          ? { ...task, status, downloadUrl, isRefreshing: false }
          : task
      );
      saveTasksToStorage(updatedTasks);
      return updatedTasks;
    });
  }, [saveTasksToStorage]);

  // 从后端获取任务状态
  const fetchTaskStatus = useCallback(async (taskId: string) => {
    try {
      // 设置刷新状态
      setTaskList(prev => prev.map(task =>
        task.taskId === taskId ? { ...task, isRefreshing: true } : task
      ));

      // 获取token - 使用与主页面相同的方式
      const token = localStorage.getItem('access_token');
      if (!token) {
        throw new Error('未找到访问令牌，请重新登录');
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/tts/status/${taskId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      // 更新任务状态
      if (data.status === 'complete') {
        updateTaskStatus(taskId, 'complete', data.audioUrl);
      } else if (data.status === 'processing') {
        updateTaskStatus(taskId, 'processing');
      } else if (data.status === 'failed') {
        updateTaskStatus(taskId, 'failed');
      } else {
        // 对于未知状态，设置为failed
        updateTaskStatus(taskId, 'failed');
      }

    } catch (error: any) {
      console.error('Failed to fetch task status:', error);
      // 重置刷新状态
      setTaskList(prev => prev.map(task =>
        task.taskId === taskId ? { ...task, isRefreshing: false } : task
      ));
      // 这里可以添加错误提示，但为了简化，暂时只在控制台输出
    }
  }, [updateTaskStatus]);

  // 复制任务ID到剪贴板
  const copyTaskId = useCallback(async (taskId: string) => {
    try {
      await navigator.clipboard.writeText(taskId);
      setCopiedTaskId(taskId);
      // 2秒后重置复制状态
      setTimeout(() => {
        setCopiedTaskId(null);
      }, 2000);
    } catch (error) {
      console.error('Failed to copy task ID:', error);
      // 如果clipboard API不可用，使用fallback方法
      try {
        const textArea = document.createElement('textarea');
        textArea.value = taskId;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        setCopiedTaskId(taskId);
        setTimeout(() => {
          setCopiedTaskId(null);
        }, 2000);
      } catch (fallbackError) {
        console.error('Fallback copy method also failed:', fallbackError);
      }
    }
  }, []);

  // 搜索过滤逻辑
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredTasks(taskList);
    } else {
      const query = searchQuery.toLowerCase().trim();
      const filtered = taskList.filter(task =>
        task.taskId.toLowerCase().includes(query)
      );
      setFilteredTasks(filtered);
    }
  }, [taskList, searchQuery]);

  // 格式化时间显示
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMinutes < 1) {
      return '刚刚';
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  // 暴露方法给父组件使用
  React.useImperativeHandle(ref, () => ({
    addTask,
    updateTaskStatus
  }));

  return (
    <>
      {/* 任务中心按钮 */}
      <div className={className}>
        <button
          onClick={() => setShowTaskCenter(true)}
          className="relative overflow-hidden px-4 py-2 text-sm font-semibold rounded-2xl transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform group text-white bg-gradient-to-r from-purple-500 via-purple-600 to-indigo-600 hover:from-purple-600 hover:via-purple-700 hover:to-indigo-700 hover:scale-105 backdrop-blur-sm"
          title="查看任务中心"
        >
          {/* 渐变光效背景 */}
          <div className="absolute inset-0 bg-gradient-to-r from-purple-400/20 via-purple-500/20 to-indigo-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          
          {/* 图标和文字 */}
          <List className="w-4 h-4 relative z-10" />
          <span className="relative z-10">任务中心</span>
          
          {/* 任务数量徽章 */}
          {taskList.length > 0 && (
            <span className="relative z-10 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] h-[18px] flex items-center justify-center">
              {taskList.length > 99 ? '99+' : taskList.length}
            </span>
          )}
        </button>
      </div>

      {/* 任务中心模态框 */}
      <Dialog open={showTaskCenter} onOpenChange={setShowTaskCenter}>
        <DialogContent className="max-w-4xl max-h-[80vh] flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-xl font-bold">
              <List className="w-5 h-5 text-purple-600" />
              任务中心
              <span className="text-sm font-normal text-gray-500">
                ({taskList.length} 个任务)
              </span>
            </DialogTitle>
          </DialogHeader>

          {/* 搜索和操作区域 */}
          <div className="flex items-center gap-3 py-4 border-b">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="搜索任务ID..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 border-2 border-gray-200 rounded-xl focus:border-purple-400 focus:ring-purple-500/10"
              />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery("")}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 hover:text-gray-600"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
            </div>

            {taskList.length > 0 && (
              <Button
                onClick={clearAllTasks}
                variant="outline"
                size="sm"
                className="px-4 py-2 text-sm border-2 border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300 rounded-xl"
              >
                清空全部
              </Button>
            )}
          </div>

          {/* 任务列表区域 */}
          <div className="flex-1 overflow-y-auto min-h-[300px]">
            {filteredTasks.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-gray-500 py-12">
                <List className="w-16 h-16 text-gray-300 mb-4" />
                <p className="text-lg font-medium mb-2">
                  {taskList.length === 0 ? '暂无任务记录' : '未找到匹配的任务'}
                </p>
                <p className="text-sm text-gray-400">
                  {taskList.length === 0
                    ? '当您开始生成音频时，任务记录将显示在这里'
                    : '尝试使用不同的搜索关键词'
                  }
                </p>
              </div>
            ) : (
              <div className="space-y-3 p-1">
                {filteredTasks.map((task, index) => (
                  <div
                    key={task.taskId}
                    className="bg-white border-2 border-gray-100 rounded-xl p-4 hover:border-purple-200 hover:shadow-md transition-all duration-200"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-xs font-medium text-purple-600 bg-purple-100 px-2 py-1 rounded-lg">
                            #{index + 1}
                          </span>
                          <span className="text-xs text-gray-500 flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {formatTime(task.createdAt)}
                          </span>
                          {/* 状态指示器 */}
                          {task.status && (
                            <span className={`text-xs px-2 py-1 rounded-lg font-medium ${
                              task.status === 'complete'
                                ? 'bg-green-100 text-green-700'
                                : task.status === 'processing'
                                ? 'bg-blue-100 text-blue-700'
                                : task.status === 'failed'
                                ? 'bg-red-100 text-red-700'
                                : 'bg-gray-100 text-gray-700'
                            }`}>
                              {task.status === 'complete' ? '已完成' :
                               task.status === 'processing' ? '处理中' :
                               task.status === 'failed' ? '失败' : '未知'}
                            </span>
                          )}
                        </div>

                        {/* 任务ID和操作按钮在同一行 */}
                        <div className="flex items-center gap-3">
                          <span className="text-sm font-medium text-gray-700 whitespace-nowrap">任务ID:</span>
                          <div className="flex-1 min-w-0 relative group">
                            <div
                              className="text-xs font-mono bg-gray-50 px-3 py-2 rounded-lg border break-all truncate cursor-pointer hover:bg-gray-100 transition-colors duration-200 relative"
                              onClick={() => copyTaskId(task.taskId)}
                              title="点击复制任务ID"
                            >
                              {task.taskId}

                              {/* 复制图标 - 悬停时显示 */}
                              <div className="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                {copiedTaskId === task.taskId ? (
                                  <Check className="w-3 h-3 text-green-600" />
                                ) : (
                                  <Copy className="w-3 h-3 text-gray-500" />
                                )}
                              </div>
                            </div>

                            {/* 复制成功提示 */}
                            {copiedTaskId === task.taskId && (
                              <div className="absolute top-full left-0 mt-1 bg-green-600 text-white text-xs px-2 py-1 rounded shadow-lg z-10">
                                已复制到剪贴板
                              </div>
                            )}
                          </div>

                          {/* 操作按钮区域 - 水平排列 */}
                          <div className="flex items-center gap-2 flex-shrink-0">
                            {/* 下载按钮 */}
                            <button
                              onClick={() => {
                                if (task.downloadUrl) {
                                  window.open(task.downloadUrl, '_blank');
                                }
                              }}
                              disabled={!task.downloadUrl || task.status !== 'complete'}
                              className={`p-2 rounded-lg transition-all duration-200 flex items-center justify-center ${
                                task.downloadUrl && task.status === 'complete'
                                  ? 'bg-green-100 text-green-600 hover:bg-green-200 hover:text-green-700 cursor-pointer'
                                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                              }`}
                              title={
                                task.downloadUrl && task.status === 'complete'
                                  ? '下载音频文件'
                                  : task.status === 'processing'
                                  ? '任务处理中，请稍后再试'
                                  : task.status === 'failed'
                                  ? '任务失败，无法下载'
                                  : '任务未完成，请点击刷新按钮查询状态'
                              }
                            >
                              <Download className="w-4 h-4" />
                            </button>

                            {/* 刷新按钮 */}
                            <button
                              onClick={() => fetchTaskStatus(task.taskId)}
                              disabled={task.isRefreshing}
                              className={`p-2 rounded-lg transition-all duration-200 flex items-center justify-center ${
                                task.isRefreshing
                                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                  : 'bg-blue-100 text-blue-600 hover:bg-blue-200 hover:text-blue-700 cursor-pointer'
                              }`}
                              title="刷新任务状态"
                            >
                              <RefreshCw className={`w-4 h-4 ${task.isRefreshing ? 'animate-spin' : ''}`} />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
});

TaskCenter.displayName = 'TaskCenter';

export default TaskCenter;
