// 集成测试：验证SSML功能与现有系统的兼容性
// 模拟完整的splitText函数逻辑

// 模拟智能分割函数
function smartSplitLongText(text, maxLength) {
  const subChunks = [];
  let remainingText = text;

  while (remainingText.length > maxLength) {
    let splitPos = -1;
    const searchRange = remainingText.substring(0, maxLength);

    // 寻找标点符号
    const punctuationChars = '，,;；:：';
    for (let i = 0; i < punctuationChars.length; i++) {
      const pos = searchRange.lastIndexOf(punctuationChars[i]);
      if (pos > splitPos) {
        splitPos = pos + 1;
      }
    }

    // 寻找空格
    if (splitPos === -1) {
      splitPos = searchRange.lastIndexOf(' ');
    }

    // 硬切分
    if (splitPos === -1 || splitPos < maxLength * 0.7) {
      splitPos = maxLength;
    }

    splitPos = Math.max(1, Math.min(splitPos, remainingText.length));
    const chunk = remainingText.substring(0, splitPos).trim();
    if (chunk) {
      subChunks.push(chunk);
    }
    remainingText = remainingText.substring(splitPos).trim();
  }

  if (remainingText.trim()) {
    subChunks.push(remainingText.trim());
  }

  return subChunks;
}

// 模拟传统分割函数
async function splitTextTraditional(text, maxLength) {
  // 简化的句子分割
  const sentencePattern = /(?<=[。！？!?；;:：…]{1,2})\s*/;
  const sentences = text.split(sentencePattern);

  const chunks = [];
  let currentChunk = "";

  for (const sentence of sentences) {
    if (sentence.length > maxLength) {
      if (currentChunk) {
        chunks.push(currentChunk.trim());
        currentChunk = "";
      }
      const subChunks = smartSplitLongText(sentence, maxLength);
      chunks.push(...subChunks);
    } else if (currentChunk.length + sentence.length > maxLength) {
      chunks.push(currentChunk.trim());
      currentChunk = sentence;
    } else {
      currentChunk += sentence;
    }
  }

  if (currentChunk) {
    chunks.push(currentChunk.trim());
  }

  return chunks;
}

// 模拟SSML分割函数
async function splitTextWithSSML(text, maxLength) {
  const parts = text.split(/(\[.*?\])/g).filter(Boolean);
  const chunks = [];
  let currentChunk = "";

  for (const part of parts) {
    if (currentChunk.length + part.length > maxLength) {
      if (currentChunk.trim()) {
        chunks.push(currentChunk.trim());
      }
      
      if (part.length > maxLength) {
        if (/^\[.*?\]$/.test(part.trim())) {
          console.warn('[SSML-SPLIT] Warning: SSML directive exceeds maxLength:', part.substring(0, 50) + '...');
          chunks.push(part.trim());
          currentChunk = "";
        } else {
          const subChunks = smartSplitLongText(part, maxLength);
          chunks.push(...subChunks);
          currentChunk = "";
        }
      } else {
        currentChunk = part;
      }
    } else {
      currentChunk += part;
    }
  }

  if (currentChunk.trim()) {
    chunks.push(currentChunk.trim());
  }
  
  if (chunks.length === 0 && text.length > 0) {
    chunks.push(text);
  }

  return chunks;
}

// 模拟完整的splitText函数
async function splitText(text) {
  const maxLength = 490;
  const hasSSMLDirectives = /\[.*?\]/.test(text);
  
  if (hasSSMLDirectives) {
    console.log('[SSML-SPLIT] Detected SSML directives, using SSML-aware splitting');
    return await splitTextWithSSML(text, maxLength);
  } else {
    return await splitTextTraditional(text, maxLength);
  }
}

// 集成测试用例
async function runIntegrationTests() {
  console.log('=== 集成测试开始 ===\n');

  const testCases = [
    {
      name: "传统文本（无SSML）",
      text: "这是一段普通的中文文本。它不包含任何SSML指令。应该使用传统的分割方法处理。这段文本足够长，可以测试分割功能是否正常工作。",
      expectSSML: false
    },
    {
      name: "英文文本（无SSML）",
      text: "This is a normal English text without any SSML directives. It should be processed using the traditional splitting method. This text is long enough to test the splitting functionality.",
      expectSSML: false
    },
    {
      name: "包含SSML的中文文本",
      text: "[calmly] 在古老的艾尔多利亚大陆上，天空闪闪发光，森林 [whispering] 向风儿低语着秘密，住着一条名叫泽菲罗斯的龙。[sarcastic] 不是那种'烧毁一切'的龙！[thoughtful] 他很温和，很聪明。",
      expectSSML: true
    },
    {
      name: "包含SSML的英文文本",
      text: "[calmly] In the ancient land of Eldoria, where skies shimmered and forests [whispering] whispered secrets to the wind, lived a dragon named Zephyros. [sarcastic] Not the 'burn it all down' kind!",
      expectSSML: true
    },
    {
      name: "混合语言与SSML",
      text: "[excited] Hello world! 你好世界！[whispering] This is a secret. 这是一个秘密。[normal] Back to normal voice.",
      expectSSML: true
    },
    {
      name: "超长文本与SSML",
      text: "[calmly] " + "这是一段非常长的文本，".repeat(50) + " [whispering] " + "用来测试超长文本的分割功能。".repeat(30),
      expectSSML: true
    },
    {
      name: "空文本",
      text: "",
      expectSSML: false
    },
    {
      name: "仅包含SSML指令",
      text: "[calmly] [whispering] [excited]",
      expectSSML: true
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n--- 测试: ${testCase.name} ---`);
    console.log(`输入长度: ${testCase.text.length} 字符`);
    
    const hasSSML = /\[.*?\]/.test(testCase.text);
    console.log(`检测到SSML: ${hasSSML} (期望: ${testCase.expectSSML})`);
    
    if (hasSSML !== testCase.expectSSML) {
      console.error(`❌ SSML检测失败！期望: ${testCase.expectSSML}, 实际: ${hasSSML}`);
      continue;
    }

    try {
      const startTime = Date.now();
      const chunks = await splitText(testCase.text);
      const endTime = Date.now();
      
      console.log(`✅ 分割成功`);
      console.log(`处理时间: ${endTime - startTime}ms`);
      console.log(`输出块数: ${chunks.length}`);
      
      if (chunks.length > 0) {
        console.log(`块长度范围: ${Math.min(...chunks.map(c => c.length))} - ${Math.max(...chunks.map(c => c.length))} 字符`);
        
        // 验证所有块都不超过最大长度
        const oversizedChunks = chunks.filter(chunk => chunk.length > 490);
        if (oversizedChunks.length > 0) {
          console.warn(`⚠️ 发现超长块: ${oversizedChunks.length} 个`);
        }
        
        // 验证SSML指令完整性
        if (hasSSML) {
          const allSSMLDirectives = testCase.text.match(/\[.*?\]/g) || [];
          const chunksSSMLDirectives = chunks.flatMap(chunk => chunk.match(/\[.*?\]/g) || []);
          
          if (allSSMLDirectives.length === chunksSSMLDirectives.length) {
            console.log(`✅ SSML指令完整性验证通过 (${allSSMLDirectives.length} 个指令)`);
          } else {
            console.error(`❌ SSML指令丢失！原始: ${allSSMLDirectives.length}, 分割后: ${chunksSSMLDirectives.length}`);
          }
        }
        
        // 显示前几个块的内容（用于调试）
        if (testCase.text.length > 0) {
          console.log(`前${Math.min(3, chunks.length)}个块:`);
          chunks.slice(0, 3).forEach((chunk, index) => {
            const preview = chunk.length > 60 ? chunk.substring(0, 60) + '...' : chunk;
            console.log(`  块${index + 1} (${chunk.length}字符): ${preview}`);
          });
        }
      }
      
    } catch (error) {
      console.error(`❌ 分割失败: ${error.message}`);
    }
  }

  console.log('\n=== 集成测试完成 ===');
}

// 运行集成测试
runIntegrationTests().catch(console.error);
