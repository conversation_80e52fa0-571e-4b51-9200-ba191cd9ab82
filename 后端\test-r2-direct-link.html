<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>R2直链下载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .url-test {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 3px;
        }
        .url-test a {
            color: #007bff;
            text-decoration: none;
        }
        .url-test a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 R2直链下载优化测试</h1>
        
        <div class="test-section info">
            <h3>📋 测试说明</h3>
            <p>此页面用于测试R2直链下载优化是否正常工作。</p>
            <p><strong>优化前：</strong> 前端 → Worker → R2 → Worker → 前端</p>
            <p><strong>优化后：</strong> 前端 → CDN → R2 → 前端（绕过Worker）</p>
        </div>

        <div class="test-section">
            <h3>🔗 R2直链格式测试</h3>
            <p>测试R2直链URL格式是否正确：</p>
            <div class="url-test">
                <strong>域名：</strong> r2-assets.aispeak.top<br>
                <strong>路径格式：</strong> /audios/{taskId}.mp3<br>
                <strong>示例URL：</strong> 
                <a href="https://r2-assets.aispeak.top/audios/test-12345.mp3" target="_blank">
                    https://r2-assets.aispeak.top/audios/test-12345.mp3
                </a>
            </div>
            <button onclick="testR2Domain()">测试R2域名连通性</button>
            <div id="domainTestResult"></div>
        </div>

        <div class="test-section">
            <h3>🎵 完整TTS流程测试</h3>
            <p>测试完整的TTS生成和R2直链下载流程：</p>
            <input type="text" id="testText" placeholder="输入测试文本" value="这是R2直链下载防重复测试" style="width: 300px; padding: 8px; margin: 5px;">
            <br>
            <button onclick="startTTSTest()" id="ttsTestBtn">开始TTS测试</button>
            <button onclick="clearResults()">清空结果</button>
            <div id="ttsTestResult"></div>

            <div style="margin-top: 15px; padding: 10px; background-color: #e3f2fd; border-radius: 5px;">
                <h4>🔍 重复下载检测</h4>
                <p><strong>修复内容：</strong></p>
                <ul style="margin-left: 20px; font-size: 14px;">
                    <li>✅ 添加了防重复下载缓存机制</li>
                    <li>✅ 增强了下载过程的日志记录</li>
                    <li>✅ 优化了R2直链和Worker代理的切换逻辑</li>
                </ul>
                <p><strong>预期结果：</strong> 网络请求中应该只看到一次音频下载请求</p>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 性能对比测试</h3>
            <p>对比Worker代理下载和R2直链下载的性能：</p>
            <button onclick="performanceTest()" id="perfTestBtn">开始性能测试</button>
            <div id="performanceResult"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'https://ttsapi.aispeak.top';
        let currentTaskId = null;

        // 测试R2域名连通性
        async function testR2Domain() {
            const resultDiv = document.getElementById('domainTestResult');
            resultDiv.innerHTML = '<p>🔍 测试R2域名连通性...</p>';
            
            try {
                // 测试域名解析
                const testUrl = 'https://r2-assets.aispeak.top/';
                const response = await fetch(testUrl, { method: 'HEAD' });
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ R2域名连通性测试成功</h4>
                        <p><strong>状态码：</strong> ${response.status}</p>
                        <p><strong>响应头：</strong></p>
                        <pre>${Array.from(response.headers.entries()).map(([k,v]) => `${k}: ${v}`).join('\n')}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ R2域名连通性测试失败</h4>
                        <p><strong>错误：</strong> ${error.message}</p>
                    </div>
                `;
            }
        }

        // 开始TTS测试
        async function startTTSTest() {
            const text = document.getElementById('testText').value.trim();
            if (!text) {
                alert('请输入测试文本');
                return;
            }

            const resultDiv = document.getElementById('ttsTestResult');
            const btn = document.getElementById('ttsTestBtn');
            
            btn.disabled = true;
            btn.textContent = '测试中...';
            
            try {
                resultDiv.innerHTML = '<p>🚀 开始TTS生成测试...</p>';
                
                // 步骤1：生成TTS
                const generateResponse = await fetch(`${API_BASE}/api/tts/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                    },
                    body: JSON.stringify({
                        input: text,
                        voice: 'Brian',
                        stability: 0.5,
                        similarity_boost: 0.75,
                        style: 0.5,
                        speed: 1.0
                    })
                });

                if (!generateResponse.ok) {
                    throw new Error(`生成请求失败: ${generateResponse.status}`);
                }

                const generateData = await generateResponse.json();
                currentTaskId = generateData.taskId;
                
                resultDiv.innerHTML += `
                    <div class="info">
                        <p>✅ TTS任务已创建</p>
                        <p><strong>任务ID：</strong> ${currentTaskId}</p>
                    </div>
                `;

                // 步骤2：轮询状态
                await pollTaskStatus(currentTaskId, resultDiv);
                
            } catch (error) {
                resultDiv.innerHTML += `
                    <div class="error">
                        <h4>❌ TTS测试失败</h4>
                        <p><strong>错误：</strong> ${error.message}</p>
                    </div>
                `;
            } finally {
                btn.disabled = false;
                btn.textContent = '开始TTS测试';
            }
        }

        // 轮询任务状态
        async function pollTaskStatus(taskId, resultDiv) {
            const maxAttempts = 60; // 最多轮询60次
            let attempts = 0;
            
            while (attempts < maxAttempts) {
                try {
                    const statusResponse = await fetch(`${API_BASE}/api/tts/status/${taskId}`, {
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                        }
                    });

                    if (!statusResponse.ok) {
                        throw new Error(`状态查询失败: ${statusResponse.status}`);
                    }

                    const statusData = await statusResponse.json();
                    
                    resultDiv.innerHTML += `<p>📊 状态检查 ${attempts + 1}: ${statusData.status} - ${statusData.progress || statusData.currentStep || ''}</p>`;
                    
                    if (statusData.status === 'complete') {
                        // 任务完成，分析下载URL
                        await analyzeDownloadUrl(statusData, resultDiv);
                        return;
                    } else if (statusData.status === 'failed') {
                        throw new Error(`任务失败: ${statusData.error}`);
                    }
                    
                    attempts++;
                    await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒
                    
                } catch (error) {
                    throw new Error(`状态轮询失败: ${error.message}`);
                }
            }
            
            throw new Error('任务超时');
        }

        // 分析下载URL
        async function analyzeDownloadUrl(statusData, resultDiv) {
            const audioUrl = statusData.audioUrl;
            const isR2Direct = audioUrl && audioUrl.includes('r2-assets.aispeak.top');

            resultDiv.innerHTML += `
                <div class="${isR2Direct ? 'success' : 'error'}">
                    <h4>${isR2Direct ? '✅ R2直链优化成功！' : '⚠️ 仍在使用Worker代理'}</h4>
                    <p><strong>状态接口返回的audioUrl：</strong> <a href="${audioUrl || 'N/A'}" target="_blank">${audioUrl || 'N/A'}</a></p>
                    <p><strong>下载方式：</strong> ${isR2Direct ? 'R2直链 (优化后)' : 'Worker代理 (优化前)'}</p>
                    <p><strong>音频大小：</strong> ${(statusData.audioSize / 1024 / 1024).toFixed(2)} MB</p>
                    <p><strong>处理块数：</strong> ${statusData.chunksProcessed}/${statusData.totalChunks}</p>
                    <p><strong>前端轮询修复：</strong> <span style="color: green;">✅ 已修复，前端将自动使用R2直链</span></p>
                </div>
            `;

            // 测试下载速度
            if (isR2Direct) {
                await testDownloadSpeed(audioUrl, resultDiv);
            } else {
                resultDiv.innerHTML += `
                    <div class="info">
                        <p><strong>说明：</strong> 如果看到Worker代理，可能是：</p>
                        <ul style="margin-left: 20px;">
                            <li>1. 旧任务（R2直链优化前生成的）</li>
                            <li>2. R2直链生成失败，自动回退到Worker代理</li>
                            <li>3. 后端配置问题</li>
                        </ul>
                        <p><strong>建议：</strong> 生成新任务测试R2直链优化效果</p>
                    </div>
                `;
            }
        }

        // 测试下载速度
        async function testDownloadSpeed(url, resultDiv) {
            try {
                resultDiv.innerHTML += '<p>⏱️ 测试下载速度...</p>';
                
                const startTime = Date.now();
                const response = await fetch(url);
                
                if (!response.ok) {
                    throw new Error(`下载失败: ${response.status}`);
                }
                
                const blob = await response.blob();
                const endTime = Date.now();
                const duration = endTime - startTime;
                const sizeInMB = blob.size / 1024 / 1024;
                const speedInMBps = sizeInMB / (duration / 1000);
                
                resultDiv.innerHTML += `
                    <div class="success">
                        <h4>📈 下载性能测试</h4>
                        <p><strong>文件大小：</strong> ${sizeInMB.toFixed(2)} MB</p>
                        <p><strong>下载时间：</strong> ${duration} ms</p>
                        <p><strong>下载速度：</strong> ${speedInMBps.toFixed(2)} MB/s</p>
                    </div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML += `
                    <div class="error">
                        <p>❌ 下载速度测试失败: ${error.message}</p>
                    </div>
                `;
            }
        }

        // 性能对比测试
        async function performanceTest() {
            const resultDiv = document.getElementById('performanceResult');
            const btn = document.getElementById('perfTestBtn');
            
            if (!currentTaskId) {
                alert('请先完成TTS测试以获取任务ID');
                return;
            }
            
            btn.disabled = true;
            btn.textContent = '测试中...';
            
            try {
                resultDiv.innerHTML = '<p>🔄 开始性能对比测试...</p>';
                
                // 测试R2直链下载
                const r2Url = `https://r2-assets.aispeak.top/audios/${currentTaskId}.mp3`;
                const r2Result = await measureDownloadTime(r2Url, 'R2直链');
                
                // 测试Worker代理下载
                const workerUrl = `${API_BASE}/api/tts/download/${currentTaskId}`;
                const workerResult = await measureDownloadTime(workerUrl, 'Worker代理', true);
                
                // 显示对比结果
                const speedImprovement = ((workerResult.speed - r2Result.speed) / workerResult.speed * 100).toFixed(1);
                
                resultDiv.innerHTML += `
                    <div class="success">
                        <h4>📊 性能对比结果</h4>
                        <table style="width: 100%; border-collapse: collapse;">
                            <tr style="background-color: #f8f9fa;">
                                <th style="border: 1px solid #ddd; padding: 8px;">下载方式</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">下载时间</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">下载速度</th>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 8px;">R2直链 (优化后)</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${r2Result.duration} ms</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${r2Result.speed.toFixed(2)} MB/s</td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 8px;">Worker代理 (优化前)</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${workerResult.duration} ms</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${workerResult.speed.toFixed(2)} MB/s</td>
                            </tr>
                        </table>
                        <p><strong>🚀 性能提升：</strong> ${speedImprovement > 0 ? '+' : ''}${speedImprovement}%</p>
                    </div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML += `
                    <div class="error">
                        <p>❌ 性能测试失败: ${error.message}</p>
                    </div>
                `;
            } finally {
                btn.disabled = false;
                btn.textContent = '开始性能测试';
            }
        }

        // 测量下载时间
        async function measureDownloadTime(url, method, needAuth = false) {
            const headers = {};
            if (needAuth) {
                headers['Authorization'] = `Bearer ${localStorage.getItem('access_token')}`;
            }
            
            const startTime = Date.now();
            const response = await fetch(url, { headers });
            
            if (!response.ok) {
                throw new Error(`${method}下载失败: ${response.status}`);
            }
            
            const blob = await response.blob();
            const endTime = Date.now();
            const duration = endTime - startTime;
            const sizeInMB = blob.size / 1024 / 1024;
            const speed = sizeInMB / (duration / 1000);
            
            return { duration, speed, size: sizeInMB };
        }

        // 清空结果
        function clearResults() {
            document.getElementById('ttsTestResult').innerHTML = '';
            document.getElementById('performanceResult').innerHTML = '';
            document.getElementById('domainTestResult').innerHTML = '';
            currentTaskId = null;
        }

        // 页面加载时检查登录状态
        window.onload = function() {
            const token = localStorage.getItem('access_token');
            if (!token) {
                document.body.innerHTML = `
                    <div class="container">
                        <div class="error">
                            <h3>❌ 未登录</h3>
                            <p>请先登录系统后再进行测试。</p>
                            <a href="/" style="color: #007bff;">返回登录页面</a>
                        </div>
                    </div>
                `;
            }
        };
    </script>
</body>
</html>
