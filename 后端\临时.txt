# 你的 Cloudflare 账户 ID
account_id = "23b9e456d27237601315e751f0d33059" # <-- 必须修改

# Worker 的基本信息
name = "tts-api"
main = "worker.js"
compatibility_date = "2024-03-20"

# Durable Object 绑定
[durable_objects]
bindings = [
  { name = "TTS_TASK_DO", class_name = "TtsTaskDo" }
]

# Durable Object 迁移记录 (建议保留)
[[migrations]]
tag = "v1"
new_classes = ["TtsTaskDo"]

# KV 命名空间绑定
# 运行 `wrangler kv:namespace create <NAME>` 获取 id
kv_namespaces = [
  { binding = "TTS_STATUS", id = "0ae5fbcb1ed34dab9357ae1a838b34f3" },
  { binding = "USERS",      id = "f79f4b66ec7c4902aa5e65ba33c2c191" },
  { binding = "CARDS",      id = "69d6e32b35dd4a0bb996584ebf3f5b27" }
]

# R2 存储桶绑定
# 运行 `wrangler r2 bucket create <NAME>` 创建存储桶
r2_buckets = [
  { binding = "AUDIOS",       bucket_name = "audios" },
  { binding = "AUDIO_BUCKET", bucket_name = "preview-audio-kf" }
]

# 环境变量
[vars]
SES_REGION = "ap-guangzhou"
FROM_EMAIL = "<EMAIL>" # <-- 建议修改
FROM_EMAIL_NAME = "tts.aispeak.top"
VERIFICATION_TEMPLATE_ID = "32699" # <-- 必须修改
DEBUG = false

# 提醒：请不要忘记使用 `wrangler secret put <KEY>` 命令设置以下机密信息：
# - JWT_SECRET
# - TENCENT_SECRET_ID
# - TENCENT_SECRET_KEY