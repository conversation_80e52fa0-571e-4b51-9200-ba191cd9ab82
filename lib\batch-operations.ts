/**
 * 批量操作工具函数模块
 * 提供对话行的复制、导入、解析等功能
 */

interface Voice {
  id: string;
  name: string;
  gender: string;
  language?: string;
  description: string;
  preview?: string;
}

interface DialogueLine {
  id: number;
  voice: string;
  text: string;
}

interface ParseResult {
  validLines: DialogueLine[];
  errors: string[];
  warnings: string[];
}

/**
 * 格式化对话行为复制文本
 */
export function formatDialogueForCopy(
  dialogueLines: DialogueLine[], 
  voices: Voice[]
): string {
  const validLines = dialogueLines.filter(line => line.text.trim());
  
  if (validLines.length === 0) {
    throw new Error('没有可复制的对话内容');
  }
  
  return validLines.map(line => {
    const voice = voices.find(v => v.id === line.voice);
    const voiceName = voice?.name || '未知声音';
    return `${voiceName}@${line.text.trim()}`;
  }).join('\n');
}

/**
 * 复制文本到剪贴板
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // 降级方案：使用传统的 document.execCommand
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      const result = document.execCommand('copy');
      document.body.removeChild(textArea);
      return result;
    }
  } catch (error) {
    console.error('复制失败:', error);
    return false;
  }
}

/**
 * 智能声音匹配
 * 支持精确匹配、模糊匹配、别名匹配等
 */
export function matchVoiceName(inputName: string, voices: Voice[]): Voice | null {
  const normalizedInput = inputName.toLowerCase().trim();
  
  if (!normalizedInput) return null;
  
  // 1. 精确匹配
  let match = voices.find(v => v.name.toLowerCase() === normalizedInput);
  if (match) return match;
  
  // 2. 模糊匹配 - 包含关系
  match = voices.find(v => 
    v.name.toLowerCase().includes(normalizedInput) ||
    normalizedInput.includes(v.name.toLowerCase())
  );
  if (match) return match;
  
  // 3. 去除空格后匹配
  const inputNoSpaces = normalizedInput.replace(/\s+/g, '');
  match = voices.find(v => {
    const voiceNoSpaces = v.name.toLowerCase().replace(/\s+/g, '');
    return voiceNoSpaces === inputNoSpaces || 
           voiceNoSpaces.includes(inputNoSpaces) ||
           inputNoSpaces.includes(voiceNoSpaces);
  });
  if (match) return match;
  
  // 4. 首字母匹配（用于缩写）
  if (inputNoSpaces.length <= 3) {
    match = voices.find(v => {
      const initials = v.name.toLowerCase()
        .split(/\s+/)
        .map(word => word.charAt(0))
        .join('');
      return initials === inputNoSpaces;
    });
    if (match) return match;
  }
  
  return null;
}

/**
 * 解析导入文本
 */
export function parseImportText(text: string, voices: Voice[]): ParseResult {
  const lines = text.split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0);
  
  const validLines: DialogueLine[] = [];
  const errors: string[] = [];
  const warnings: string[] = [];
  
  if (lines.length === 0) {
    errors.push('导入内容为空');
    return { validLines, errors, warnings };
  }
  
  lines.forEach((line, index) => {
    const lineNumber = index + 1;
    
    // 检查基本格式
    const match = line.match(/^(.+?)@(.+)$/);
    if (!match) {
      errors.push(`第${lineNumber}行格式错误：${line.substring(0, 50)}${line.length > 50 ? '...' : ''}`);
      return;
    }
    
    const [, voiceName, dialogueText] = match;
    const trimmedVoiceName = voiceName.trim();
    const trimmedText = dialogueText.trim();
    
    // 检查声音名称
    if (!trimmedVoiceName) {
      errors.push(`第${lineNumber}行声音名称为空`);
      return;
    }
    
    // 检查对话文本
    if (!trimmedText) {
      errors.push(`第${lineNumber}行对话内容为空`);
      return;
    }
    
    // 检查文本长度
    if (trimmedText.length > 1000) {
      warnings.push(`第${lineNumber}行文本过长(${trimmedText.length}字符)，建议控制在1000字符以内`);
    }
    
    // 匹配声音
    const voice = matchVoiceName(trimmedVoiceName, voices);
    
    if (!voice) {
      warnings.push(`第${lineNumber}行声音"${trimmedVoiceName}"不存在，将使用默认声音`);
      // 使用默认声音（第一个声音）
      validLines.push({
        id: Date.now() + Math.random() + index,
        voice: voices[0]?.id || "",
        text: trimmedText
      });
    } else {
      validLines.push({
        id: Date.now() + Math.random() + index,
        voice: voice.id,
        text: trimmedText
      });
    }
  });
  
  return { validLines, errors, warnings };
}

/**
 * 验证导入数据
 */
export function validateImportData(lines: DialogueLine[], voices: Voice[]): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  if (lines.length === 0) {
    errors.push('没有有效的对话行');
    return { isValid: false, errors, warnings };
  }
  
  if (lines.length > 100) {
    warnings.push(`对话行数量较多(${lines.length}行)，可能影响性能`);
  }
  
  // 检查重复的声音使用
  const voiceUsage = new Map<string, number>();
  lines.forEach(line => {
    const count = voiceUsage.get(line.voice) || 0;
    voiceUsage.set(line.voice, count + 1);
  });
  
  voiceUsage.forEach((count, voiceId) => {
    if (count > 10) {
      const voice = voices.find(v => v.id === voiceId);
      warnings.push(`声音"${voice?.name || voiceId}"使用次数较多(${count}次)`);
    }
  });
  
  // 检查文本总长度
  const totalLength = lines.reduce((sum, line) => sum + line.text.length, 0);
  if (totalLength > 50000) {
    warnings.push(`总文本长度较大(${totalLength}字符)，生成音频可能需要较长时间`);
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 分批处理大量数据
 */
export async function batchProcess<T>(
  items: T[],
  batchSize: number,
  processor: (batch: T[], batchIndex: number) => Promise<void> | void,
  onProgress?: (processed: number, total: number) => void
): Promise<void> {
  const total = items.length;
  let processed = 0;
  
  for (let i = 0; i < total; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchIndex = Math.floor(i / batchSize);
    
    await processor(batch, batchIndex);
    
    processed += batch.length;
    onProgress?.(processed, total);
    
    // 让出控制权，避免阻塞UI
    if (i + batchSize < total) {
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }
}

/**
 * 生成示例导入文本
 */
export function generateSampleImportText(voices: Voice[]): string {
  const sampleVoices = voices.slice(0, 3);
  const samples = [
    '你好！今天过得怎么样？',
    '我今天过得很好，谢谢你的关心！',
    '那真是太好了，有什么特别的事情吗？'
  ];
  
  return sampleVoices.map((voice, index) => 
    `${voice.name}@${samples[index] || '这是一个示例对话。'}`
  ).join('\n');
}

/**
 * 导出统计信息
 */
export function getDialogueStats(lines: DialogueLine[], voices: Voice[]): {
  totalLines: number;
  totalCharacters: number;
  voiceDistribution: Array<{ voice: Voice; count: number; percentage: number }>;
  averageLength: number;
} {
  const totalLines = lines.length;
  const totalCharacters = lines.reduce((sum, line) => sum + line.text.length, 0);
  const averageLength = totalLines > 0 ? Math.round(totalCharacters / totalLines) : 0;
  
  // 统计声音分布
  const voiceCount = new Map<string, number>();
  lines.forEach(line => {
    const count = voiceCount.get(line.voice) || 0;
    voiceCount.set(line.voice, count + 1);
  });
  
  const voiceDistribution = Array.from(voiceCount.entries())
    .map(([voiceId, count]) => {
      const voice = voices.find(v => v.id === voiceId);
      return {
        voice: voice || { id: voiceId, name: '未知声音', gender: 'neutral', description: '' },
        count,
        percentage: Math.round((count / totalLines) * 100)
      };
    })
    .sort((a, b) => b.count - a.count);
  
  return {
    totalLines,
    totalCharacters,
    voiceDistribution,
    averageLength
  };
}
