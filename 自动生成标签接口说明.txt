API:https://geminitts.aispeak.top/api/tts/process

Authorization: Bearer CM8l3Wqf7TaWah7ruIAxAmMZYcAd274MAeAnFkhvxPg0TMPs 将这个放在环境变量中
Content-Type: application/json

示例: (POST)
{
  "text": "Watching him happily head for the bathroom, I continued cleaning up the trash. As I picked up the scissors to cut the flowers on the table, his phone lit up. He always set it to silent mode when he came home. Without meaning to, I saw a message from <PERSON><PERSON> on the screen: <PERSON>, you were so naughty last night, my waist still hurts~",
  "language": "auto"
}


响应示例：
{
    "success": true,
    "processedText": "Watching him happily head for the bathroom, I continued cleaning up the trash. As I picked up the scissors to cut the flowers on the table, his phone lit up. He always set it to silent mode when he came home.[thoughtful] Without meaning to, I saw a message from <PERSON><PERSON> on the screen: [whispering] <PERSON>, you were so NAUGHTY last night, my waist still hurts~",
    "originalText": "Watching him happily head for the bathroom, I continued cleaning up the trash. As I picked up the scissors to cut the flowers on the table, his phone lit up. He always set it to silent mode when he came home. Without meaning to, I saw a message from <PERSON><PERSON> on the screen: <PERSON>, you were so naughty last night, my waist still hurts~",
    "language": "auto",
    "characterCount": 355,
    "processingTime": "2025-06-24T03:36:01.904Z",
    "metadata": {
        "promptLength": 3091,
        "fullPromptLength": 3423,
        "hasEmotionTags": true,
        "tagCount": 2
    }
}