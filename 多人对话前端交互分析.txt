用户流程如下：
默认状态:
当页面加载或用户进入“多人对话”模式时，没有任何对话行被选中。
右侧面板显示的是通用设置或默认状态，例如“模型选择”、“全局参数”等。声音选择器默认选项第一个。
选择一个对话行 (进入编辑上下文):
用户用鼠标点击左侧的任意一个对话行的头像。
视觉反馈:
被点击的头像会有一个明显的高亮状态（例如，边框变色、背景变色），让用户清楚地知道“这一行现在是激活状态”。
数据同步:
这个新的右侧面板会自动加载并高亮显示当前被选中行（例如 Jessica 那行）所设置的声音（比如 "Jessica" 这个声音）。用户一眼就能看到 "哦，这一行用的是 Jessica 的声音"。
在右侧选择新声音:
用户在右侧的声音列表中滚动、搜索、筛选，并点击一个新的声音（例如 "Sarah"）。
视觉反馈:
左侧被激活的对话行立即更新。它的头像和名字会从 "Jessica" 变为 "Sarah"。
右侧的声音列表中，高亮状态也会从 "Jessica" 移到 "Sarah"。
数据更新: 后台的 dialogueLines 状态数组中，对应那一行的数据 voice 字段被更新为 "Sarah" 的ID。
选择另一个对话行:
用户接着点击左侧的另一行（例如 Grandpa 头像）。
视觉反馈:
第一行的高亮消失，新点击的这一行变为高亮。
右侧的面板内容保持为声音选择器，但列表会自动滚动并高亮到 Grandpa 当前使用的声音（比如 "Grandpa Spuds Oxley"）。
这个过程无缝衔接，用户可以快速地在不同对话行之间切换并配置声音。

当用户点击左侧任意行输入框时右侧声音选择器都应该同步改行选择的声音选项