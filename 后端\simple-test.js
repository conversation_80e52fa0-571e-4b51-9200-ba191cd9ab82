// 简化的SSML功能测试

console.log('=== SSML功能验证测试 ===\n');

// 测试SSML检测
function testSSMLDetection() {
  console.log('1. SSML检测测试:');
  
  const testCases = [
    { text: "[calmly] Hello world", expected: true },
    { text: "Hello world", expected: false },
    { text: "[whispering] Secret [normal] text", expected: true },
    { text: "No SSML here", expected: false },
    { text: "", expected: false }
  ];
  
  testCases.forEach((testCase, index) => {
    const hasSSML = /\[.*?\]/.test(testCase.text);
    const result = hasSSML === testCase.expected ? '✅' : '❌';
    console.log(`  ${index + 1}. "${testCase.text}" -> ${hasSSML} ${result}`);
  });
  console.log('');
}

// 测试SSML分割
function testSSMLSplitting() {
  console.log('2. SSML分割测试:');
  
  const text = "[calmly] Hello world. [whispering] This is a secret.";
  const parts = text.split(/(\[.*?\])/g).filter(Boolean);
  
  console.log(`  输入: ${text}`);
  console.log(`  分割结果:`);
  parts.forEach((part, index) => {
    const isSSML = /^\[.*?\]$/.test(part);
    console.log(`    ${index + 1}. "${part}" (${isSSML ? 'SSML' : '文本'})`);
  });
  console.log('');
}

// 测试块组合
function testChunkCombination() {
  console.log('3. 块组合测试:');
  
  const text = "[calmly] Hello world. [whispering] This is a secret message that should be split properly.";
  const maxLength = 50;
  const parts = text.split(/(\[.*?\])/g).filter(Boolean);
  
  const chunks = [];
  let currentChunk = "";
  
  for (const part of parts) {
    if (currentChunk.length + part.length > maxLength) {
      if (currentChunk.trim()) {
        chunks.push(currentChunk.trim());
      }
      currentChunk = part;
    } else {
      currentChunk += part;
    }
  }
  
  if (currentChunk.trim()) {
    chunks.push(currentChunk.trim());
  }
  
  console.log(`  输入: ${text}`);
  console.log(`  最大长度: ${maxLength}`);
  console.log(`  输出块:`);
  chunks.forEach((chunk, index) => {
    console.log(`    ${index + 1}. (${chunk.length}字符) "${chunk}"`);
  });
  console.log('');
}

// 测试向后兼容性
function testBackwardCompatibility() {
  console.log('4. 向后兼容性测试:');
  
  const normalTexts = [
    "这是普通的中文文本。",
    "This is normal English text.",
    "Mixed 中英文 text without SSML."
  ];
  
  normalTexts.forEach((text, index) => {
    const hasSSML = /\[.*?\]/.test(text);
    console.log(`  ${index + 1}. "${text}" -> SSML检测: ${hasSSML} (应该为false)`);
  });
  console.log('');
}

// 运行所有测试
function runAllTests() {
  try {
    testSSMLDetection();
    testSSMLSplitting();
    testChunkCombination();
    testBackwardCompatibility();
    console.log('=== 所有测试完成 ===');
  } catch (error) {
    console.error('测试失败:', error);
  }
}

runAllTests();
