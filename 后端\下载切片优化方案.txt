最终实施方案

您只需要用下面的 handleDownload 函数替换掉您 worker.js 中 handleTTS 函数里处理 /api/tts/download/ 路由的整个 try...catch 代码块。

第 1 步：添加 parseRange 辅助函数

为了让代码更清晰，我们先创建一个专门解析 Range 头的辅助函数。请将这个函数添加到您的 worker.js 文件的工具函数区域（例如，放在 corsHeaders 函数后面）。

// ========== Utils 函数 ==========
// ... 其他工具函数 ...

/**
 * 解析 HTTP Range 请求头
 * @param {string} header - Range 请求头的值 (e.g., "bytes=0-1023")
 * @param {number} totalSize - 文件的总大小
 * @returns {{start: number, end: number, length: number}|null} - 解析后的范围对象或 null
 */
function parseRange(header, totalSize) {
  if (!header) return null;

  const match = header.match(/bytes=(\d+)-(\d*)/);
  if (!match) {
    return null;
  }

  const start = parseInt(match[1], 10);
  // 如果末尾为空（如 "bytes=100-"），则表示到文件末尾
  const end = match[2] ? parseInt(match[2], 10) : totalSize - 1;

  // 范围有效性检查
  if (isNaN(start) || isNaN(end) || start > end || start >= totalSize) {
    return null;
  }

  const length = end - start + 1;
  return { start, end, length };
}

第 2 步：创建 handleDownload 主函数

这个函数包含了处理下载请求的完整逻辑。它将取代您现有代码中处理下载的部分。

/**
 * 高效处理音频下载请求，支持 Range 请求（断点续传）
 * @param {Request} request - 原始请求对象
 * @param {object} env - Cloudflare Worker 的环境对象
 * @returns {Promise<Response>}
 */
async function handleDownload(request, env) {
  const url = new URL(request.url);
  const taskId = url.pathname.split('/download/')[1];

  if (!taskId) {
    return new Response(JSON.stringify({ error: 'Task ID is required' }), {
      status: 400,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });
  }

  const key = `audios/${taskId}.mp3`;

  // 1. 获取文件元数据 (大小)
  const objectMetadata = await env.AUDIOS.head(key);
  if (objectMetadata === null) {
    return new Response(JSON.stringify({ error: 'Audio file not found' }), {
      status: 404,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });
  }
  const totalSize = objectMetadata.size;

  // 2. 解析 Range 请求头
  const rangeHeader = request.headers.get('range');
  const range = parseRange(rangeHeader, totalSize);

  // 3. 根据是否存在 Range 决定响应方式
  if (range === null) {
    // --- 情况 A: 完整文件下载 ---
    console.log(`[DOWNLOAD] Serving complete file for task ${taskId}, size: ${totalSize}`);
    const object = await env.AUDIOS.get(key);
    if (object === null) {
        return new Response('Audio file could not be retrieved.', { status: 404, headers: corsHeaders() });
    }

    return new Response(object.body, {
      status: 200,
      headers: {
        ...corsHeaders(),
        'Content-Type': 'audio/mpeg',
        'Content-Disposition': `attachment; filename="audio-${taskId}.mp3"`,
        'Content-Length': totalSize.toString(),
        'Accept-Ranges': 'bytes', // 告知浏览器支持范围请求
        'Cache-Control': 'public, max-age=3600' // 缓存1小时
      }
    });

  } else {
    // --- 情况 B: 部分文件下载 (Range 请求) ---
    console.log(`[DOWNLOAD] Serving partial content for task ${taskId}: bytes ${range.start}-${range.end}`);
    const object = await env.AUDIOS.get(key, {
      range: { offset: range.start, length: range.length }
    });
     if (object === null) {
        return new Response('Audio file range could not be retrieved.', { status: 404, headers: corsHeaders() });
    }
    
    return new Response(object.body, {
      status: 206, // Partial Content
      headers: {
        ...corsHeaders(),
        'Content-Type': 'audio/mpeg',
        'Content-Length': range.length.toString(),
        'Content-Range': `bytes ${range.start}-${range.end}/${totalSize}`,
        'Accept-Ranges': 'bytes',
        'Cache-Control': 'public, max-age=3600'
      }
    });
  }
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
第 3 步：在主路由中调用新函数

现在，找到您的 handleTTS 函数，并将其中的下载逻辑替换为对 handleDownload 的一次调用。

找到这部分代码 (大约在 handleTTS 函数的末尾):

// (旧代码，将被替换)
// 处理音频下载请求 GET /api/tts/download/{taskId}
// 注意：此接口保留作为备用下载方式，新任务优先使用R2直链下载
if (request.method === 'GET' && request.url.includes('/download/')) {
    const url = new URL(request.url);
    const taskId = url.pathname.split('/download/')[1];
    // ... [此处省略约 130 行的旧下载逻辑] ...
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

将其替换为下面这几行：

// (新代码)
// 处理音频下载请求 GET /api/tts/download/{taskId}
if (request.method === 'GET' && request.url.includes('/download/')) {
    try {
        // 直接调用新的、高效的下载处理器
        return await handleDownload(request, env);
    } catch (error) {
        console.error(`Error during download for request ${request.url}:`, error);
        return new Response(JSON.stringify({ error: 'Internal server error during download' }), {
            status: 500,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
    }
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
实施总结

添加 parseRange 辅助函数 到你的工具函数区。

添加 handleDownload 主函数，它包含了所有高效下载的逻辑。

在你的主路由 handleTTS 中，用对 handleDownload 的一次调用替换掉原来复杂的下载处理代码块。

这样修改后，您的下载接口就完全遵循了最佳实践，既能处理完整文件下载，也能高效地支持断点续传（Range 请求），同时将 Worker 的内存和 CPU 消耗降至最低。