"use client"

import React from 'react';
import { X } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import RichTextDialogueInput from "@/components/ui/rich-text-dialogue-input";

interface Voice {
  id: string;
  name: string;
  description: string;
  preview?: string;
  gender: string;
  language?: string;
}

interface DialogueLineProps {
  line: {
    id: number;
    voice: string;
    text: string;
  };
  index: number;
  voices: Voice[];
  voiceIconMapping: Record<string, string>;
  voiceIcons: string[];
  isActive: boolean;
  onSelectLine: (lineId: number) => void;
  onUpdateText: (lineId: number, text: string) => void;
  onRemoveLine: (lineId: number) => void;
  canRemove: boolean;
  onTextInputFocus?: (lineId: number) => void;
  onEditVoice?: (lineId: number) => void; // 新增：点击头像打开声音选择器
}

export const DialogueLine: React.FC<DialogueLineProps> = ({
  line,
  index,
  voices,
  voiceIconMapping,
  voiceIcons,
  isActive,
  onSelectLine,
  onUpdateText,
  onRemoveLine,
  canRemove,
  onTextInputFocus,
  onEditVoice
}) => {
  const currentVoice = voices.find(v => v.id === line.voice);

  return (
    <div
      data-dialogue-line-id={line.id}
      className={`group relative flex items-center gap-3 p-3 pl-8 rounded-xl border transition-all duration-300 ${
        isActive
          ? 'border-purple-400 bg-gradient-to-r from-purple-50/50 to-blue-50/50 shadow-md'
          : 'border-gray-200/80 hover:border-gray-300 hover:bg-gray-50/30'
      }`}>
      {/* 头像 */}
      <button
        onClick={() => onEditVoice?.(line.id)}
        className={`relative w-7 h-7 rounded-full overflow-hidden shadow-md transition-all duration-300 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-purple-400 flex-shrink-0 ${
          isActive ? 'ring-2 ring-purple-300 scale-105' : 'hover:ring-2 hover:ring-purple-200'
        }`}
        title={`点击为 ${currentVoice?.name || `说话者 ${index + 1}`} 选择声音`}
      >
        <img
          src={voiceIconMapping[line.voice] || voiceIcons[0]}
          alt={currentVoice?.name}
          className="w-full h-full object-cover"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
            const parent = target.parentElement;
            if (parent && currentVoice) {
              parent.innerHTML = `
                <div class="w-full h-full rounded-full flex items-center justify-center text-white font-bold text-sm ${
                  currentVoice.gender === "male"
                    ? "bg-gradient-to-br from-blue-500 to-blue-700"
                    : currentVoice.gender === "female"
                    ? "bg-gradient-to-br from-pink-500 to-pink-700"
                    : "bg-gradient-to-br from-gray-500 to-gray-700"
                }">
                  ${currentVoice.name.charAt(0).toUpperCase()}
                </div>
              `;
            }
          }}
        />

        {/* 激活状态指示器 */}
        {isActive && (
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-purple-500 rounded-full border border-white">
            <div className="absolute inset-0 bg-purple-400 rounded-full animate-ping opacity-75" />
          </div>
        )}
      </button>

      {/* 声音名称 */}
      <div className="flex-shrink-0 min-w-0 w-20">
        <div className={`font-medium text-sm truncate transition-colors duration-300 ${
          isActive ? 'text-purple-700' : 'text-gray-700'
        }`}>
          {currentVoice?.name || '未选择'}
        </div>
        {currentVoice && (
          <div className="text-xs text-gray-500">
            {currentVoice.gender === 'male' ? '♂ 男声' :
             currentVoice.gender === 'female' ? '♀ 女声' : '⚲ 中性'}
          </div>
        )}
      </div>

      {/* 文本输入框 */}
      <div className="flex-1 min-w-0">
        <RichTextDialogueInput
          value={line.text}
          onChange={(value: string) => onUpdateText(line.id, value)}
          onFocus={() => onTextInputFocus?.(line.id)}
          placeholder={`${currentVoice?.name || `说话者 ${index + 1}`} 说...`}
          className={`w-full bg-white/80 text-base py-2.5 px-3 border rounded-md transition-all duration-300 min-h-[42px] outline-none ${
            isActive
              ? 'border-purple-300 bg-white focus:border-purple-400'
              : 'border-gray-200 focus:border-blue-400 focus:bg-white'
          }`}
          maxLength={1000}
        />
      </div>

      {/* 字符计数 */}
      <div className="flex-shrink-0 text-xs text-gray-400 w-12 text-right">
        {line.text.length}
      </div>

      {/* 删除按钮 */}
      {canRemove && (
        <Button
          variant="ghost"
          size="icon"
          onClick={() => onRemoveLine(line.id)}
          className="flex-shrink-0 w-8 h-8 text-gray-400 hover:bg-red-100 hover:text-red-600 opacity-0 group-hover:opacity-100 transition-all duration-300"
          title="删除此对话"
        >
          <X className="w-4 h-4" />
        </Button>
      )}

      {/* 对话序号*/}
      <div className={`absolute -top--1 -left-0 w-6 h-6 text-white text-xs font-bold rounded-full flex items-center justify-center shadow-lg border-2 border-white transition-all duration-300 ${
        isActive
          ? 'bg-gradient-to-r from-purple-500 to-pink-500 scale-110 shadow-xl'
          : 'bg-gradient-to-r from-blue-500 to-purple-500 group-hover:scale-105'
      }`}>
        {index + 1}
      </div>
    </div>
  );
};
