Connected to tts-api, waiting for logs...
OPTIONS https://ttsapi.aispeak.top/api/tts/generate - Ok @ 2025/6/18 20:46:02
OPTIONS https://ttsapi.aispeak.top/api/tts/status/REDACTED - Ok @ 2025/6/18 20:46:03
GET https://ttsapi.aispeak.top/api/tts/status/REDACTED - Ok @ 2025/6/18 20:46:03
OPTIONS https://ttsapi.aispeak.top/api/tts/status/REDACTED - Ok @ 2025/6/18 20:46:05
GET https://ttsapi.aispeak.top/api/tts/status/REDACTED - Ok @ 2025/6/18 20:46:05
OPTIONS https://ttsapi.aispeak.top/api/tts/status/REDACTED - Ok @ 2025/6/18 20:46:08
GET https://ttsapi.aispeak.top/api/tts/status/REDACTED - Ok @ 2025/6/18 20:46:08
POST https://ttsapi.aispeak.top/api/tts/generate - Ok @ 2025/6/18 20:46:02
  (log) [TASK-START] 🆔 Created new TTS task: {
  taskId: '5d1a2b6b-fba0-4b70-8a41-55758a119112',
  username: 'sunshine',
  inputLength: 1993,
  voice: 'cjVigY5qzO86Huf0OWal',
  model: 'eleven_turbo_v2',
  parameters: { stability: 0.61, similarity_boost: 0.75, style: 0.44, speed: 1.03 },
  timestamp: '2025-06-18T12:46:02.749Z'
}
  (log) [TASK-START] ✅ Returning task ID to client: {
  taskId: '5d1a2b6b-fba0-4b70-8a41-55758a119112',
  status: 'processing',
  responseCode: 202
}
  (log) [TASK-START] 🚀 Starting async processing for task: 5d1a2b6b-fba0-4b70-8a41-55758a119112
  (log) [ASYNC-TASK] 🚀 Starting async audio processing for task 5d1a2b6b-fba0-4b70-8a41-55758a119112: {
  taskId: '5d1a2b6b-fba0-4b70-8a41-55758a119112',
  username: 'sunshine',
  inputLength: 1993,
  voice: 'cjVigY5qzO86Huf0OWal',
  model: 'eleven_turbo_v2',
  parameters: { stability: 0.61, similarity_boost: 0.75, style: 0.44, speed: 1.03 },
  timestamp: '2025-06-18T12:46:02.749Z'
}
  (log) [TASK-START] 📌 Using event.waitUntil() to ensure task completion for: 5d1a2b6b-fba0-4b70-8a41-55758a119112
  (log) [HEALTH-CHECK] ✅ R2 storage is healthy. Response time: 55ms
  (log) [ASYNC-TASK] 📝 Starting text processing for task 5d1a2b6b-fba0-4b70-8a41-55758a119112
  (log) [ASYNC-TASK] ✅ Text split into 5 chunks for task 5d1a2b6b-fba0-4b70-8a41-55758a119112
  (log) [ASYNC-TASK] 🎵 Starting audio generation for task 5d1a2b6b-fba0-4b70-8a41-55758a119112
  (log) [CONCURRENCY] Processing 5 chunks with dynamic concurrency...
  (log) [CONCURRENCY] Using 5 concurrent requests for 5 chunks
  (log) Processing completed: 5 successful, 0 failed
  (log) Successfully processed 5/5 chunks concurrently
  (log) [ASYNC-TASK] ✅ Audio generation completed for task 5d1a2b6b-fba0-4b70-8a41-55758a119112: 5/5 chunks, 1.64MB
  (log) [ASYNC-TASK] 🔗 Starting audio merging for task 5d1a2b6b-fba0-4b70-8a41-55758a119112
  (log) [ASYNC-TASK] ✅ Audio merging completed for task 5d1a2b6b-fba0-4b70-8a41-55758a119112: 5/5 chunks, total size: 1723886 bytes
  (log) [ASYNC-TASK] 💾 Starting R2 audio storage for task 5d1a2b6b-fba0-4b70-8a41-55758a119112
  (log) [R2-AUDIO] ✅ Audio stored for task 5d1a2b6b-fba0-4b70-8a41-55758a119112, size: 1.64MB
  (log) [ASYNC-TASK] ✅ Audio file stored successfully for task 5d1a2b6b-fba0-4b70-8a41-55758a119112
  (log) [ASYNC-TASK] 📝 Updating status to complete for task 5d1a2b6b-fba0-4b70-8a41-55758a119112
  (log) [ASYNC-TASK] 🎉 Task 5d1a2b6b-fba0-4b70-8a41-55758a119112 completed successfully in 8.14s, 1.64MB
OPTIONS https://ttsapi.aispeak.top/api/tts/status/REDACTED - Ok @ 2025/6/18 20:46:11
GET https://ttsapi.aispeak.top/api/tts/status/REDACTED - Ok @ 2025/6/18 20:46:12
  (log) [STATUS-CHECK] ✅ Task 5d1a2b6b-fba0-4b70-8a41-55758a119112 completed, returning download URL