# TTS 进度消息控制功能

## 功能说明

为了优化用户体验和减少不必要的网络流量，我们添加了一个可控制的进度消息开关功能。

## 环境变量配置

### ENABLE_PROGRESS_MESSAGES

控制是否发送详细的TTS处理进度消息到前端。

**配置位置**: `wrangler.toml` 文件的 `[vars]` 部分

**可选值**:
- `true` - 启用详细进度消息
- `false` - 禁用详细进度消息（默认）

**影响的消息类型**:
- "任务初始化..."
- "文本已分割为 X 个片段"
- "正在生成 X 个音频片段..."
- "正在合并音频..."
- "正在将文件存入云存储..."

## 使用方法

### 1. 禁用进度消息（默认状态）

```toml
# wrangler.toml
[vars]
ENABLE_PROGRESS_MESSAGES = false
```

此时前端不会收到详细的进度消息，但仍会收到：
- 任务完成消息 (`complete`)
- 错误消息 (`error`)
- 基本状态更新

### 2. 启用进度消息

```toml
# wrangler.toml
[vars]
ENABLE_PROGRESS_MESSAGES = true
```

此时前端会收到所有详细的进度消息。

### 3. 调试模式

当 `DEBUG = true` 时，无论 `ENABLE_PROGRESS_MESSAGES` 设置如何，进度消息都会在服务器控制台输出，方便调试。

## 部署更新

修改配置后需要重新部署：

```bash
cd 后端
wrangler deploy
```

## 技术实现

- 新增 `getProgressConfig()` 函数读取环境变量配置
- 在 `TtsTaskDo` 类中添加 `broadcastProgress()` 方法
- 替换原有的 `this.broadcast({ type: 'progress', ... })` 调用
- 保持所有现有功能逻辑不变

## 优势

1. **性能优化**: 减少不必要的WebSocket消息传输
2. **用户体验**: 避免过多的进度信息干扰用户
3. **灵活控制**: 可随时开启/关闭，无需修改代码
4. **向后兼容**: 不影响现有的错误处理和完成通知
5. **调试友好**: 调试模式下仍可查看详细进度
