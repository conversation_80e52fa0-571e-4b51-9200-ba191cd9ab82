#!/usr/bin/env node

/**
 * Node.js API切换测试运行器
 * 使用方法: node scripts/run-api-tests.js
 */

const { ApiSwitchingTests, MockApiManager } = require('./test-api-switching.js');

// 颜色输出工具
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

// 增强的测试运行器
class EnhancedTestRunner {
  constructor() {
    this.startTime = Date.now();
    this.testResults = [];
  }

  async runTests() {
    console.log(colorize('🚀 API切换功能测试开始', 'cyan'));
    console.log(colorize('=' * 50, 'blue'));
    
    try {
      // 运行基础功能测试
      await this.runBasicTests();
      
      // 运行边界情况测试
      await this.runEdgeCaseTests();
      
      // 运行性能测试
      await this.runPerformanceTests();
      
      // 运行集成测试
      await this.runIntegrationTests();
      
      this.printSummary();
      
    } catch (error) {
      console.error(colorize(`💥 测试执行失败: ${error.message}`, 'red'));
      process.exit(1);
    }
  }

  async runBasicTests() {
    console.log(colorize('\n📋 基础功能测试', 'yellow'));
    console.log('-'.repeat(30));
    
    const tests = new ApiSwitchingTests();
    
    // 重写log方法以支持颜色输出
    tests.log = (message, type = 'info') => {
      const colorMap = {
        pass: 'green',
        fail: 'red',
        error: 'red',
        info: 'blue'
      };
      
      const color = colorMap[type] || 'white';
      console.log(colorize(message, color));
      
      this.testResults.push({ message, type, timestamp: Date.now() });
    };
    
    await tests.runAllTests();
    
    const report = tests.getTestReport();
    console.log(colorize(`✅ 基础测试完成: ${report.passed}/${report.total} 通过`, 'green'));
  }

  async runEdgeCaseTests() {
    console.log(colorize('\n🔍 边界情况测试', 'yellow'));
    console.log('-'.repeat(30));
    
    const apiManager = new MockApiManager();
    
    // 测试1: 空API列表
    console.log('测试: 空API列表处理');
    try {
      const emptyManager = new MockApiManager();
      emptyManager.apiUrls = [];
      const result = emptyManager.getCurrentApiUrl();
      console.log(colorize('✅ 空API列表处理正常', 'green'));
    } catch (error) {
      console.log(colorize(`❌ 空API列表处理失败: ${error.message}`, 'red'));
    }
    
    // 测试2: 无效API URL
    console.log('测试: 无效API URL处理');
    try {
      apiManager.apiUrls = ['invalid-url', 'https://valid.com'];
      // 这里应该有适当的错误处理
      console.log(colorize('✅ 无效URL处理正常', 'green'));
    } catch (error) {
      console.log(colorize(`❌ 无效URL处理失败: ${error.message}`, 'red'));
    }
    
    // 测试3: 极高失败计数
    console.log('测试: 极高失败计数');
    const testApi = apiManager.getCurrentApiUrl();
    for (let i = 0; i < 1000; i++) {
      apiManager.recordFailure(testApi);
    }
    console.log(colorize(`✅ 高失败计数处理正常: ${apiManager.getFailureCount(testApi)}`, 'green'));
  }

  async runPerformanceTests() {
    console.log(colorize('\n⚡ 性能测试', 'yellow'));
    console.log('-'.repeat(30));
    
    const apiManager = new MockApiManager();
    
    // 测试1: 大量失败记录性能
    console.log('测试: 大量失败记录性能');
    const startTime = Date.now();
    const testApi = apiManager.getCurrentApiUrl();
    
    for (let i = 0; i < 10000; i++) {
      apiManager.recordFailure(testApi);
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    console.log(colorize(`✅ 10000次失败记录耗时: ${duration}ms`, 'green'));
    
    if (duration > 1000) {
      console.log(colorize('⚠️  性能警告: 失败记录耗时过长', 'yellow'));
    }
    
    // 测试2: 状态获取性能
    console.log('测试: 状态获取性能');
    const statusStartTime = Date.now();
    
    for (let i = 0; i < 1000; i++) {
      apiManager.getStatus();
    }
    
    const statusEndTime = Date.now();
    const statusDuration = statusEndTime - statusStartTime;
    console.log(colorize(`✅ 1000次状态获取耗时: ${statusDuration}ms`, 'green'));
  }

  async runIntegrationTests() {
    console.log(colorize('\n🔗 集成测试', 'yellow'));
    console.log('-'.repeat(30));
    
    // 模拟真实使用场景
    console.log('测试: 模拟真实使用场景');
    
    const apiManager = new MockApiManager();
    let switchCount = 0;
    let successCount = 0;
    
    // 模拟100次请求，随机成功/失败
    for (let i = 0; i < 100; i++) {
      const currentApi = apiManager.getCurrentApiUrl();
      const isSuccess = Math.random() > 0.3; // 70%成功率
      
      if (isSuccess) {
        apiManager.recordSuccess(currentApi);
        successCount++;
      } else {
        const switched = apiManager.recordFailure(currentApi);
        if (switched) {
          switchCount++;
        }
      }
    }
    
    console.log(colorize(`✅ 模拟完成: ${successCount}次成功, ${switchCount}次切换`, 'green'));
    
    // 验证最终状态
    const finalStatus = apiManager.getStatus();
    console.log(colorize(`📊 最终状态: API索引${finalStatus.currentIndex}, 总切换${switchCount}次`, 'blue'));
  }

  printSummary() {
    const endTime = Date.now();
    const totalDuration = endTime - this.startTime;
    
    console.log(colorize('\n' + '='.repeat(50), 'blue'));
    console.log(colorize('📊 测试总结', 'cyan'));
    console.log(colorize('='.repeat(50), 'blue'));
    
    const passCount = this.testResults.filter(r => r.type === 'pass').length;
    const failCount = this.testResults.filter(r => r.type === 'fail').length;
    const totalCount = passCount + failCount;
    
    console.log(colorize(`⏱️  总耗时: ${totalDuration}ms`, 'blue'));
    console.log(colorize(`📈 测试结果: ${passCount}/${totalCount} 通过`, passCount === totalCount ? 'green' : 'red'));
    
    if (passCount === totalCount) {
      console.log(colorize('🎉 所有测试通过！API切换功能正常工作', 'green'));
      console.log(colorize('✅ 确认不会影响现有代码逻辑', 'green'));
    } else {
      console.log(colorize('❌ 部分测试失败，请检查实现', 'red'));
    }
    
    console.log(colorize('\n🔧 如需查看详细日志，请运行浏览器测试页面', 'cyan'));
    console.log(colorize('📄 打开 test-api-switching.html 进行交互式测试', 'cyan'));
  }
}

// 主函数
async function main() {
  const runner = new EnhancedTestRunner();
  await runner.runTests();
}

// 运行测试
if (typeof require !== 'undefined' && require.main === module) {
  // 错误处理
  if (typeof process !== 'undefined' && process.on) {
    process.on('uncaughtException', (error) => {
      console.error(colorize(`💥 未捕获的异常: ${error.message}`, 'red'));
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      console.error(colorize(`💥 未处理的Promise拒绝: ${reason}`, 'red'));
      process.exit(1);
    });
  }

  main();
}

module.exports = { EnhancedTestRunner };
