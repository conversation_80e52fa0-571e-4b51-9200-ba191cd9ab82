# 字符数统计功能测试指南

## 功能概述

已成功实现用户字符数统计功能，包括：

1. **用户数据结构增强**：新用户自动包含 `usage` 字段
2. **字符数统计**：TTS任务成功后自动统计字符数
3. **月度重置**：每月自动重置月度统计
4. **向后兼容**：老用户自动初始化统计数据
5. **API接口**：提供 `/api/user/usage` 查询接口

## 新增的数据结构

### 用户数据结构
```javascript
{
  username: "testuser",
  passwordHash: "...",
  createdAt: 1234567890,
  quota: { daily: 100, used: 0, resetAt: 1234567890 },
  vip: { expireAt: 0, type: null },
  usage: {
    totalChars: 0,        // 历史总字符数
    monthlyChars: 0,      // 当月已用字符数  
    monthlyResetAt: 1234567890  // 下次月度重置时间戳
  }
}
```

## 测试步骤

### 1. 测试新用户注册
- 注册新用户，检查是否包含完整的 `usage` 和 `vip` 字段
- 验证初始值是否正确

### 2. 测试字符数统计
- 发起单人TTS任务，检查任务成功后字符数是否正确累加
- 发起多人对话任务，检查所有speaker文本长度总和是否正确统计

### 3. 测试用量查询API
```bash
# 获取用户用量信息
curl -X GET "https://ttsapi.aispeak.top/api/user/usage" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

预期响应：
```json
{
  "totalChars": 1250,
  "monthlyChars": 450,
  "monthlyResetAt": 1735689600000
}
```

### 4. 测试老用户兼容性
- 使用老用户账号（没有usage字段）
- 发起TTS任务，检查是否自动初始化usage字段
- 查询用量API，检查是否返回默认值

### 5. 测试月度重置
- 模拟时间到达重置点
- 检查monthlyChars是否重置为0
- 检查monthlyResetAt是否更新到下个月

## 关键实现点

### 1. 字符数计算
- **单人任务**：`input.length`
- **多人对话**：`dialogue.reduce((sum, speaker) => sum + (speaker.text ? speaker.text.length : 0), 0)`

### 2. 更新时机
- 在TTS任务成功完成后（音频文件存入R2之后）
- 失败的任务不计入统计

### 3. 并发安全
- 使用简单的读-修改-写模式
- 对于字符统计这种非关键数据，轻微误差可接受

### 4. 错误处理
- 统计更新失败不影响主任务流程
- 只记录错误日志，不抛出异常

## 监控要点

1. **日志监控**：关注 `[USAGE-UPDATE]` 相关日志
2. **数据一致性**：定期检查用户usage数据的合理性
3. **性能影响**：监控KV操作增加对整体性能的影响
4. **月度重置**：确保月度重置逻辑正常工作

## 故障排除

### 常见问题
1. **老用户没有usage字段**：代码已做兼容处理，会自动初始化
2. **统计不准确**：检查TTS任务是否真正成功完成
3. **月度重置失败**：检查时间戳计算逻辑

### 调试方法
1. 查看控制台日志中的 `[USAGE-UPDATE]` 消息
2. 直接查询KV中的用户数据结构
3. 测试 `/api/user/usage` 接口响应

## 部署注意事项

1. **无需数据迁移**：老用户数据会自动兼容
2. **向后兼容**：不会影响现有功能
3. **渐进式生效**：老用户在下次使用时开始统计
4. **安全部署**：可以安全地部署到生产环境
