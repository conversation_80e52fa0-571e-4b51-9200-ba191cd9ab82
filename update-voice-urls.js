const fs = require('fs');
const path = require('path');

// 读取CSV文件并解析
function parseCSV(csvPath) {
    const csvContent = fs.readFileSync(csvPath, 'utf-8');
    const lines = csvContent.split('\n');
    const urlMapping = {};
    
    // 跳过标题行，从第二行开始
    for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;
        
        const [fileName, url, voiceId] = line.split(',');
        
        // 忽略 #N/A 的记录和已经使用腾讯云URL的George
        if (voiceId && voiceId !== '#N/A' && voiceId !== 'Yko7PKHZNXotIFUBG7I9') {
            urlMapping[voiceId] = url;
        }
    }
    
    return urlMapping;
}

// 更新voices.json文件
function updateVoicesJson(voicesPath, urlMapping) {
    const voicesContent = fs.readFileSync(voicesPath, 'utf-8');
    const voices = JSON.parse(voicesContent);
    
    let updatedCount = 0;
    
    voices.forEach(voice => {
        if (urlMapping[voice.id]) {
            console.log(`更新声音: ${voice.name} (${voice.id})`);
            console.log(`  原URL: ${voice.preview}`);
            console.log(`  新URL: ${urlMapping[voice.id]}`);
            voice.preview = urlMapping[voice.id];
            updatedCount++;
        }
    });
    
    // 写回文件
    fs.writeFileSync(voicesPath, JSON.stringify(voices, null, 2), 'utf-8');
    
    return updatedCount;
}

// 主函数
function main() {
    const csvPath = path.join(__dirname, 'lib', 'cos-object-list-1750402939983.csv');
    const voicesPath = path.join(__dirname, 'lib', 'voices.json');
    
    console.log('开始解析CSV文件...');
    const urlMapping = parseCSV(csvPath);
    console.log(`解析到 ${Object.keys(urlMapping).length} 个有效的URL映射`);
    
    console.log('\n开始更新voices.json文件...');
    const updatedCount = updateVoicesJson(voicesPath, urlMapping);
    
    console.log(`\n✅ 更新完成！共更新了 ${updatedCount} 个声音的URL`);
}

// 执行脚本
main();
