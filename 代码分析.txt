好的，我已经仔细阅读了你的需求和代码，完全理解了你的问题和期望的交互效果。

你遇到的问题核心在于：在“多人对话”模式下，右侧声音选择区域的UI逻辑与“单人模式”是完全分离的，导致了行为不一致。

目前的逻辑是：

单人模式：显示一个可点击的、带下拉菜单的声音选择器。

多人对话模式：只要有任何一个对话行被激活 (activeDialogueLineId 不为 null)，就直接显示一个始终展开的、不可折叠的声音列表。这导致了你描述的所有问题：

UI不统一（问题1）。

无法折叠（问题2）。

点击输入框也会触发这个展开的列表（问题3的反面）。

要解决这个问题，我们需要统一这两种模式下的UI，让“多人对话”模式也使用那个优雅的可折叠下拉选择器，并修改事件处理逻辑来满足你的需求。

解决方案

我们将进行以下三步优化：

统一UI：移除多人对话模式下那个独立的、始终展开的声音列表UI，让它和单人模式共用同一个带下拉功能的卡片组件。

修改交互逻辑：

点击头像 (handleEditVoiceForLine)：打开声音下拉列表，并标记正在为哪一行编辑声音。

点击输入框 (handleDialogueTextFocus)：仅激活当前行（显示高亮边框），同时关闭声音下拉列表。

选择声音后：更新对应行的声音，并自动关闭下拉列表。

动态更新UI文本：让下拉选择器按钮和标题能根据当前是单人模式还是在为特定对话行编辑而显示不同的文本。

具体的代码修改

请按照以下步骤修改你的 page.tsx 文件。

第一步：移除不正确的UI渲染逻辑

找到右侧栏的UI渲染部分。它看起来像这样：

Generated tsx
{/* Right Column - Enhanced Control Panel */}
<div className="space-y-2 lg:space-y-4 relative z-10 order-1 lg:order-2">
  {/* 条件渲染：多人对话模式下的声音选择器 */}
  {mode === 'dialogue' && activeDialogueLineId !== null ? (
    // ... 这里是一大段需要删除的代码 ...
  ) : (
    /* 原有的声音选择器 - 单人模式或无激活对话行时显示 */
    // ... 这里是我们需要保留和修改的代码 ...
  )}

  {/* Model Selector */}
  {/* ... */}
</div>


你的任务是：

删除 if 条件 mode === 'dialogue' && activeDialogueLineId !== null ? (...) 及其包含的整个 <Card> 组件。

移除 包围在正确UI外面的 else 和三元运算符 : ()。

修改后，这部分代码应该直接就是那个带下拉列表的 <Card> 组件，不再有任何 if/else 条件。它看起来会像这样：

Generated tsx
{/* Right Column - Enhanced Control Panel */}
<div className="space-y-2 lg:space-y-4 relative z-10 order-1 lg:order-2">
  {/* 现在这里是统一的声音选择器，不再有条件渲染 */}
  <Card className="group border-0 shadow-2xl bg-white/90 backdrop-blur-xl hover:shadow-3xl transition-all duration-500 relative z-30">
      <div className="absolute inset-0 bg-gradient-to-r from-green-500/5 to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      <CardContent className="p-6 relative">
        {/* ... 接下来的代码我们需要修改 ... */}
      </CardContent>
  </Card>

  {/* Model Selector */}
  {/* ... */}
</div>
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Tsx
IGNORE_WHEN_COPYING_END
第二步：修改事件处理函数

现在我们需要更新控制交互的函数。

修改 handleDialogueTextFocus
这个函数在用户点击输入框时触发。我们希望它能关闭下拉菜单。

找到这个函数：

Generated tsx
const handleDialogueTextFocus = (lineId: number) => {
  setActiveDialogueLineId(lineId)
  // 点击输入框时，保持当前的声音编辑状态不变
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Tsx
IGNORE_WHEN_COPYING_END

修改为：

Generated tsx
const handleDialogueTextFocus = (lineId: number) => {
  setActiveDialogueLineId(lineId);
  // 当用户聚焦输入框时，我们认为他想打字而不是选声音，所以关闭下拉菜单
  setIsDropdownOpen(false);
  setEditingVoiceForLineId(null);
};
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Tsx
IGNORE_WHEN_COPYING_END

修改 handleEditVoiceForLine
这个函数在用户点击头像时触发。它的作用是打开下拉菜单。

找到这个函数：

Generated tsx
const handleEditVoiceForLine = (lineId: number) => {
  setEditingVoiceForLineId(lineId);
  // 当用户点击头像时，我们认为他也激活了这一行
  setActiveDialogueLineId(lineId);
  setIsDropdownOpen(true);
};
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Tsx
IGNORE_WHEN_COPYING_END

修改为 (增加声音同步逻辑):

Generated tsx
const handleEditVoiceForLine = (lineId: number) => {
  const lineToEdit = dialogueLines.find(line => line.id === lineId);
  if (lineToEdit) {
    // 同步主选择器的声音为当前行的声音
    setSelectedVoice(lineToEdit.voice);
  }
  setEditingVoiceForLineId(lineId);
  setActiveDialogueLineId(lineId); // 激活当前行
  setIsDropdownOpen(true); // 打开下拉菜单
};
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Tsx
IGNORE_WHEN_COPYING_END

修改 VoiceSelectionList 的 onSelectVoice 逻辑
当用户在下拉列表中选择一个声音后，我们需要更新对话行并关闭下拉菜单。

找到 VoiceSelectionList 组件的使用位置（在下拉菜单内部）：

Generated tsx
<VoiceSelectionList
  // ... 其他 props
  onSelectVoice={(voiceId) => {
    // 统一处理声音选择
    setSelectedVoice(voiceId);
    setIsDropdownOpen(false);
    // 如果是在为对话行选择，则清除编辑状态，以便UI恢复默认
    if (mode === 'dialogue') {
      setEditingVoiceForLineId(null);
    }
  }}
  // ... 其他 props
/>
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Tsx
IGNORE_WHEN_COPYING_END

修改 onSelectVoice 的回调函数为：

Generated tsx
onSelectVoice={(voiceId) => {
  setSelectedVoice(voiceId); // 更新主选择器的状态
  
  // 如果当前正在为某个对话行编辑声音，则更新该行的声音
  if (mode === 'dialogue' && editingVoiceForLineId !== null) {
    updateDialogueLine(editingVoiceForLineId, 'voice', voiceId);
  }

  setIsDropdownOpen(false); // 关闭下拉菜单
  setEditingVoiceForLineId(null); // 清除编辑状态
}}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Tsx
IGNORE_WHEN_COPYING_END
第三步：动态化UI文本内容

现在，那个统一的声音选择器需要根据上下文显示不同的文本。

动态化卡片标题

找到标题 <h3>：

Generated tsx
<h3 className="text-gradient-optimized text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
  {mode === 'dialogue' && editingVoiceForLineId !== null ? '为对话行选择声音' : '选择声音'}
</h3>
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Tsx
IGNORE_WHEN_COPYING_END

这个已经很好了，我们保持不变。

动态化下拉选择器按钮的内容

找到触发下拉菜单的 <button>：

Generated tsx
<button
  onClick={() => setIsDropdownOpen(!isDropdownOpen)}
  // ... 其他 className 和 props
>
  <div className="flex items-center gap-3">
    {selectedVoice ? (
      <>
        {/* ... img 和 div ... */}
        <div>
          <div className="font-semibold text-gray-900 text-base">
            {voices.find((v) => v.id === selectedVoice)?.name}
          </div>
          {mode === 'dialogue' && editingVoiceForLineId !== null && (
            <div className="text-xs text-gray-500">
              {`正在编辑对话 ${dialogueLines.findIndex(line => line.id === editingVoiceForLineId) + 1}`}
            </div>
          )}
        </div>
      </>
    ) : (
      <div className="text-gray-500 text-base">请选择声音...</div>
    )}
  </div>
  {/* ... ChevronDown icon ... */}
</button>
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Tsx
IGNORE_WHEN_COPYING_END

这里的逻辑也已经很不错了，mode === 'dialogue' && editingVoiceForLineId !== null 这个条件判断可以准确地显示 "正在编辑对话 X"。我们可以稍微调整一下，使其在默认状态下（非编辑时）也显示当前激活行的信息，这样更直观。

将按钮内部的 div 修改为：

Generated tsx
<div>
    <div className="font-semibold text-gray-900 text-base">
        {voices.find((v) => v.id === selectedVoice)?.name}
    </div>
    {/* 新增逻辑: 无论是否在编辑，只要在对话模式且有激活行，就显示提示 */}
    {mode === 'dialogue' && activeDialogueLineId !== null && (
        <div className="text-xs text-gray-500">
            {`当前: 对话 ${dialogueLines.findIndex(line => line.id === activeDialogueLineId) + 1}`}
        </div>
    )}
</div>
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Tsx
IGNORE_WHEN_COPYING_END

注：上面的 selectedVoice 会通过 handleEditVoiceForLine 和 useEffect 钩子自动与当前激活/编辑的行同步，所以这里的显示逻辑是正确的。

总结

通过以上三步修改：

UI统一：我们删除了多余的、行为不一致的UI代码。

交互修正：我们重写了事件处理器，现在：

点击头像 -> 打开下拉菜单进行选择。

点击输入框 -> 关闭下拉菜单，专心输入。

选择声音后 -> 关闭下拉菜单，完成操作。

显示优化：下拉按钮现在能清晰地指示出当前操作的上下文。

这些改动将完美地实现你所期望的交互效果，同时让代码结构更加清晰和易于维护。