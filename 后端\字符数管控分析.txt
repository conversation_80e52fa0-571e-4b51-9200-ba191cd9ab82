好的，我已经深入分析了您提供的 worker.js 后端代码。

结论

是的，完全可以实现。

您的代码架构非常出色，已经为实现“字符数配额管控”打下了坚实的基础。现有的用户数据结构、字符数统计函数和会员验证流程，使得增加字符数配额限制的改造工作清晰、直接且风险较低。

您只需要在现有逻辑上进行一些关键性的增强和调整，就能完美实现“每个套餐有独立的字符数配额，用完即止（即使会员未到期）”的功能。

详细分析

以下是我深入分析代码后得出的关键点，解释了为什么这个功能是可行的：

【已有基础】用户数据结构已包含用量统计：
在 /api/auth/register 接口中，创建新用户时，已经初始化了 usage 对象：

Generated javascript
usage: {
    totalChars: 0,
    monthlyChars: 0,
    monthlyResetAt: getNextMonthResetTimestamp()
}


这表明系统已经具备了存储和跟踪用户字符数的能力。虽然目前是按月统计，但这个结构可以轻松扩展。

【已有基础】字符数计算和更新逻辑已存在：
在 TtsTaskDo 这个 Durable Object 中，有一个非常关键的函数 updateUserUsage。它的作用就是：

从 KV 中读取指定用户的数据。

将本次任务消耗的字符数 (charCount) 累加到 totalChars 和 monthlyChars 上。

将更新后的用户数据写回 KV。

【已有基础】任务执行时已调用统计函数：
无论是单人配音 (runSingleTtsProcess) 还是多人对话 (runDialogueTtsProcess)，在任务成功完成后，都已经正确地计算了消耗的字符数，并调用了 await this.updateUserUsage(username, charCount);。

单人配音：const charCount = input.length;

多人对话：const charCount = dialogue.reduce(...);

【关键切入点】会员验证逻辑集中：
所有需要付费权限的操作，都会在任务开始时调用 checkVip 函数。目前，checkVip 主要检查两件事：

会员是否到期 (Date.now() > userData.vip.expireAt)。

会员等级是否满足功能要求（例如，多人对话需要 PRO 会员）。

这就是实现您需求的核心修改点。我们只需要在 checkVip 函数中，增加一项检查：验证用户的剩余字符数配额是否足够完成本次任务。

综上所述，我们不需要从零开始。我们要做的是：

在套餐配置中加入字符数额度。

在用户兑换卡密时，将这个额度赋予用户。

在 updateUserUsage 中同步更新已用配额。

在 checkVip 中增加对配额的检查。

具体实现方案 (修改建议)

以下是详细的、分步骤的实施指南，您可以参考这些建议来修改代码：

第1步：扩展套餐配置 PACKAGES

在 PACKAGES 常量中，为每个套餐类型增加一个 chars 字段，定义其包含的字符数配额。

修改位置： const PACKAGES

Generated javascript
// 卡密套餐配置
const PACKAGES = {
  // --- 标准套餐 ---
  'M': { days: 30, price: 15, chars: 50000 },     // 月套餐，5万字符
  'Q': { days: 90, price: 42, chars: 160000 },    // 季度套餐，16万字符
  'H': { days: 180, price: 82, chars: 350000 },   // 半年套餐，35万字符

  // --- 新增：PRO套餐 ---
  'PM': { days: 30, price: 45, chars: 200000 },   // 月度PRO，20万字符
  'PQ': { days: 90, price: 120, chars: 650000 },  // 季度PRO，65万字符
  'PH': { days: 180, price: 220, chars: 1500000 }, // 半年PRO，150万字符

  // --- 特殊套餐 ---
  'PT': { days: 0.0208, price: 0, chars: 1000 }   // 30分钟测试套餐，1000字符
};
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
第2步：修改用户数据结构（在兑换卡密时）

我们需要在用户的 vip 对象中增加字段来存储总配额和已用配额。

修改位置： useCard 函数

这个函数的逻辑需要增强，以处理字符配额的叠加。

Generated javascript
// 使用卡密
async function useCard(code, username, env) {
  // ... (前面的验证逻辑不变)

  try {
    const userDataString = await env.USERS.get(`user:${username}`);
    const userData = userDataString ? JSON.parse(userDataString) : {}; // 容错处理

    // 获取新套餐的配置
    const newPackage = PACKAGES[card.t];
    if (!newPackage) {
      throw new Error('未知的套餐类型');
    }

    // 初始化VIP对象（如果不存在）
    if (!userData.vip) {
      userData.vip = {
        expireAt: 0,
        type: null,
        quotaChars: 0, // 新增：总配额
        usedChars: 0   // 新增：已用配额
      };
    }

    // 1. 计算新的到期时间
    const baseTime = Math.max(userData.vip.expireAt || 0, Date.now());
    userData.vip.expireAt = baseTime + (newPackage.days * 86400000);

    // 2. 叠加字符数配额
    // 如果旧套餐已过期，则配额清零重算；否则在剩余配额基础上叠加
    const isExpired = Date.now() > (userData.vip.expireAt || 0);
    const oldRemainingChars = isExpired ? 0 : Math.max(0, userData.vip.quotaChars - userData.vip.usedChars);
    userData.vip.quotaChars = oldRemainingChars + newPackage.chars;
    userData.vip.usedChars = 0; // 每次充值后，已用计数器清零，额度合并到总额度
    
    // 3. 更新套餐类型
    userData.vip.type = card.t;

    // ... (标记卡密为已使用)
    card.s = 'used';
    card.u = username;
    card.a = Date.now();

    // 保存更新
    await env.USERS.put(`user:${username}`, JSON.stringify(userData));
    await env.CARDS.put(`card:${code}`, JSON.stringify(card));

    return userData.vip;
  } catch (error) {
    // ... (错误回滚逻辑不变)
    throw error;
  }
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
第3步：同步更新已用配额

修改 updateUserUsage 函数，让它在更新月度用量的同时，也更新 vip 对象中的已用字符数。

修改位置： TtsTaskDo 类中的 updateUserUsage 方法

Generated javascript
async updateUserUsage(username, charCount) {
    if (!username || charCount <= 0) {
      return;
    }

    try {
      const userKey = `user:${username}`;
      const userDataString = await this.env.USERS.get(userKey);
      if (!userDataString) { /* ... */ return; }
      const userData = JSON.parse(userDataString);

      // --- START: 新增逻辑 ---
      // 更新VIP套餐内的已用配额
      if (userData.vip) {
        // 初始化字段以兼容老数据
        if (userData.vip.usedChars === undefined) {
          userData.vip.usedChars = 0;
        }
        userData.vip.usedChars += charCount;
      }
      // --- END: 新增逻辑 ---

      // ... (原有的月度和总用量统计逻辑保持不变)
      if (!userData.usage) { /* ... */ }
      const now = Date.now();
      if (now >= userData.usage.monthlyResetAt) { /* ... */ }
      userData.usage.totalChars += charCount;
      userData.usage.monthlyChars += charCount;

      await this.env.USERS.put(userKey, JSON.stringify(userData));
      console.log(`[USAGE-UPDATE] ... Added: ${charCount}, VIP Used: ${userData.vip?.usedChars}, Monthly total: ${userData.usage.monthlyChars}`);

    } catch (error) {
      console.error(`[USAGE-UPDATE] Failed to update usage for user ${username}:`, error);
    }
  }
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
第4步：增强 checkVip 函数（核心改动）

这是最关键的一步。修改 checkVip，让它接受本次任务所需的字符数作为参数，并进行配额检查。

修改位置： 全局函数 checkVip

Generated javascript
/**
 * 升级版VIP权限检查 (包含字符数配额检查)
 * @param {string} username - 用户名
 * @param {number} requestedChars - 本次任务请求的字符数
 * @param {object} env - Cloudflare环境变量
 * @param {('STANDARD'|'PRO')} [requiredTier='STANDARD'] - 要求最低的会员等级
 */
async function checkVip(username, requestedChars, env, requiredTier = 'STANDARD') {
  const userDataString = await env.USERS.get(`user:${username}`);
  if (!userDataString) {
      throw new Error('用户不存在', { cause: 'quota' });
  }
  const userData = JSON.parse(userDataString);
  const vip = userData.vip;

  // 1. 基础检查：是否有会员资格
  if (!vip) {
    throw new Error('请先开通会员', { cause: 'quota' });
  }

  // 2. 时间检查：会员是否已过期
  if (Date.now() > vip.expireAt) {
    throw new Error('会员已过期，请续费', { cause: 'quota' });
  }

  // --- START: 新增字符数配额检查 ---
  const currentUsed = vip.usedChars || 0;
  const totalQuota = vip.quotaChars || 0;
  
  if (currentUsed + requestedChars > totalQuota) {
    const remaining = Math.max(0, totalQuota - currentUsed);
    throw new Error(`字符数配额不足。剩余 ${remaining} 字符，本次需要 ${requestedChars} 字符。请升级或续费套餐。`, { cause: 'quota' });
  }
  // --- END: 新增字符数配额检查 ---

  // 3. 等级检查：如果要求PRO权限 (逻辑不变)
  if (requiredTier === 'PRO') {
    const userTier = vip.type;
    if (!userTier || !userTier.startsWith('P')) {
      throw new Error('此功能需要PRO会员权限', { cause: 'quota' });
    }
  }

  // 4. 测试套餐的特殊逻辑 (逻辑不变)
  // ...
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
第5步：调整 checkVip 的调用位置

因为 checkVip 现在需要 requestedChars 参数，所以必须在计算出字符数之后再调用它。

修改位置： TtsTaskDo 类中的 runSingleTtsProcess 和 runDialogueTtsProcess 方法

对于 runSingleTtsProcess:

Generated javascript
async runSingleTtsProcess() {
    // ...
    const { taskId, input, voice, /*...,*/ token } = this.taskData;
    const voiceId = await getVoiceId(voice, this.env);

    try {
        const username = await verifyToken(token, this.env);

        // --- START: 调整顺序 ---
        // 1. 先计算字符数
        const charCount = input.length;
        
        // 2. 再调用增强后的 checkVip 进行检查
        await checkVip(username, charCount, this.env);
        // --- END: 调整顺序 ---

        // ... (后续任务流程不变)

        // 在任务完成时，调用 updateUserUsage (这一步不变)
        await this.updateUserUsage(username, charCount);
        // ...
    } catch (error) { /* ... */ }
  }
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

对于 runDialogueTtsProcess:

Generated javascript
async runDialogueTtsProcess() {
    // ...
    const { taskId, dialogue, /*...,*/ token } = this.taskData;

    try {
      const username = await verifyToken(token, this.env);
      
      // --- START: 调整顺序 ---
      // 1. 先计算总字符数
      const charCount = dialogue.reduce((sum, speaker) => sum + (speaker.text ? speaker.text.length : 0), 0);
      
      // 2. 再调用增强后的 checkVip 进行检查 (PRO权限)
      await checkVip(username, charCount, this.env, 'PRO');
      // --- END: 调整顺序 ---

      // ... (后续任务流程不变)

      // 在任务完成时，调用 updateUserUsage (这一步不变)
      await this.updateUserUsage(username, charCount);
      // ...
    } catch (error) { /* ... */ }
  }
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

完成以上五步修改并部署后，您的会员系统就将具备完善的、基于字符数配额的管控能力。