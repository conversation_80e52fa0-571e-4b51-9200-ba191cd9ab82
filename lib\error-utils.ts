/**
 * 前端错误处理工具函数
 * 配合后端的结构化错误响应，提供统一的错误识别和处理
 */

// 认证相关错误码
export const AUTH_ERROR_CODES = {
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID', 
  TOKEN_TYPE_INVALID: 'TOKEN_TYPE_INVALID',
  NO_TOKEN: 'NO_TOKEN',
  AUTH_ERROR: 'AUTH_ERROR'
} as const

// 错误类型
export type AuthErrorCode = typeof AUTH_ERROR_CODES[keyof typeof AUTH_ERROR_CODES]

// 扩展的错误对象接口
export interface ExtendedError extends Error {
  code?: string
}

/**
 * 判断是否为认证相关错误
 * @param error 错误对象
 * @returns 是否为认证错误
 */
export function isAuthError(error: any): boolean {
  // 优先检查错误码（最可靠）
  if (error?.code) {
    return Object.values(AUTH_ERROR_CODES).includes(error.code)
  }
  
  // 兼容性检查：检查错误消息
  if (error?.message) {
    const message = error.message.toLowerCase()
    return message.includes('token') ||
           message.includes('expired') ||
           message.includes('unauthorized') ||
           message.includes('401') ||
           message.includes('登录') ||
           message.includes('refresh')
  }
  
  return false
}

/**
 * 判断是否为token过期错误
 * @param error 错误对象
 * @returns 是否为token过期错误
 */
export function isTokenExpiredError(error: any): boolean {
  // 优先检查错误码
  if (error?.code === AUTH_ERROR_CODES.TOKEN_EXPIRED) {
    return true
  }
  
  // 兼容性检查：检查错误消息
  if (error?.message) {
    const message = error.message.toLowerCase()
    return message.includes('token expired') ||
           message.includes('expired') ||
           message.includes('过期')
  }
  
  return false
}

/**
 * 获取用户友好的错误消息
 * @param error 错误对象
 * @returns 用户友好的错误消息
 */
export function getAuthErrorMessage(error: any): {
  title: string
  description: string
  shouldRedirect: boolean
} {
  if (isAuthError(error)) {
    return {
      title: "认证失败",
      description: "会话已过期，正在跳转到登录页面...",
      shouldRedirect: true
    }
  }
  
  // 业务错误或网络错误
  return {
    title: "操作失败", 
    description: error?.message || "请检查网络连接后重试",
    shouldRedirect: false
  }
}

/**
 * 统一的认证错误处理函数
 * @param error 错误对象
 * @param onAuthError 认证错误回调（可选）
 * @returns 处理结果
 */
export function handleAuthError(
  error: any,
  onAuthError?: () => void
): {
  isAuthError: boolean
  message: string
  shouldRedirect: boolean
} {
  const isAuth = isAuthError(error)
  
  if (isAuth && onAuthError) {
    onAuthError()
  }
  
  const errorInfo = getAuthErrorMessage(error)
  
  return {
    isAuthError: isAuth,
    message: errorInfo.description,
    shouldRedirect: errorInfo.shouldRedirect
  }
}

/**
 * 为页面组件提供的错误处理hook辅助函数
 * @param error 错误对象
 * @returns UI状态更新信息
 */
export function getErrorUIState(error: any): {
  statusDisplay: string
  toastTitle: string
  toastDescription: string
  variant: 'default' | 'destructive'
} {
  if (isAuthError(error)) {
    return {
      statusDisplay: "请重新登录",
      toastTitle: "认证失败",
      toastDescription: "会话已过期，正在跳转到登录页面...",
      variant: 'destructive'
    }
  }
  
  // 检查是否为业务错误（如卡密无效等）
  if (error?.message?.includes('卡密')) {
    return {
      statusDisplay: "充值失败",
      toastTitle: "充值失败", 
      toastDescription: "卡密无效或已使用，请检查后重试",
      variant: 'destructive'
    }
  }
  
  // 网络或其他错误
  return {
    statusDisplay: "获取失败",
    toastTitle: "操作失败",
    toastDescription: error?.message || "请检查网络连接后重试",
    variant: 'destructive'
  }
}
