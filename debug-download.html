<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS下载调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        input, button {
            padding: 10px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 TTS下载调试工具</h1>
        
        <div class="section">
            <h3>1. 测试R2连接</h3>
            <button onclick="testR2Connection()">测试R2连接</button>
            <div id="r2Result" class="result"></div>
        </div>

        <div class="section">
            <h3>2. 列出音频文件</h3>
            <button onclick="listAudioFiles()">列出所有音频文件</button>
            <div id="listResult" class="result"></div>
        </div>

        <div class="section">
            <h3>3. 测试下载</h3>
            <input type="text" id="taskIdInput" placeholder="输入任务ID" style="width: 300px;">
            <button onclick="testDownload()">测试下载</button>
            <div id="downloadResult" class="result"></div>
        </div>

        <div class="section">
            <h3>4. 检查任务状态</h3>
            <input type="text" id="statusTaskIdInput" placeholder="输入任务ID" style="width: 300px;">
            <button onclick="checkTaskStatus()">检查状态</button>
            <div id="statusResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'https://ttsapi.aispeak.top';
        
        // 获取token（需要先登录）
        function getToken() {
            return localStorage.getItem('access_token');
        }

        // 通用请求函数
        async function makeRequest(url, options = {}) {
            const token = getToken();
            if (!token) {
                throw new Error('请先登录获取token');
            }

            const headers = {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
                ...options.headers
            };

            const response = await fetch(url, {
                ...options,
                headers
            });

            return response;
        }

        // 测试R2连接
        async function testR2Connection() {
            const resultDiv = document.getElementById('r2Result');
            resultDiv.textContent = '测试中...';
            resultDiv.className = 'result info';

            try {
                const response = await makeRequest(`${API_BASE}/api/tts/test-r2`);
                const data = await response.json();
                
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = response.ok ? 'result success' : 'result error';
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 列出音频文件
        async function listAudioFiles() {
            const resultDiv = document.getElementById('listResult');
            resultDiv.textContent = '获取中...';
            resultDiv.className = 'result info';

            try {
                const response = await makeRequest(`${API_BASE}/api/tts/list-audios`);
                const data = await response.json();
                
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = response.ok ? 'result success' : 'result error';
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 测试下载
        async function testDownload() {
            const taskId = document.getElementById('taskIdInput').value.trim();
            const resultDiv = document.getElementById('downloadResult');
            
            if (!taskId) {
                resultDiv.textContent = '请输入任务ID';
                resultDiv.className = 'result error';
                return;
            }

            resultDiv.textContent = '下载测试中...';
            resultDiv.className = 'result info';

            try {
                const response = await makeRequest(`${API_BASE}/api/tts/download/${taskId}`);
                
                const info = {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    contentLength: response.headers.get('content-length'),
                    contentType: response.headers.get('content-type')
                };

                if (response.ok && response.headers.get('content-type') === 'audio/mpeg') {
                    const blob = await response.blob();
                    info.blobSize = blob.size;
                    info.blobType = blob.type;
                    
                    // 尝试创建下载链接
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `test-audio-${taskId}.mp3`;
                    a.click();
                    URL.revokeObjectURL(url);
                    
                    info.downloadAttempted = true;
                } else {
                    const text = await response.text();
                    info.responseText = text;
                }
                
                resultDiv.textContent = JSON.stringify(info, null, 2);
                resultDiv.className = response.ok ? 'result success' : 'result error';
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 检查任务状态
        async function checkTaskStatus() {
            const taskId = document.getElementById('statusTaskIdInput').value.trim();
            const resultDiv = document.getElementById('statusResult');
            
            if (!taskId) {
                resultDiv.textContent = '请输入任务ID';
                resultDiv.className = 'result error';
                return;
            }

            resultDiv.textContent = '检查中...';
            resultDiv.className = 'result info';

            try {
                const response = await makeRequest(`${API_BASE}/api/tts/status/${taskId}`);
                const data = await response.json();
                
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = response.ok ? 'result success' : 'result error';
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 页面加载时检查token
        window.onload = function() {
            const token = getToken();
            if (!token) {
                alert('请先在主页面登录，然后再使用此调试工具');
            }
        };
    </script>
</body>
</html>
