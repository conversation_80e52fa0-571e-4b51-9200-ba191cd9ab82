# API自动切换功能实现说明

## 🎯 功能概述

已成功实现API地址自动切换功能，当音频生成失败时，系统能够自动切换到备用API地址，提供更高的服务可用性。

## 📋 实现内容

### 1. **API管理器 (ApiManager)**
- **位置**: `lib/api.ts`
- **功能**: 
  - 管理多个API地址
  - 跟踪失败计数
  - 自动切换逻辑
  - 成功状态重置

### 2. **环境变量配置**
- **位置**: `.env.local`
- **新增配置**:
  ```env
  # 备用API地址（逗号分隔）
  NEXT_PUBLIC_BACKUP_API_URLS=https://ttss.aispeak.top,https://tts-api.panxuchao19951206.workers.dev
  
  # API切换失败阈值（默认3次失败后切换）
  NEXT_PUBLIC_API_SWITCH_THRESHOLD=3
  ```

### 3. **智能重试策略**
- **位置**: `app/page.tsx`
- **策略层次**:
  1. **数据中心切换** - 优先在同一API下切换数据中心
  2. **API地址切换** - 数据中心切换失败后切换到备用API
  3. **完全失败** - 所有重试策略用尽后显示最终错误

### 4. **WebSocket URL构建增强**
- **位置**: `app/page.tsx` - `buildWsUrl`函数
- **功能**: 
  - 支持动态API地址
  - 自动获取当前API
  - 支持自定义API地址

### 5. **后台无感切换**
- **特性**:
  - 完全后台运行，用户无感知
  - 自动记录和管理API状态
  - 智能切换决策
  - 保持用户体验流畅

## 🔧 核心特性

### **自动切换逻辑**
```typescript
// 智能重试策略
if (shouldTryApiSwitch && hasBackupApis) {
  // 尝试API切换重试
  retryWithApiSwitch(taskData)
} else {
  // 尝试常规自动重试
  startAutoRetry()
}
```

### **失败检测**
- 网络连接错误
- 超时错误
- 重复失败（达到阈值）
- 特定错误关键词

### **成功恢复**
- WebSocket连接成功时重置失败计数
- 自动记录API状态
- 智能负载均衡

## 🎨 用户体验

### **透明切换**
- 用户完全无感知的API切换
- 后台智能状态管理
- 自动故障恢复

### **静默运行**
- 无UI显示，保持界面简洁
- 后台记录切换日志
- 自动负载均衡

## 📊 配置说明

### **默认配置**
- 主API: `https://ttssapi.aispeak.top`
- 备用API: `https://ttss.aispeak.top`, `https://tts-api.panxuchao19951206.workers.dev`
- 切换阈值: 3次失败
- 切换冷却: 30秒

### **自定义配置**
通过环境变量可以自定义：
- 备用API地址列表
- 失败切换阈值
- 是否启用切换功能

## 🔄 工作流程

### **正常流程**
1. 用户点击生成音频
2. 使用主API建立WebSocket连接
3. 连接成功，记录API成功状态
4. 正常处理音频生成

### **失败切换流程**
1. API请求失败，记录失败计数
2. 达到阈值，触发API切换
3. 切换到备用API
4. 重新建立WebSocket连接
5. 继续音频生成流程

### **恢复流程**
- 备用API成功后重置失败计数
- 可选择手动重置到主API
- 自动负载均衡

## 🛡️ 兼容性保证

### **向后兼容**
- 未配置备用API时降级为原有行为
- 保留所有现有功能逻辑
- 不影响现有用户体验

### **渐进增强**
- 在现有重试基础上添加API切换
- 保留数据中心切换机制
- 智能策略选择

## 🔍 监控和调试

### **日志记录**
- API切换事件记录
- 失败原因分析
- 性能指标跟踪

### **状态监控**
- 后台API状态记录
- 失败计数统计
- 切换历史日志

## 🚀 使用建议

### **生产环境**
1. 配置可靠的备用API地址
2. 根据实际情况调整切换阈值
3. 监控API切换频率
4. 定期检查API健康状态

### **开发环境**
1. 通过浏览器控制台查看切换日志
2. 模拟API失败场景
3. 测试不同网络条件下的表现

## ✅ 测试验证

可以通过以下方式验证功能：
- 查看浏览器控制台的切换日志
- 模拟网络故障测试自动切换
- 观察错误提示中的切换信息

这个实现确保了在不影响现有功能的前提下，大大提高了系统的可用性和用户体验。
