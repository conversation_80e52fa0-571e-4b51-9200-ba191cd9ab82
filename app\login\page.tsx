"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Eye, EyeOff, Mail, Lock, Mic, ArrowRight, AlertCircle, CheckCircle, Send } from "lucide-react"
import { auth } from "@/lib/auth-service"
import { Label } from "@/components/ui/label"

export default function LoginPage() {
  const [formData, setFormData] = useState({
    username: "",
    password: "",
    rememberMe: false,
  })
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const [loginError, setLoginError] = useState("")
  const [isPageLoaded, setIsPageLoaded] = useState(false)

  // 忘记密码相关状态
  const [showForgotPasswordDialog, setShowForgotPasswordDialog] = useState(false)
  const [forgotPasswordStep, setForgotPasswordStep] = useState<'email' | 'verify' | 'success'>('email')
  const [forgotPasswordData, setForgotPasswordData] = useState({
    email: "",
    code: "",
    newPassword: "",
    confirmPassword: ""
  })
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [forgotPasswordError, setForgotPasswordError] = useState("")
  const [isSendingCode, setIsSendingCode] = useState(false)
  const [isResettingPassword, setIsResettingPassword] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [isClient, setIsClient] = useState(false)

  // 客户端挂载状态管理 - 解决水合失败问题
  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    setIsPageLoaded(true)
    // Check if user is already logged in
    const savedCredentials = localStorage.getItem("rememberedCredentials")
    if (savedCredentials) {
      const { username, rememberMe } = JSON.parse(savedCredentials)
      setFormData((prev) => ({ ...prev, username, rememberMe }))
    }
  }, [])

  // 倒计时效果
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000)
      return () => clearTimeout(timer)
    }
  }, [countdown])

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const validatePassword = (password: string): boolean => {
    return password.length >= 2
  }

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {}

    // Username validation
    if (!formData.username.trim()) {
      newErrors.username = "用户名或邮箱不能为空"
    }

    // Password validation
    if (!formData.password.trim()) {
      newErrors.password = "密码不能为空"
    } else if (!validatePassword(formData.password)) {
      newErrors.password = "密码长度至少为2位"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
    // Clear specific field error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }))
    }
    // Clear login error when user modifies form
    if (loginError) {
      setLoginError("")
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsLoading(true)
    setLoginError("")

    try {
      // 调用真实的登录API
      await auth.login({
        username: formData.username,
        password: formData.password,
      })

      // Save credentials if remember me is checked
      if (formData.rememberMe) {
        localStorage.setItem(
          "rememberedCredentials",
          JSON.stringify({
            username: formData.username,
            rememberMe: true,
          }),
        )
      } else {
        localStorage.removeItem("rememberedCredentials")
      }

      // Redirect to main application
      window.location.href = "/"
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "登录失败，请检查网络连接后重试"
      setLoginError(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleForgotPassword = () => {
    setShowForgotPasswordDialog(true)
    setForgotPasswordStep('email')
    setForgotPasswordError("")
  }

  // 发送重置密码验证码
  const handleSendResetCode = async () => {
    setForgotPasswordError("")

    // 验证邮箱格式
    if (!forgotPasswordData.email) {
      setForgotPasswordError("请输入邮箱地址")
      return
    }

    if (!validateEmail(forgotPasswordData.email)) {
      setForgotPasswordError("请输入有效的邮箱地址")
      return
    }

    setIsSendingCode(true)

    try {
      await auth.forgotPassword({ email: forgotPasswordData.email })
      setForgotPasswordStep('verify')
      setCountdown(60) // 60秒倒计时
    } catch (error: any) {
      console.error('Send reset code error:', error)
      setForgotPasswordError(error.message || '发送验证码失败，请重试')
    } finally {
      setIsSendingCode(false)
    }
  }

  // 重置密码
  const handleResetPassword = async () => {
    setForgotPasswordError("")

    // 验证表单
    if (!forgotPasswordData.code || !forgotPasswordData.newPassword || !forgotPasswordData.confirmPassword) {
      setForgotPasswordError("请填写所有字段")
      return
    }

    if (forgotPasswordData.newPassword !== forgotPasswordData.confirmPassword) {
      setForgotPasswordError("新密码和确认密码不匹配")
      return
    }

    if (forgotPasswordData.newPassword.length < 6) {
      setForgotPasswordError("新密码长度不能少于6位")
      return
    }

    setIsResettingPassword(true)

    try {
      await auth.resetPassword({
        email: forgotPasswordData.email,
        code: forgotPasswordData.code,
        newPassword: forgotPasswordData.newPassword
      })
      setForgotPasswordStep('success')
    } catch (error: any) {
      console.error('Reset password error:', error)
      setForgotPasswordError(error.message || '重置密码失败，请重试')
    } finally {
      setIsResettingPassword(false)
    }
  }

  // 关闭忘记密码对话框
  const closeForgotPasswordDialog = () => {
    setShowForgotPasswordDialog(false)
    setForgotPasswordStep('email')
    setForgotPasswordData({
      email: "",
      code: "",
      newPassword: "",
      confirmPassword: ""
    })
    setForgotPasswordError("")
    setCountdown(0)
  }

  // 预定义的粒子位置和动画参数 - 解决水合失败问题
  const loginParticleConfigs = [
    { left: 20, top: 25, duration: 11 },
    { left: 75, top: 40, duration: 9 },
    { left: 45, top: 65, duration: 12 },
    { left: 85, top: 20, duration: 10 },
    { left: 30, top: 80, duration: 13 },
    { left: 65, top: 50, duration: 8 },
    { left: 55, top: 35, duration: 11 },
    { left: 35, top: 85, duration: 9 }
  ];

  const FloatingParticles = () => (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {loginParticleConfigs.map((config, i) => (
        <div
          key={i}
          className="absolute w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-20 animate-float"
          style={{
            left: `${config.left}%`,
            top: `${config.top}%`,
            animationDelay: `${i * 2}s`,
            animationDuration: `${config.duration}s`,
          }}
        />
      ))}
    </div>
  )

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center p-6 relative overflow-hidden">
      {isClient && <FloatingParticles />}

      {/* Animated Background Elements */}
      <div className="absolute top-20 right-20 w-72 h-72 bg-gradient-to-r from-blue-200/20 to-purple-200/20 rounded-full blur-3xl animate-pulse" />
      <div
        className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-r from-purple-200/20 to-pink-200/20 rounded-full blur-3xl animate-pulse"
        style={{ animationDelay: "2s" }}
      />

      <div
        className={`w-full max-w-md transition-all duration-1000 ${isPageLoaded ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"}`}
      >
        <Card className="border-0 shadow-2xl bg-white/90 backdrop-blur-xl relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5" />

          <CardHeader className="text-center pb-8 relative">
            <div className="flex justify-center mb-6">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl blur-lg opacity-50 animate-pulse" />
                <div className="relative p-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl shadow-xl">
                  <Mic className="w-8 h-8 text-white" />
                </div>
              </div>
            </div>
            <CardTitle className="text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-2">
              欢迎回来
            </CardTitle>
            <p className="text-gray-600 text-lg">登录您的 AI 语音工作室账户</p>
          </CardHeader>

          <CardContent className="relative">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Login Error Message */}
              {loginError && (
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-3 animate-fade-in">
                  <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0" />
                  <p className="text-red-700 text-sm">{loginError}</p>
                </div>
              )}

              {/* Username/Email Field */}
              <div className="space-y-4">
                <div>
                  <Label
                    htmlFor="username"
                    className="text-sm font-medium text-gray-700 group-hover:text-indigo-600 transition-colors duration-300"
                  >
                    用户名或邮箱
                  </Label>
                  <div className="mt-1 relative rounded-2xl shadow-sm">
                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-4">
                      <Mail className="h-5 w-5 text-gray-400" />
                    </div>
                    <Input
                      id="username"
                      name="username"
                      type="text"
                      autoComplete="username"
                      required
                      placeholder="请输入您的用户名或邮箱"
                      value={formData.username}
                      onChange={(e) => handleInputChange("username", e.target.value)}
                      className={`pl-12 ${errors.username ? "border-red-500 ring-red-500" : "border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"}`}
                    />
                  </div>
                  {errors.username && <p className="mt-2 text-sm text-red-600">{errors.username}</p>}
                </div>

                {/* Password Field */}
                <div className="space-y-2">
                  <label htmlFor="password" className="block text-sm font-semibold text-gray-700">
                    密码
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock className="h-5 w-5 text-gray-400" />
                    </div>
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={formData.password}
                      onChange={(e) => handleInputChange("password", e.target.value)}
                      placeholder="请输入您的密码"
                      className={`pl-10 pr-12 h-12 text-lg border-2 transition-all duration-300 ${
                        errors.password
                          ? "border-red-400 focus:border-red-500 focus:ring-red-100"
                          : "border-gray-200 focus:border-blue-400 focus:ring-blue-100"
                      }`}
                      disabled={isLoading}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center hover:bg-gray-50 rounded-r-lg transition-colors duration-200"
                      disabled={isLoading}
                    >
                      {showPassword ? (
                        <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                      ) : (
                        <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                      )}
                    </button>
                  </div>
                  {errors.password && (
                    <p className="text-red-500 text-sm flex items-center gap-2 animate-fade-in">
                      <AlertCircle className="w-4 h-4" />
                      {errors.password}
                    </p>
                  )}
                </div>
              </div>

              {/* Remember Me & Forgot Password */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="rememberMe"
                    checked={formData.rememberMe}
                    onCheckedChange={(checked) => handleInputChange("rememberMe", checked as boolean)}
                    disabled={isLoading}
                    className="data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
                  />
                  <label htmlFor="rememberMe" className="text-sm text-gray-700 cursor-pointer select-none">
                    记住我
                  </label>
                </div>
                <button
                  type="button"
                  onClick={handleForgotPassword}
                  className="text-sm text-blue-600 hover:text-blue-800 hover:underline transition-colors duration-200"
                  disabled={isLoading}
                >
                  忘记密码？
                </button>
              </div>

              {/* Login Button */}
              <Button
                type="submit"
                disabled={isLoading}
                className="w-full h-12 text-lg font-bold bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 hover:from-blue-600 hover:via-purple-600 hover:to-pink-600 disabled:from-gray-400 disabled:to-gray-500 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:scale-100 relative overflow-hidden group"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="relative z-10 flex items-center justify-center gap-3">
                  {isLoading ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      登录中...
                    </>
                  ) : (
                    <>
                      登录
                      <ArrowRight className="w-5 h-5" />
                    </>
                  )}
                </div>
              </Button>

              {/* Demo Credentials Info */}
            </form>

            {/* Sign Up Link */}
            <div className="mt-8 text-center">
              <p className="text-gray-600">
                还没有账户？{" "}
                <button
                  onClick={() => (window.location.href = "/register")}
                  className="text-blue-600 hover:text-blue-800 font-semibold hover:underline transition-colors duration-200"
                >
                  立即注册
                </button>
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Back to Home Link */}
        <div className="mt-6 text-center">
          <button
            onClick={() => (window.location.href = "/")}
            className="text-gray-600 hover:text-gray-800 text-sm hover:underline transition-colors duration-200"
          >
            ← 返回首页
          </button>
        </div>
      </div>

      {/* 忘记密码对话框 */}
      <Dialog open={showForgotPasswordDialog} onOpenChange={setShowForgotPasswordDialog}>
        <DialogContent className="sm:max-w-md bg-white/95 backdrop-blur-xl border border-gray-200">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
              <Mail className="w-5 h-5 text-blue-600" />
              {forgotPasswordStep === 'email' && '重置密码'}
              {forgotPasswordStep === 'verify' && '验证邮箱'}
              {forgotPasswordStep === 'success' && '重置成功'}
            </DialogTitle>
            <DialogDescription className="text-gray-600">
              {forgotPasswordStep === 'email' && '请输入您的邮箱地址，我们将发送验证码'}
              {forgotPasswordStep === 'verify' && '请输入验证码和新密码'}
              {forgotPasswordStep === 'success' && '密码重置成功，请使用新密码登录'}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* 第一步：输入邮箱 */}
            {forgotPasswordStep === 'email' && (
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">邮箱地址</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-5 w-5 text-gray-400" />
                  </div>
                  <Input
                    type="email"
                    value={forgotPasswordData.email}
                    onChange={(e) => setForgotPasswordData(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="请输入您的邮箱地址"
                    className="pl-10 border-2 transition-all duration-300 focus:border-blue-400 focus:ring-4 focus:ring-blue-50"
                  />
                </div>
              </div>
            )}

            {/* 第二步：验证码和新密码 */}
            {forgotPasswordStep === 'verify' && (
              <>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">验证码</label>
                  <Input
                    type="text"
                    value={forgotPasswordData.code}
                    onChange={(e) => setForgotPasswordData(prev => ({ ...prev, code: e.target.value }))}
                    placeholder="请输入6位验证码"
                    className="border-2 transition-all duration-300 focus:border-blue-400 focus:ring-4 focus:ring-blue-50"
                    maxLength={6}
                  />
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-gray-500">验证码已发送至 {forgotPasswordData.email}</p>
                    <button
                      type="button"
                      onClick={handleSendResetCode}
                      disabled={countdown > 0 || isSendingCode}
                      className="text-sm text-blue-600 hover:text-blue-800 disabled:text-gray-400 disabled:cursor-not-allowed"
                    >
                      {countdown > 0 ? `${countdown}s后重发` : '重新发送'}
                    </button>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">新密码</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock className="h-5 w-5 text-gray-400" />
                    </div>
                    <Input
                      type={showNewPassword ? "text" : "password"}
                      value={forgotPasswordData.newPassword}
                      onChange={(e) => setForgotPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                      placeholder="请输入新密码（至少6位）"
                      className="pl-10 pr-10 border-2 transition-all duration-300 focus:border-blue-400 focus:ring-4 focus:ring-blue-50"
                    />
                    <button
                      type="button"
                      onClick={() => setShowNewPassword(!showNewPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      {showNewPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">确认新密码</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock className="h-5 w-5 text-gray-400" />
                    </div>
                    <Input
                      type={showConfirmPassword ? "text" : "password"}
                      value={forgotPasswordData.confirmPassword}
                      onChange={(e) => setForgotPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                      placeholder="请再次输入新密码"
                      className="pl-10 pr-10 border-2 transition-all duration-300 focus:border-blue-400 focus:ring-4 focus:ring-blue-50"
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                  </div>
                </div>
              </>
            )}

            {/* 第三步：成功提示 */}
            {forgotPasswordStep === 'success' && (
              <div className="text-center py-6">
                <div className="relative mx-auto w-16 h-16 mb-4">
                  <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full blur-md opacity-50" />
                  <div className="relative p-4 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full shadow-lg">
                    <CheckCircle className="w-8 h-8 text-white" />
                  </div>
                </div>
                <p className="text-gray-600 mb-2">密码重置成功！</p>
                <p className="text-sm text-gray-500">请使用新密码登录您的账户</p>
              </div>
            )}

            {/* 错误提示 */}
            {forgotPasswordError && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2 text-red-700">
                  <AlertCircle className="w-4 h-4" />
                  <span className="text-sm font-medium">{forgotPasswordError}</span>
                </div>
              </div>
            )}
          </div>

          <DialogFooter className="flex gap-2">
            <Button
              variant="outline"
              onClick={closeForgotPasswordDialog}
              disabled={isSendingCode || isResettingPassword}
              className="flex-1"
            >
              {forgotPasswordStep === 'success' ? '关闭' : '取消'}
            </Button>

            {forgotPasswordStep === 'email' && (
              <Button
                onClick={handleSendResetCode}
                disabled={isSendingCode}
                className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white"
              >
                {isSendingCode ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    发送中...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Send className="w-4 h-4" />
                    发送验证码
                  </div>
                )}
              </Button>
            )}

            {forgotPasswordStep === 'verify' && (
              <Button
                onClick={handleResetPassword}
                disabled={isResettingPassword}
                className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white"
              >
                {isResettingPassword ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    重置中...
                  </div>
                ) : (
                  "确认重置"
                )}
              </Button>
            )}

            {forgotPasswordStep === 'success' && (
              <Button
                onClick={closeForgotPasswordDialog}
                className="flex-1 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white"
              >
                返回登录
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
        }
        @keyframes fade-in {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-float {
          animation: float 8s ease-in-out infinite;
        }
        .animate-fade-in {
          animation: fade-in 0.3s ease-out forwards;
        }
        /* 应用动画优化 */
        .animate-float {
          will-change: transform;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }
        .animate-fade-in {
          will-change: transform, opacity;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }
      `}</style>
    </div>
  )
}
