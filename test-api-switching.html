<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API切换功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #4CAF50;
            color: white;
        }
        
        .btn-primary:hover {
            background: #45a049;
        }
        
        .btn-secondary {
            background: #2196F3;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #1976D2;
        }
        
        .btn-danger {
            background: #f44336;
            color: white;
        }
        
        .btn-danger:hover {
            background: #d32f2f;
        }
        
        .status-panel {
            background: #f5f5f5;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .status-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #2196F3;
        }
        
        .status-label {
            font-weight: 600;
            color: #666;
            font-size: 12px;
            text-transform: uppercase;
            margin-bottom: 5px;
        }
        
        .status-value {
            font-size: 16px;
            color: #333;
        }
        
        .log-container {
            background: #1e1e1e;
            color: #fff;
            border-radius: 8px;
            padding: 20px;
            height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-pass { color: #4CAF50; }
        .log-fail { color: #f44336; }
        .log-info { color: #2196F3; }
        .log-error { color: #ff9800; }
        
        .test-summary {
            background: #e8f5e8;
            border: 1px solid #4CAF50;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .test-summary.failed {
            background: #ffeaea;
            border-color: #f44336;
        }
        
        .api-list {
            background: white;
            border-radius: 6px;
            padding: 10px;
            margin-top: 10px;
        }
        
        .api-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            margin: 5px 0;
            background: #f9f9f9;
            border-radius: 4px;
        }
        
        .api-current {
            background: #e3f2fd;
            border-left: 4px solid #2196F3;
        }
        
        .failure-count {
            background: #ffebee;
            color: #c62828;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 API切换功能测试</h1>
        
        <div class="test-controls">
            <button class="btn-primary" onclick="runAllTests()">🚀 运行所有测试</button>
            <button class="btn-secondary" onclick="runSingleTest()">🔧 单项测试</button>
            <button class="btn-secondary" onclick="simulateFailure()">💥 模拟失败</button>
            <button class="btn-secondary" onclick="simulateSuccess()">✅模拟成功</button>
            <button class="btn-danger" onclick="resetApiManager()">🔄 重置状态</button>
            <button class="btn-secondary" onclick="clearLogs()">🗑️ 清空日志</button>
        </div>
        
        <div class="status-panel">
            <h3>📊 当前API状态</h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-label">当前API</div>
                    <div class="status-value" id="current-api">-</div>
                </div>
                <div class="status-item">
                    <div class="status-label">API索引</div>
                    <div class="status-value" id="api-index">-</div>
                </div>
                <div class="status-item">
                    <div class="status-label">总API数</div>
                    <div class="status-value" id="total-apis">-</div>
                </div>
                <div class="status-item">
                    <div class="status-label">最后切换时间</div>
                    <div class="status-value" id="last-switch">-</div>
                </div>
            </div>
            
            <div class="api-list" id="api-list">
                <!-- API列表将在这里动态生成 -->
            </div>
        </div>
        
        <div>
            <h3>📝 测试日志</h3>
            <div class="log-container" id="log-container">
                <!-- 日志将在这里显示 -->
            </div>
        </div>
        
        <div id="test-summary" class="test-summary" style="display: none;">
            <!-- 测试总结将在这里显示 -->
        </div>
    </div>

    <script src="scripts/test-api-switching.js"></script>
    <script>
        let apiManager;
        let testRunner;
        
        // 初始化
        function init() {
            apiManager = new MockApiManager();
            testRunner = new ApiSwitchingTests();
            updateStatus();
            log('🎯 API切换测试环境已初始化', 'info');
        }
        
        // 更新状态显示
        function updateStatus() {
            const status = apiManager.getStatus();
            
            document.getElementById('current-api').textContent = new URL(status.currentApi).hostname;
            document.getElementById('api-index').textContent = `${status.currentIndex + 1} / ${status.totalApis}`;
            document.getElementById('total-apis').textContent = status.totalApis;
            document.getElementById('last-switch').textContent = status.lastSwitchTime ? 
                new Date(status.lastSwitchTime).toLocaleTimeString() : '从未切换';
            
            // 更新API列表
            const apiList = document.getElementById('api-list');
            apiList.innerHTML = '<h4>API列表:</h4>';
            
            apiManager.getAllApiUrls().forEach((url, index) => {
                const div = document.createElement('div');
                div.className = `api-item ${index === status.currentIndex ? 'api-current' : ''}`;
                
                const failureCount = apiManager.getFailureCount(url);
                div.innerHTML = `
                    <span>${new URL(url).hostname} ${index === status.currentIndex ? '(当前)' : ''}</span>
                    <span class="failure-count">失败: ${failureCount}</span>
                `;
                
                apiList.appendChild(div);
            });
        }
        
        // 日志记录
        function log(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // 运行所有测试
        async function runAllTests() {
            log('🚀 开始运行所有测试...', 'info');
            
            try {
                // 重置状态
                apiManager = new MockApiManager();
                testRunner = new ApiSwitchingTests();
                
                // 重写testRunner的log方法以显示在页面上
                testRunner.log = log;
                
                await testRunner.runAllTests();
                
                const report = testRunner.getTestReport();
                showTestSummary(report, true);
                
                updateStatus();
                
            } catch (error) {
                log(`💥 测试失败: ${error.message}`, 'error');
                showTestSummary(null, false);
            }
        }
        
        // 显示测试总结
        function showTestSummary(report, success) {
            const summaryDiv = document.getElementById('test-summary');
            summaryDiv.style.display = 'block';
            summaryDiv.className = `test-summary ${success ? '' : 'failed'}`;
            
            if (success && report) {
                summaryDiv.innerHTML = `
                    <h4>✅ 测试完成</h4>
                    <p>总计: ${report.total} 项 | 通过: ${report.passed} 项 | 失败: ${report.failed} 项</p>
                    <p>API切换功能正常工作，不会影响现有代码逻辑！</p>
                `;
            } else {
                summaryDiv.innerHTML = `
                    <h4>❌ 测试失败</h4>
                    <p>请检查控制台获取详细错误信息</p>
                `;
            }
        }
        
        // 模拟失败
        function simulateFailure() {
            const currentApi = apiManager.getCurrentApiUrl();
            const switched = apiManager.recordFailure(currentApi);
            
            if (switched) {
                log(`🔄 API切换: ${new URL(currentApi).hostname} -> ${new URL(apiManager.getCurrentApiUrl()).hostname}`, 'info');
            } else {
                log(`💥 记录失败: ${new URL(currentApi).hostname} (${apiManager.getFailureCount(currentApi)}/3)`, 'error');
            }
            
            updateStatus();
        }
        
        // 模拟成功
        function simulateSuccess() {
            const currentApi = apiManager.getCurrentApiUrl();
            apiManager.recordSuccess(currentApi);
            log(`✅ 记录成功: ${new URL(currentApi).hostname}，失败计数已重置`, 'pass');
            updateStatus();
        }
        
        // 重置API管理器
        function resetApiManager() {
            apiManager.resetToFirstApi();
            log('🔄 已重置到主API', 'info');
            updateStatus();
        }
        
        // 清空日志
        function clearLogs() {
            document.getElementById('log-container').innerHTML = '';
            document.getElementById('test-summary').style.display = 'none';
        }
        
        // 单项测试
        function runSingleTest() {
            log('🔧 运行单项测试: 失败记录和切换', 'info');
            
            try {
                const mainApi = apiManager.getCurrentApiUrl();
                
                // 记录3次失败触发切换
                for (let i = 1; i <= 3; i++) {
                    const switched = apiManager.recordFailure(mainApi);
                    if (switched) {
                        log(`✅ 第${i}次失败触发切换成功`, 'pass');
                        break;
                    } else {
                        log(`📝 第${i}次失败，未触发切换`, 'info');
                    }
                }
                
                updateStatus();
                
            } catch (error) {
                log(`💥 单项测试失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时初始化
        window.onload = init;
    </script>
</body>
</html>
