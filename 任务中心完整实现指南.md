# 任务中心完整实现指南

## 📋 概述

本文档详细描述了AI语音工作室中任务中心组件的完整实现逻辑、UI设计风格和技术架构，为在其他项目中复刻此功能提供全面指导。

## 🏗️ 整体架构

### 核心设计理念
- **组件化设计**：采用React forwardRef模式，支持外部控制
- **状态管理**：结合React Hooks和localStorage实现数据持久化
- **API集成**：与后端服务深度集成，支持实时状态同步
- **用户体验优先**：注重交互反馈和视觉效果

### 技术栈
- **前端框架**：React 18+ with TypeScript
- **UI组件库**：Radix UI + Tailwind CSS
- **图标库**：Lucide React
- **状态管理**：React Hooks (useState, useEffect, useCallback)
- **数据持久化**：localStorage
- **网络请求**：Fetch API

## 📊 数据结构设计

### 任务对象接口
```typescript
interface Task {
  taskId: string;              // 任务唯一标识符
  createdAt: number;           // 创建时间戳
  status?: 'processing' | 'complete' | 'failed' | 'unknown';  // 任务状态
  downloadUrl?: string;        // 下载链接（完成状态时）
  isRefreshing?: boolean;      // 是否正在刷新状态
}
```

### 组件Props接口
```typescript
interface TaskCenterProps {
  className?: string;          // 自定义样式类名
}

export interface TaskCenterRef {
  addTask: (taskId: string) => void;
  updateTaskStatus: (taskId: string, status: 'processing' | 'complete' | 'failed', downloadUrl?: string) => void;
}
```

## 🎨 UI设计风格

### 设计主题
- **主色调**：紫色渐变系 (`purple-500` → `purple-600` → `indigo-600`)
- **辅助色**：蓝色系（刷新按钮）、绿色系（成功状态）、红色系（失败状态）
- **设计风格**：现代化、扁平化、微拟物化
- **动画效果**：平滑过渡、悬停缩放、加载旋转

### 按钮设计规范
```css
/* 主按钮样式 */
.task-center-button {
  background: linear-gradient(to right, #8b5cf6, #7c3aed, #4f46e5);
  border-radius: 1rem;
  padding: 0.5rem 1rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  transform: scale(1);
}

.task-center-button:hover {
  background: linear-gradient(to right, #7c3aed, #6d28d9, #4338ca);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}
```

### 状态颜色编码
- **处理中**：橙色系 (`bg-orange-100 text-orange-600`)
- **已完成**：绿色系 (`bg-green-100 text-green-600`)
- **失败**：红色系 (`bg-red-100 text-red-600`)
- **未知**：灰色系 (`bg-gray-100 text-gray-600`)

## 🔧 核心功能实现

### 1. 数据持久化
```typescript
const STORAGE_KEY = 'tts_task_center_tasks';

// 保存到本地存储
const saveTasksToStorage = useCallback((tasks: Task[]) => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(tasks));
  } catch (error) {
    console.error('Failed to save tasks to localStorage:', error);
  }
}, []);

// 从本地存储加载
useEffect(() => {
  try {
    const savedTasks = localStorage.getItem(STORAGE_KEY);
    if (savedTasks) {
      const tasks = JSON.parse(savedTasks);
      setTaskList(tasks);
    }
  } catch (error) {
    console.error('Failed to load tasks from localStorage:', error);
  }
}, []);
```

### 2. 任务状态管理
```typescript
// 添加新任务
const addTask = useCallback((taskId: string) => {
  const newTask: Task = {
    taskId,
    createdAt: Date.now(),
    status: 'processing'
  };
  
  setTaskList(prev => {
    const updatedTasks = [newTask, ...prev];
    saveTasksToStorage(updatedTasks);
    return updatedTasks;
  });
}, [saveTasksToStorage]);

// 更新任务状态
const updateTaskStatus = useCallback((taskId: string, status: 'processing' | 'complete' | 'failed', downloadUrl?: string) => {
  setTaskList(prev => {
    const updatedTasks = prev.map(task =>
      task.taskId === taskId
        ? { ...task, status, downloadUrl, isRefreshing: false }
        : task
    );
    saveTasksToStorage(updatedTasks);
    return updatedTasks;
  });
}, [saveTasksToStorage]);
```

### 3. API集成
```typescript
// 获取任务状态
const fetchTaskStatus = useCallback(async (taskId: string) => {
  try {
    // 设置刷新状态
    setTaskList(prev => prev.map(task =>
      task.taskId === taskId ? { ...task, isRefreshing: true } : task
    ));

    const token = localStorage.getItem('access_token');
    if (!token) {
      throw new Error('未找到访问令牌，请重新登录');
    }

    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/tts/status/${taskId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    // 更新任务状态
    if (data.status === 'complete') {
      updateTaskStatus(taskId, 'complete', data.audioUrl);
    } else if (data.status === 'processing') {
      updateTaskStatus(taskId, 'processing');
    } else if (data.status === 'failed') {
      updateTaskStatus(taskId, 'failed');
    } else {
      updateTaskStatus(taskId, 'failed');
    }

  } catch (error: any) {
    console.error('Failed to fetch task status:', error);
    // 重置刷新状态
    setTaskList(prev => prev.map(task =>
      task.taskId === taskId ? { ...task, isRefreshing: false } : task
    ));
  }
}, [updateTaskStatus]);
```

## 🎯 交互功能特性

### 1. 搜索过滤
```typescript
const [searchQuery, setSearchQuery] = useState("");
const [filteredTasks, setFilteredTasks] = useState<Task[]>([]);

// 搜索过滤逻辑
useEffect(() => {
  if (!searchQuery.trim()) {
    setFilteredTasks(taskList);
  } else {
    const filtered = taskList.filter(task =>
      task.taskId.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredTasks(filtered);
  }
}, [taskList, searchQuery]);
```

### 2. 复制功能
```typescript
const [copiedTaskId, setCopiedTaskId] = useState<string | null>(null);

const copyTaskId = useCallback(async (taskId: string) => {
  try {
    await navigator.clipboard.writeText(taskId);
    setCopiedTaskId(taskId);
    setTimeout(() => setCopiedTaskId(null), 2000);
  } catch (error) {
    console.error('Failed to copy task ID:', error);
  }
}, []);
```

### 3. 下载功能
```typescript
const downloadAudio = useCallback((downloadUrl: string, taskId: string) => {
  try {
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `audio_${taskId}.mp3`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error('Failed to download audio:', error);
  }
}, []);
```

## 📱 响应式设计

### 断点设置
- **移动端**：< 768px
- **平板端**：768px - 1024px  
- **桌面端**：> 1024px

### 适配策略
```css
/* 移动端优化 */
@media (max-width: 768px) {
  .task-center-dialog {
    max-width: 95vw;
    max-height: 90vh;
  }
  
  .task-card {
    padding: 0.75rem;
  }
}

/* 桌面端优化 */
@media (min-width: 1024px) {
  .task-center-dialog {
    max-width: 56rem; /* 4xl */
  }
}
```

## 🔄 状态同步机制

### 外部组件集成
```typescript
// 在父组件中使用
const taskCenterRef = useRef<TaskCenterRef>(null);

// WebSocket消息处理
const handleWebSocketMessage = (data: any) => {
  switch (data.type) {
    case 'complete':
      const completedTaskId = data.taskId;
      if (completedTaskId && taskCenterRef.current) {
        taskCenterRef.current.updateTaskStatus(completedTaskId, 'complete', data.downloadUrl);
      }
      break;
    case 'error':
      const erroredTaskId = data.taskId;
      if (erroredTaskId && taskCenterRef.current) {
        taskCenterRef.current.updateTaskStatus(erroredTaskId, 'failed');
      }
      break;
  }
};
```

## 🎨 完整UI组件结构

### 主按钮组件
```jsx
<button
  onClick={() => setShowTaskCenter(true)}
  className="relative overflow-hidden px-4 py-2 text-sm font-semibold rounded-2xl transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform group text-white bg-gradient-to-r from-purple-500 via-purple-600 to-indigo-600 hover:from-purple-600 hover:via-purple-700 hover:to-indigo-700 hover:scale-105 backdrop-blur-sm"
>
  {/* 渐变光效背景 */}
  <div className="absolute inset-0 bg-gradient-to-r from-purple-400/20 via-purple-500/20 to-indigo-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
  
  {/* 图标和文字 */}
  <List className="w-4 h-4 relative z-10" />
  <span className="relative z-10">任务中心</span>
  
  {/* 任务数量徽章 */}
  {taskList.length > 0 && (
    <span className="relative z-10 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] h-[18px] flex items-center justify-center">
      {taskList.length > 99 ? '99+' : taskList.length}
    </span>
  )}
</button>
```

### 模态框组件
```jsx
<Dialog open={showTaskCenter} onOpenChange={setShowTaskCenter}>
  <DialogContent className="max-w-4xl max-h-[80vh] flex flex-col">
    <DialogHeader>
      <DialogTitle className="flex items-center gap-2 text-xl font-bold">
        <List className="w-5 h-5 text-purple-600" />
        任务中心
        <span className="text-sm font-normal text-gray-500">
          ({taskList.length} 个任务)
        </span>
      </DialogTitle>
    </DialogHeader>

    {/* 搜索和操作栏 */}
    <div className="flex items-center gap-3 py-4 border-b border-gray-100">
      <div className="relative flex-1">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
        <Input
          placeholder="搜索任务ID..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 h-10 border-2 border-gray-200 focus:border-purple-400 focus:ring-2 focus:ring-purple-50"
        />
      </div>
      <Button
        onClick={clearAllTasks}
        variant="outline"
        size="sm"
        className="h-10 px-4 border-2 border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300"
      >
        <X className="w-4 h-4 mr-1" />
        清空
      </Button>
    </div>

    {/* 任务列表 */}
    <div className="flex-1 overflow-y-auto">
      {filteredTasks.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-64 text-gray-500">
          <List className="w-12 h-12 mb-4 text-gray-300" />
          <h3 className="text-lg font-semibold mb-2">暂无任务</h3>
          <p className="text-sm text-center max-w-sm">
            {searchQuery ? "没有找到匹配的任务" : "您还没有创建任何任务，开始使用AI语音转换功能来创建您的第一个任务吧！"}
          </p>
        </div>
      ) : (
        <div className="space-y-3 p-1">
          {filteredTasks.map((task, index) => (
            <TaskCard key={task.taskId} task={task} index={index} />
          ))}
        </div>
      )}
    </div>
  </DialogContent>
</Dialog>
```

### 任务卡片组件
```jsx
const TaskCard = ({ task, index }) => (
  <div className="bg-white border-2 border-gray-100 rounded-xl p-4 hover:border-purple-200 hover:shadow-md transition-all duration-200">
    <div className="flex items-start justify-between">
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-2">
          <span className="text-xs font-medium text-purple-600 bg-purple-100 px-2 py-1 rounded-lg">
            #{index + 1}
          </span>
          <span className="text-xs text-gray-500 flex items-center gap-1">
            <Clock className="w-3 h-3" />
            {formatTime(task.createdAt)}
          </span>
          {/* 状态指示器 */}
          {task.status && (
            <span className={`text-xs px-2 py-1 rounded-lg font-medium ${getStatusStyle(task.status)}`}>
              {getStatusText(task.status)}
            </span>
          )}
        </div>

        {/* 任务ID */}
        <div className="flex items-center gap-2 mb-3">
          <span className="text-sm font-mono text-gray-700 bg-gray-50 px-2 py-1 rounded border truncate max-w-[200px]">
            {task.taskId}
          </span>
          <button
            onClick={() => copyTaskId(task.taskId)}
            className="p-1 rounded hover:bg-gray-100 transition-colors duration-200"
            title="复制任务ID"
          >
            {copiedTaskId === task.taskId ? (
              <Check className="w-3 h-3 text-green-600" />
            ) : (
              <Copy className="w-3 h-3 text-gray-400" />
            )}
          </button>
        </div>
      </div>

      {/* 操作按钮区域 */}
      <div className="flex items-center gap-2 ml-4">
        {/* 下载按钮 */}
        {task.status === 'complete' && task.downloadUrl && (
          <button
            onClick={() => downloadAudio(task.downloadUrl!, task.taskId)}
            className="p-2 rounded-lg bg-green-100 text-green-600 hover:bg-green-200 hover:text-green-700 transition-all duration-200"
            title="下载音频"
          >
            <Download className="w-4 h-4" />
          </button>
        )}

        {/* 刷新按钮 */}
        <button
          onClick={() => fetchTaskStatus(task.taskId)}
          disabled={task.isRefreshing}
          className={`p-2 rounded-lg transition-all duration-200 flex items-center justify-center ${
            task.isRefreshing
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-blue-100 text-blue-600 hover:bg-blue-200 hover:text-blue-700 cursor-pointer'
          }`}
          title="刷新任务状态"
        >
          <RefreshCw className={`w-4 h-4 ${task.isRefreshing ? 'animate-spin' : ''}`} />
        </button>
      </div>
    </div>
  </div>
);
```

## 🛠️ 工具函数

### 时间格式化
```typescript
const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return '刚刚';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}分钟前`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}小时前`;
  } else {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days}天前`;
  }
};
```

### 状态样式获取
```typescript
const getStatusStyle = (status: string): string => {
  switch (status) {
    case 'processing':
      return 'bg-orange-100 text-orange-600';
    case 'complete':
      return 'bg-green-100 text-green-600';
    case 'failed':
      return 'bg-red-100 text-red-600';
    default:
      return 'bg-gray-100 text-gray-600';
  }
};

const getStatusText = (status: string): string => {
  switch (status) {
    case 'processing':
      return '处理中';
    case 'complete':
      return '已完成';
    case 'failed':
      return '失败';
    default:
      return '未知';
  }
};
```

## 🔧 部署配置

### 环境变量
```env
NEXT_PUBLIC_API_URL=https://your-api-domain.com
```

### 依赖包
```json
{
  "dependencies": {
    "react": "^18.0.0",
    "lucide-react": "^0.263.1",
    "@radix-ui/react-dialog": "^1.0.4",
    "tailwindcss": "^3.3.0"
  }
}
```

### Tailwind CSS 配置
```javascript
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'gradient': 'gradient 15s ease infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-20px)' },
        },
        gradient: {
          '0%, 100%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' },
        },
      },
    },
  },
  plugins: [],
}
```

## 📝 使用示例

### 基础使用
```tsx
import TaskCenter, { TaskCenterRef } from '@/components/TaskCenter';

function App() {
  const taskCenterRef = useRef<TaskCenterRef>(null);

  const handleTaskCreated = (taskId: string) => {
    if (taskCenterRef.current) {
      taskCenterRef.current.addTask(taskId);
    }
  };

  const handleTaskCompleted = (taskId: string, downloadUrl: string) => {
    if (taskCenterRef.current) {
      taskCenterRef.current.updateTaskStatus(taskId, 'complete', downloadUrl);
    }
  };

  return (
    <div>
      <TaskCenter ref={taskCenterRef} />
      {/* 其他组件 */}
    </div>
  );
}
```

### 与WebSocket集成
```typescript
const setupWebSocket = () => {
  const ws = new WebSocket('wss://your-websocket-url');

  ws.onmessage = (event) => {
    const data = JSON.parse(event.data);

    switch (data.type) {
      case 'task_created':
        if (taskCenterRef.current) {
          taskCenterRef.current.addTask(data.taskId);
        }
        break;

      case 'task_completed':
        if (taskCenterRef.current) {
          taskCenterRef.current.updateTaskStatus(data.taskId, 'complete', data.downloadUrl);
        }
        break;

      case 'task_failed':
        if (taskCenterRef.current) {
          taskCenterRef.current.updateTaskStatus(data.taskId, 'failed');
        }
        break;
    }
  };
};
```

## 🎯 最佳实践

### 1. 性能优化
- 使用 `useCallback` 和 `useMemo` 优化重渲染
- 虚拟滚动处理大量任务列表
- 防抖搜索输入

### 2. 错误处理
- 网络请求失败重试机制
- localStorage 异常处理
- 用户友好的错误提示

### 3. 用户体验
- 加载状态指示
- 操作反馈动画
- 响应式设计适配

### 4. 安全考虑
- Token 过期处理
- API 请求鉴权
- 敏感信息保护

## 📚 扩展功能建议

### 1. 高级功能
- 任务分类标签
- 批量操作支持
- 任务优先级设置
- 历史记录导出

### 2. 集成建议
- 消息通知系统
- 进度条显示
- 音频预览播放
- 分享功能

---

**总结**：这个任务中心组件采用了现代化的设计理念和技术实现，具有良好的可扩展性和用户体验。通过本文档的详细指导，您可以在其他项目中成功复刻并根据需求进行定制化开发。
