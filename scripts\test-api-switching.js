/**
 * API切换功能测试脚本
 * 用于验证API自动切换功能是否正常工作，不影响现有逻辑
 */

// 模拟环境变量
const mockEnv = {
  NEXT_PUBLIC_API_URL: 'https://ttsapia.aispeak.top',
  NEXT_PUBLIC_BACKUP_API_URLS: 'https://ttssapi.aispeak.top,https://tts-api.panxuchao19951206.workers.dev',
  NEXT_PUBLIC_API_SWITCH_THRESHOLD: '3'
}

// 模拟process.env（兼容浏览器和Node.js环境）
if (typeof global !== 'undefined') {
  global.process = global.process || {};
  global.process.env = mockEnv;
} else if (typeof window !== 'undefined') {
  window.process = window.process || {};
  window.process.env = mockEnv;
}

// 模拟API配置
const API_CONFIG = {
  BASE_URL: mockEnv.NEXT_PUBLIC_API_URL,
  TIMEOUT: 120000,
  BACKUP_URLS: mockEnv.NEXT_PUBLIC_BACKUP_API_URLS?.split(',').filter(Boolean) || [],
  SWITCH_THRESHOLD: parseInt(mockEnv.NEXT_PUBLIC_API_SWITCH_THRESHOLD || '3'),
}

// 模拟ApiManager类
class MockApiManager {
  constructor() {
    this.currentApiIndex = 0
    this.apiUrls = [API_CONFIG.BASE_URL, ...API_CONFIG.BACKUP_URLS]
    this.failureCounts = new Map()
    this.switchThreshold = API_CONFIG.SWITCH_THRESHOLD
    this.lastSwitchTime = 0
    this.SWITCH_COOLDOWN = 30000

    // 初始化失败计数
    this.apiUrls.forEach(url => {
      this.failureCounts.set(url, 0)
    })
  }

  getCurrentApiUrl() {
    return this.apiUrls[this.currentApiIndex] || API_CONFIG.BASE_URL
  }

  getCurrentApiIndex() {
    return this.currentApiIndex
  }

  getAllApiUrls() {
    return [...this.apiUrls]
  }

  recordFailure(apiUrl) {
    const count = (this.failureCounts.get(apiUrl) || 0) + 1
    this.failureCounts.set(apiUrl, count)
    
    console.log(`[TEST] 记录失败: ${apiUrl} (${count}/${this.switchThreshold})`)
    
    if (count >= this.switchThreshold) {
      return this.switchToNextApi()
    }
    return false
  }

  switchToNextApi() {
    const now = Date.now()
    
    if (now - this.lastSwitchTime < this.SWITCH_COOLDOWN) {
      console.log('[TEST] 切换冷却中，跳过切换')
      return false
    }

    if (this.currentApiIndex < this.apiUrls.length - 1) {
      const oldApi = this.getCurrentApiUrl()
      this.currentApiIndex++
      const newApi = this.getCurrentApiUrl()
      this.lastSwitchTime = now
      
      console.log(`[TEST] ✅ 切换成功: ${oldApi} -> ${newApi}`)
      return true
    }
    
    console.log('[TEST] ❌ 所有API已用尽，无法继续切换')
    return false
  }

  recordSuccess(apiUrl) {
    this.failureCounts.set(apiUrl, 0)
    console.log(`[TEST] ✅ 记录成功: ${apiUrl}，重置失败计数`)
  }

  resetToFirstApi() {
    if (this.currentApiIndex !== 0) {
      const oldApi = this.getCurrentApiUrl()
      this.currentApiIndex = 0
      const newApi = this.getCurrentApiUrl()
      console.log(`[TEST] 🔄 重置到主API: ${oldApi} -> ${newApi}`)
    }
    
    this.apiUrls.forEach(url => {
      this.failureCounts.set(url, 0)
    })
  }

  getFailureCount(apiUrl) {
    return this.failureCounts.get(apiUrl) || 0
  }

  getStatus() {
    return {
      currentApi: this.getCurrentApiUrl(),
      currentIndex: this.currentApiIndex,
      totalApis: this.apiUrls.length,
      failureCounts: Object.fromEntries(this.failureCounts),
      lastSwitchTime: this.lastSwitchTime
    }
  }
}

// 测试用例
class ApiSwitchingTests {
  constructor() {
    this.apiManager = new MockApiManager()
    this.testResults = []
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString()
    const logMessage = `[${timestamp}] [${type.toUpperCase()}] ${message}`
    console.log(logMessage)
    this.testResults.push({ timestamp, type, message })
  }

  assert(condition, message) {
    if (condition) {
      this.log(`✅ PASS: ${message}`, 'pass')
    } else {
      this.log(`❌ FAIL: ${message}`, 'fail')
      throw new Error(`断言失败: ${message}`)
    }
  }

  // 测试1: 初始状态验证
  testInitialState() {
    this.log('开始测试: 初始状态验证')
    
    const status = this.apiManager.getStatus()
    this.assert(status.currentIndex === 0, '初始API索引应为0')
    this.assert(status.currentApi === 'https://ttsapia.aispeak.top', '初始API应为主API')
    this.assert(status.totalApis === 3, '总API数量应为3')
    
    this.log('初始状态测试完成')
  }

  // 测试2: 失败记录和切换
  testFailureRecordingAndSwitching() {
    this.log('开始测试: 失败记录和切换')
    
    const mainApi = this.apiManager.getCurrentApiUrl()
    
    // 记录2次失败，不应该切换
    let switched = this.apiManager.recordFailure(mainApi)
    this.assert(!switched, '2次失败不应该触发切换')
    
    switched = this.apiManager.recordFailure(mainApi)
    this.assert(!switched, '2次失败不应该触发切换')
    
    // 第3次失败应该触发切换
    switched = this.apiManager.recordFailure(mainApi)
    this.assert(switched, '3次失败应该触发切换')
    
    const newApi = this.apiManager.getCurrentApiUrl()
    this.assert(newApi === 'https://ttssapi.aispeak.top', '应该切换到第一个备用API')
    this.assert(this.apiManager.getCurrentApiIndex() === 1, 'API索引应为1')
    
    this.log('失败记录和切换测试完成')
  }

  // 测试3: 成功状态重置
  testSuccessReset() {
    this.log('开始测试: 成功状态重置')
    
    const currentApi = this.apiManager.getCurrentApiUrl()
    const failureCountBefore = this.apiManager.getFailureCount(currentApi)
    
    // 记录一次失败
    this.apiManager.recordFailure(currentApi)
    this.assert(this.apiManager.getFailureCount(currentApi) === failureCountBefore + 1, '失败计数应该增加')
    
    // 记录成功，应该重置失败计数
    this.apiManager.recordSuccess(currentApi)
    this.assert(this.apiManager.getFailureCount(currentApi) === 0, '成功后失败计数应该重置为0')
    
    this.log('成功状态重置测试完成')
  }

  // 测试4: 所有API用尽的情况
  testAllApisExhausted() {
    this.log('开始测试: 所有API用尽')
    
    // 强制切换到最后一个API
    while (this.apiManager.getCurrentApiIndex() < this.apiManager.getAllApiUrls().length - 1) {
      const currentApi = this.apiManager.getCurrentApiUrl()
      for (let i = 0; i < 3; i++) {
        this.apiManager.recordFailure(currentApi)
      }
    }
    
    // 现在应该在最后一个API
    this.assert(this.apiManager.getCurrentApiIndex() === 2, '应该在最后一个API')
    
    // 尝试再次切换，应该失败
    const lastApi = this.apiManager.getCurrentApiUrl()
    let switched = false
    for (let i = 0; i < 3; i++) {
      switched = this.apiManager.recordFailure(lastApi)
    }
    this.assert(!switched, '所有API用尽时不应该能够切换')
    
    this.log('所有API用尽测试完成')
  }

  // 测试5: 重置到主API
  testResetToMainApi() {
    this.log('开始测试: 重置到主API')
    
    // 确保不在主API
    this.assert(this.apiManager.getCurrentApiIndex() !== 0, '测试前应该不在主API')
    
    // 重置到主API
    this.apiManager.resetToFirstApi()
    
    this.assert(this.apiManager.getCurrentApiIndex() === 0, '重置后应该在主API')
    this.assert(this.apiManager.getCurrentApiUrl() === 'https://ttsapia.aispeak.top', '应该回到主API地址')
    
    // 检查所有失败计数是否重置
    this.apiManager.getAllApiUrls().forEach(url => {
      this.assert(this.apiManager.getFailureCount(url) === 0, `${url} 的失败计数应该重置为0`)
    })
    
    this.log('重置到主API测试完成')
  }

  // 测试6: 切换冷却机制
  testSwitchCooldown() {
    this.log('开始测试: 切换冷却机制')
    
    const mainApi = this.apiManager.getCurrentApiUrl()
    
    // 触发第一次切换
    for (let i = 0; i < 3; i++) {
      this.apiManager.recordFailure(mainApi)
    }
    
    // 立即尝试再次切换，应该被冷却机制阻止
    const currentApi = this.apiManager.getCurrentApiUrl()
    let switched = false
    for (let i = 0; i < 3; i++) {
      switched = this.apiManager.recordFailure(currentApi)
    }
    
    // 注意：这里可能会切换成功，因为是不同的API
    // 冷却机制主要是防止同一时间内频繁切换
    
    this.log('切换冷却机制测试完成')
  }

  // 运行所有测试
  async runAllTests() {
    this.log('🚀 开始API切换功能测试')
    
    try {
      this.testInitialState()
      this.testFailureRecordingAndSwitching()
      this.testSuccessReset()
      this.testAllApisExhausted()
      this.testResetToMainApi()
      this.testSwitchCooldown()
      
      this.log('🎉 所有测试通过！API切换功能正常工作')
      
      // 输出最终状态
      const finalStatus = this.apiManager.getStatus()
      this.log(`最终状态: ${JSON.stringify(finalStatus, null, 2)}`)
      
    } catch (error) {
      this.log(`💥 测试失败: ${error.message}`, 'error')
      throw error
    }
  }

  // 获取测试报告
  getTestReport() {
    const passCount = this.testResults.filter(r => r.type === 'pass').length
    const failCount = this.testResults.filter(r => r.type === 'fail').length
    
    return {
      total: this.testResults.length,
      passed: passCount,
      failed: failCount,
      results: this.testResults
    }
  }
}

// 运行测试
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { ApiSwitchingTests, MockApiManager }
} else {
  // 浏览器环境下直接运行
  const tests = new ApiSwitchingTests()
  tests.runAllTests().then(() => {
    const report = tests.getTestReport()
    console.log('\n📊 测试报告:')
    console.log(`总计: ${report.total} 项`)
    console.log(`通过: ${report.passed} 项`)
    console.log(`失败: ${report.failed} 项`)
  }).catch(error => {
    console.error('测试执行失败:', error)
  })
}
