# 多人对话批量操作功能

## 功能概述

多人对话批量操作功能为TTS应用提供了高效的对话管理能力，支持批量复制、导入和统计分析。

## 核心功能

### 1. 批量复制功能 📋

**位置**: v3模型提示信息右侧的"复制对话"按钮

**功能描述**:
- 将当前所有对话行复制到剪贴板
- 格式：`声音名称@对话内容`，每行一个对话
- 自动过滤空文本行
- 提供复制状态反馈

**示例输出**:
```
Adam@你好呀！今天过得怎么样？
Jessica@我今天过得很好，谢谢你的关心！
Brian@那真是太好了，有什么特别的事情吗？
```

### 2. 批量导入功能 📥

**位置**: v3模型提示信息右侧的"导入对话"按钮

**功能特性**:
- 支持从剪贴板导入批量对话
- 智能声音名称匹配（精确匹配、模糊匹配、缩写匹配）
- 实时预览解析结果
- 错误和警告提示
- 两种导入模式：替换现有对话 / 追加到现有对话

**导入格式**:
```
声音名称@对话内容
```

**智能匹配规则**:
1. **精确匹配**: 声音名称完全匹配
2. **模糊匹配**: 包含关系匹配
3. **去空格匹配**: 忽略空格后匹配
4. **缩写匹配**: 支持首字母缩写（≤3字符）

### 3. 统计信息功能 📊

**位置**: 当有对话内容时显示的"统计"按钮

**统计内容**:
- 对话行数
- 总字符数
- 平均每行字符数
- 声音使用分布（次数和百分比）
- 性别分布可视化

## 性能优化

### 1. 虚拟滚动
- **触发条件**: 对话行数 > 20行
- **优势**: 支持大量对话行的流畅渲染
- **特性**: 自动切换普通列表和虚拟滚动

### 2. 分批处理
- **导入优化**: 大量数据分批导入，避免UI卡顿
- **批次大小**: 10行/批次
- **间隔时间**: 100ms/批次

### 3. 防抖机制
- **实时预览**: 输入防抖，避免频繁解析
- **操作防抖**: 防止快速连续操作

## 用户体验优化

### 1. 状态反馈
- **复制状态**: 复制中 → 已复制 → 恢复
- **导入状态**: 导入中 → 完成
- **错误处理**: 友好的错误提示

### 2. 智能提示
- **示例按钮**: 一键加载示例文本
- **格式说明**: 清晰的格式要求
- **预览功能**: 实时显示解析结果

### 3. 操作便利性
- **自动聚焦**: 导入后自动激活第一行
- **自动滚动**: 滚动到新导入的内容
- **模态框**: 大屏幕友好的导入界面

## 技术架构

### 1. 组件结构
```
BatchOperations.tsx          # 主要批量操作组件
├── 复制功能模块
├── 导入功能模块
└── 统计功能模块

VirtualDialogueList.tsx      # 虚拟滚动组件
├── VirtualDialogueList      # 虚拟滚动实现
├── RegularDialogueList      # 普通列表
└── SmartDialogueList        # 智能选择组件

batch-operations.ts          # 工具函数库
├── formatDialogueForCopy    # 格式化复制文本
├── parseImportText          # 解析导入文本
├── matchVoiceName          # 智能声音匹配
└── getDialogueStats        # 统计信息生成
```

### 2. 状态管理
```typescript
interface BatchOperationState {
  showImportDialog: boolean;    // 导入对话框显示状态
  showStatsDialog: boolean;     // 统计对话框显示状态
  importText: string;           // 导入文本内容
  importMode: 'replace' | 'append'; // 导入模式
  importPreview: DialogueLine[]; // 预览数据
  importErrors: string[];       // 错误信息
  importWarnings: string[];     // 警告信息
  isImporting: boolean;         // 导入进行状态
  isCopying: boolean;          // 复制进行状态
  copySuccess: boolean;        // 复制成功状态
  showSample: boolean;         // 示例显示状态
}
```

## 使用指南

### 1. 基础使用
1. 在多人对话模式下，选择v3模型
2. 在提示信息右侧找到批量操作按钮
3. 使用"复制对话"导出当前对话
4. 使用"导入对话"批量导入新对话

### 2. 高级功能
1. **批量编辑**: 复制 → 外部编辑 → 导入
2. **模板复用**: 保存常用对话模板
3. **数据分析**: 使用统计功能分析对话分布

### 3. 最佳实践
1. **格式规范**: 严格按照 `声音名称@对话内容` 格式
2. **声音匹配**: 使用准确的声音名称，支持模糊匹配
3. **分批导入**: 大量数据建议分批导入
4. **预览确认**: 导入前仔细检查预览结果

## 错误处理

### 1. 常见错误
- **格式错误**: 缺少 `@` 分隔符
- **声音不存在**: 声音名称无法匹配
- **内容为空**: 声音名称或对话内容为空

### 2. 警告提示
- **文本过长**: 单行文本超过1000字符
- **声音重复**: 同一声音使用次数过多
- **数据量大**: 总字符数或行数较多

### 3. 降级处理
- **声音匹配失败**: 自动使用默认声音
- **剪贴板失败**: 提供手动复制选项
- **大数据处理**: 自动启用虚拟滚动

## 兼容性

### 1. 浏览器支持
- **现代浏览器**: 完整功能支持
- **旧版浏览器**: 降级到基础功能
- **移动端**: 响应式设计适配

### 2. 数据兼容
- **向后兼容**: 不影响现有功能
- **数据格式**: 标准JSON格式
- **导入导出**: 纯文本格式，跨平台兼容

## 更新日志

### v1.0.0 (当前版本)
- ✅ 批量复制功能
- ✅ 批量导入功能
- ✅ 统计信息功能
- ✅ 虚拟滚动优化
- ✅ 智能声音匹配
- ✅ 错误处理和用户提示

### 计划功能
- 🔄 撤销/重做功能
- 🔄 导入历史记录
- 🔄 模板管理功能
- 🔄 导出为其他格式
