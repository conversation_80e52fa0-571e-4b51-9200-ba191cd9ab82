// --- START OF FILE 修改版本.js ---

addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

const voiceIdMapping = {
  "Adam": "pNInz6obpgDQGcFmaJgB",
  "Alice": "Xb7hH8MSUJpSbSDYk0k2",
  "Antoni": "ErXwobaYiN019PkySvjV",
  "Aria": "9BWtsMINqrJLrRacOk9x",
  "Arnold": "VR6AewLTigWG4xSOukaG",
  "Arnold 2": "wViXBPUzp2ZZixB1xQuM",
  "Bill": "pqHfZKP75CvOlQylNhV4",
  "<PERSON>": "nPczCjzI2devNBz1zQrb",
  "Callum": "N2lVS1w4EtoT3dr4eOWO",
  "Charlie": "IKne3meq5aSn9XLyUdCD", 
  "Charlotte": "XB0fDUnXU5powFXDhCwa",
  "<PERSON>": "iP95p4xoKVk53GoZ742B",
  "<PERSON>": "2EiwWnXFnvU5JabPnv8n",
  "Daniel": "onwK4e9ZLuTAKqWW03F9",
  "Dave": "CYw3kZ02Hs0563khs1Fj",
  "Domi": "AZnzlk1XvdvUeBnXmlld",
  "Dorothy": "ThT5KcBeYPX3keUQqHPh",
  "Drew": "29vD33N1CtxCmqQRPOHJ",
  "Elli": "MF3mGyEYCl7XYWbV9V6O",
  "Emily": "LcfcDJNUP1GQjkzn1xUU",
  "Eric": "cjVigY5qzO86Huf0OWal",
  "Ethan": "g5CIjZEefAph4nQFvHAz",
  "Fin": "D38z5RcWu1voky8WS1ja",
  "Freya": "jsCqWAovK2LkecY7zXl4",
  "George": "JBFqnCBsd6RMkjVDRZzb",
  "George 2": "Yko7PKHZNXotIFUBG7I9",
  "Gigi": "jBpfuIE2acCO8z3wKNLl",
  "Giovanni": "zcAOhNBS3c14rBihAFp1",
  "Glinda": "z9fAnlkpzviPz146aGWa",
  "Grace": "oWAxZDx7w5VEj9dCyTzz",
  "Grandpa Spuds Oxley": "NOpBlnGInO9m6vDvFkFC",
  "Harry": "SOYHLrjzK2X1ezoPC6cr",
  "James": "ZQe5CZNOzWyzPSCn5a3c",
  "James - Husky & Engaging": "EkK5I93UQWFDigLMpZcX",
  "Jeremy": "bVMeCyTHy58xNoL34h3p",
  "Jessica": "cgSgspJ2msm6clMCkdW9",
  "Jessie": "t0jbNlBVZ17f02VDIeMI",
  "Joseph": "Zlb1dXrM653N07WRdFW3",
  "Josh": "TxGEqnHWrfWFTfGW9XjX",
  "Laura": "FGY2WhTYpPnrIDTdsKH5",
  "Liam": "TX3LPaxmHKxFdv7VOQHJ",
  "Lily": "pFZP5JQG7iQjIQuC4Bku",
  "Matilda": "XrExE9yKIg1WjnnlVkGX",
  "Michael": "flq6f7yk4E4fJM5XTYuZ",
  "Mimi": "zrHiDhphv9ZnVXBqCLjz",
  "Nicole": "piTKgcLEGmPE4e6mEKli",
  "Patrick": "ODq5zmih8GrVes37Dizd",
  "Paul": "5Q0t7uMcjvnagumLfvZi",
  "Rachel": "21m00Tcm4TlvDq8ikWAM",
  "River": "SAz9YHcvj6GT2YYXdXww",
  "Roger": "CwhRBWXzGAHq8TQ4Fs17",
  "Sam": "yoZ06aMxZJJ28mfd3POQ",
  "Samara X": "19STyYD15bswVz51nqLf",
  "Sarah": "EXAVITQu4vr4xnSDxMaL",
  "Serena": "pMsXgVXv3BLzUgSXRplE",
  "Thomas": "GBv7mTt0atIp3Br8iCZE",
  "Will": "bIHbv24MWmeRgasZH58o",
  "🎅 Santa Claus": "knrPHWnBmmDHMoiMeP3l"
}

/**
 * [已修改] 智能分割文本以支持SSML指令。
 * 这个函数会将文本分割成不超过maxLength的块，同时确保 [...] 形式的指令不会被破坏。
 * 它首先将文本分割成普通文本片段和SSML指令片段的数组，然后将这些片段组合成块。
 * @param {string} text - 输入的文本，可能包含SSML指令。
 * @param {number} maxLength - 每个块的最大长度，默认为500。
 * @returns {Promise<string[]>} - 分割后的文本块数组。
 */
async function splitText(text, maxLength = 500) {
  // 使用正则表达式将文本分割成一个包含普通文本和SSML指令的数组。
  // 例如 "[calmly] Hello." 会被分割成 ["", "[calmly]", " Hello."]。
  const parts = text.split(/(\[.*?\])/g).filter(Boolean); // filter(Boolean) 用于移除空字符串

  const chunks = [];
  let currentChunk = "";

  for (const part of parts) {
    // 如果当前块加上新片段会超过最大长度
    if (currentChunk.length + part.length > maxLength) {
      // 如果当前块有内容，则推入chunks数组
      if (currentChunk) {
        chunks.push(currentChunk.trim());
      }
      // 开始一个新的块。如果片段本身就超长，它会自己成为一个块。
      currentChunk = part;
    } else {
      // 否则，将新片段添加到当前块
      currentChunk += part;
    }
  }

  // 推入最后一个块
  if (currentChunk) {
    chunks.push(currentChunk.trim());
  }
  
  // 如果没有任何内容，返回一个空数组，避免错误
  if (chunks.length === 0 && text.length > 0) {
    chunks.push(text);
  }

  return chunks;
}

async function generateSpeech(text, voiceId, modelId, output_format = "pcm_16000") {
  // 保留了 allow_unauthenticated=1 端点
  const url = `https://api.us.elevenlabs.io/v1/text-to-speech/${voiceId}?allow_unauthenticated=1`;
  const headers = { 'Content-Type': 'application/json' };
  const payload = {
    text: text,
    model_id: modelId,
    output_format,
    voice_settings: {
      stability: 0.5,
      similarity_boost: 0.75,
      // 使用 use_speaker_boost: true 通常在新模型上效果更好
      // use_speaker_boost: true
    }
  };
  
  console.log(`[DEBUG] Generating speech for voice: ${voiceId}, text: "${text.substring(0, 50)}...", model: ${modelId}, format: ${output_format}`);
  
  try {
  const response = await fetch(url, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify(payload)
  });
  
  if (response.ok) {
      const audioBuffer = await response.arrayBuffer();
      console.log(`[DEBUG] Successfully generated audio, size: ${audioBuffer.byteLength} bytes`);
      return audioBuffer;
  } else {
      const errorText = await response.text();
      console.error(`[ERROR] ElevenLabs API Error: ${response.status} ${response.statusText}`, errorText);
      console.error(`[ERROR] Request payload:`, JSON.stringify(payload, null, 2));
    return null; // 返回null以便于后续处理错误
    }
  } catch (error) {
    console.error(`[ERROR] Network error during speech generation:`, error);
    return null;
  }
}

async function processChunks(chunks, voiceId, modelId, output_format) {
  console.log(`[DEBUG] Processing ${chunks.length} chunks for voice: ${voiceId}`);
  const audioDataList = [];
  let successCount = 0;
  let failCount = 0;
  
  for (let i = 0; i < chunks.length; i++) {
    const chunk = chunks[i];
    // 如果文本块为空，则跳过，避免向API发送空请求
    if (!chunk || chunk.trim() === '') {
      console.log(`[DEBUG] Skipping empty chunk ${i}`);
      continue;
    }
    
    console.log(`[DEBUG] Processing chunk ${i+1}/${chunks.length}: "${chunk.substring(0, 50)}..."`);
    const audioData = await generateSpeech(chunk, voiceId, modelId, output_format);
    if (audioData && audioData.byteLength > 0) {
      audioDataList.push(audioData);
      successCount++;
      console.log(`[DEBUG] Chunk ${i+1} processed successfully`);
    } else {
      failCount++;
      console.warn(`[WARN] Failed to generate audio for chunk ${i+1}: "${chunk.substring(0, 100)}..."`);
    }
  }
  
  console.log(`[DEBUG] Processed chunks: ${successCount} success, ${failCount} failed, ${audioDataList.length} total audio chunks`);
  return audioDataList;
}

function combineAudio(audioDataList) {
  if (audioDataList.length === 0) return new ArrayBuffer(0);

  const totalLength = audioDataList.reduce((acc, buffer) => acc + buffer.byteLength, 0);
  const combined = new Uint8Array(totalLength);
  
  let offset = 0;
  for (const buffer of audioDataList) {
    combined.set(new Uint8Array(buffer), offset);
    offset += buffer.byteLength;
  }
  
  return combined.buffer;
}

/**
 * [新增] 解析并验证多人对话数据
 * @param {Object} data - 请求数据
 * @returns {Object} - 包含解析结果和错误信息的对象
 */
function parseDialogueData(data) {
  console.log(`[DEBUG] Parsing dialogue data:`, JSON.stringify(data, null, 2));
  const { dialogue, model_id, output_format } = data;
  
  // 验证dialogue数组
  if (!Array.isArray(dialogue) || dialogue.length === 0) {
    console.error(`[ERROR] Invalid dialogue array:`, dialogue);
    return { error: "dialogue must be a non-empty array" };
  }
  
  console.log(`[DEBUG] Found ${dialogue.length} dialogue items`);
  
  // 验证每个对话项
  for (let i = 0; i < dialogue.length; i++) {
    const item = dialogue[i];
    console.log(`[DEBUG] Validating dialogue[${i}]:`, JSON.stringify(item, null, 2));
    
    if (!item.text || typeof item.text !== 'string') {
      console.error(`[ERROR] dialogue[${i}] has invalid text:`, item.text);
      return { error: `dialogue[${i}].text is required and must be a string` };
    }
    
    // 支持voice_id直接指定或通过voice_name映射
    if (!item.voice_id && !item.voice_name) {
      console.error(`[ERROR] dialogue[${i}] missing both voice_id and voice_name:`, item);
      return { error: `dialogue[${i}] must have either voice_id or voice_name` };
    }
    
    // 如果提供了voice_name但没有voice_id，尝试映射
    if (!item.voice_id && item.voice_name) {
      console.log(`[DEBUG] Mapping voice_name "${item.voice_name}" for dialogue[${i}]`);
      const mappedId = voiceIdMapping[item.voice_name];
      if (!mappedId) {
        console.error(`[ERROR] Voice mapping failed for "${item.voice_name}". Available voices:`, Object.keys(voiceIdMapping));
        return { error: `Invalid voice_name "${item.voice_name}" in dialogue[${i}]. Available voices: ${Object.keys(voiceIdMapping).join(', ')}` };
      }
      item.voice_id = mappedId; // 设置映射的voice_id
      console.log(`[DEBUG] Mapped "${item.voice_name}" to voice_id: ${mappedId}`);
    }
    
    console.log(`[DEBUG] dialogue[${i}] validated successfully: voice_id=${item.voice_id}, text_length=${item.text.length}`);
  }
  
  const result = {
    dialogue,
    model: model_id || "eleven_v3",
    output_format: output_format || "pcm_16000"
  };
  
  console.log(`[DEBUG] Dialogue parsing completed successfully:`, {
    speakers_count: dialogue.length,
    model: result.model,
    output_format: result.output_format
  });
  
  return result;
}

/**
 * [新增] 处理多人对话音频生成
 * @param {Array} dialogue - 对话数组
 * @param {string} modelId - 模型ID
 * @param {string} output_format - 输出格式
 * @returns {Promise<ArrayBuffer|null>} - 合并后的音频数据或null
 */
async function processDialogue(dialogue, modelId, output_format) {
  console.log(`[DEBUG] Starting dialogue processing: ${dialogue.length} speakers, model: ${modelId}, format: ${output_format}`);
  const audioDataList = [];
  let successSpeakers = 0;
  let failedSpeakers = 0;
  
  for (let i = 0; i < dialogue.length; i++) {
    const item = dialogue[i];
    console.log(`[DEBUG] Processing dialogue[${i}]: voice_id=${item.voice_id}, voice_name=${item.voice_name}, text="${item.text.substring(0, 50)}..."`);
    
    // 验证voice_id是否存在
    if (!item.voice_id) {
      console.error(`[ERROR] dialogue[${i}] missing voice_id after parsing`);
      failedSpeakers++;
      continue;
    }
    
    // 为每个对话项分割文本（如果太长）
    const chunks = await splitText(item.text);
    console.log(`[DEBUG] dialogue[${i}] split into ${chunks.length} chunks`);
    
    // 为该说话者的所有文本块生成音频
    const speakerAudioList = await processChunks(chunks, item.voice_id, modelId, output_format);
    
    if (speakerAudioList.length === 0) {
      console.error(`[ERROR] Failed to generate any audio for dialogue[${i}] (voice: ${item.voice_id})`);
      failedSpeakers++;
      continue; // 跳过这个对话项，继续处理下一个
    }
    
    // 合并该说话者的所有音频块
    const speakerCombinedAudio = combineAudio(speakerAudioList);
    if (speakerCombinedAudio.byteLength > 0) {
      audioDataList.push(speakerCombinedAudio);
      successSpeakers++;
      console.log(`[DEBUG] dialogue[${i}] successfully processed, audio size: ${speakerCombinedAudio.byteLength} bytes`);
    } else {
      console.error(`[ERROR] dialogue[${i}] generated empty audio after combining`);
      failedSpeakers++;
    }
  }
  
  console.log(`[DEBUG] Dialogue processing completed: ${successSpeakers} success, ${failedSpeakers} failed`);
  
  if (audioDataList.length === 0) {
    console.error(`[ERROR] No audio data generated for any dialogue speaker`);
    return null;
  }
  
  // 合并所有说话者的音频
  const finalAudio = combineAudio(audioDataList);
  console.log(`[DEBUG] Final combined audio size: ${finalAudio.byteLength} bytes`);
  return finalAudio;
}

async function handleRequest(request) {
  const url = new URL(request.url);
  
  // 预检请求处理，适用于某些前端框架
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });
  }
  
  if (request.method === 'GET' && url.pathname === '/') {
    return new Response(htmlForm, { headers: { 'Content-Type': 'text/html; charset=utf-8' } });
  } 
  
  // 统一处理POST请求，避免代码重复
  if (request.method === 'POST' && (url.pathname === '/generate' || url.pathname === '/v1/audio/speech')) {
    try {
      const data = await request.json();
      // 对于 /generate 路径，如果model未提供，则设置默认值
      // 对于 /v1/audio/speech，我们期望请求中包含model
      const model = data.model || "eleven_v3"; 
      const { input, voice } = data;
      // 兼容OpenAI API的 output_format 或前端的自定义参数
      const output_format = data.output_format || "pcm_16000"; 
      
      const voiceId = voiceIdMapping[voice];
      if (!voiceId) {
        return new Response(JSON.stringify({ error: "Invalid voice selection" }), { status: 400, headers: { 'Content-Type': 'application/json' } });
      }
      
      if (!input) {
        return new Response(JSON.stringify({ error: "Input text is required" }), { status: 400, headers: { 'Content-Type': 'application/json' } });
      }

      const chunks = await splitText(input);
      const audioDataList = await processChunks(chunks, voiceId, model, output_format);
      
      if (audioDataList.length === 0) {
        return new Response(JSON.stringify({ error: "Failed to generate any audio data" }), { status: 500, headers: { 'Content-Type': 'application/json' } });
      }

      const combinedAudioData = combineAudio(audioDataList);
      
      // 根据请求的格式返回不同的音频类型
      const contentType = output_format.startsWith('mp3') ? 'audio/mpeg' : 'audio/wav';
      const filename = output_format.startsWith('mp3') ? 'speech.mp3' : 'speech.wav';

      return new Response(combinedAudioData, { 
        headers: { 
          'Content-Type': contentType, 
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Access-Control-Allow-Origin': '*' // 允许跨域
        } 
      });

    } catch (e) {
      return new Response(JSON.stringify({ error: "Invalid JSON in request body", details: e.message }), { status: 400, headers: { 'Content-Type': 'application/json' } });
    }
  } 
  
  // [新增] 多人对话API端点
  if (request.method === 'POST' && (url.pathname === '/dialogue' || url.pathname === '/v1/audio/dialogue')) {
    try {
      const data = await request.json();
      console.log(`[DEBUG] Received dialogue request:`, JSON.stringify(data, null, 2));
      
      // 解析和验证多人对话数据
      const parseResult = parseDialogueData(data);
      if (parseResult.error) {
        console.error(`[ERROR] Dialogue data validation failed:`, parseResult.error);
        return new Response(JSON.stringify({ error: parseResult.error }), { 
          status: 400, 
          headers: { 'Content-Type': 'application/json' } 
        });
      }
      
      const { dialogue, model, output_format } = parseResult;
      console.log(`[DEBUG] Dialogue data parsed successfully: ${dialogue.length} speakers, model: ${model}, format: ${output_format}`);
      
      // 处理多人对话音频生成
      const combinedAudioData = await processDialogue(dialogue, model, output_format);
      
      if (!combinedAudioData || combinedAudioData.byteLength === 0) {
        console.error(`[ERROR] Failed to generate dialogue audio - no audio data returned`);
        return new Response(JSON.stringify({ 
          error: "Failed to generate dialogue audio", 
          details: "No audio data was generated. Check server logs for more information.",
          debug_info: {
            speakers_count: dialogue.length,
            model: model,
            output_format: output_format
          }
        }), { 
          status: 500, 
          headers: { 'Content-Type': 'application/json' } 
        });
      }
      
      console.log(`[DEBUG] Dialogue audio generated successfully, size: ${combinedAudioData.byteLength} bytes`);
      
      // 根据请求的格式返回不同的音频类型
      const contentType = output_format.startsWith('mp3') ? 'audio/mpeg' : 'audio/wav';
      const filename = output_format.startsWith('mp3') ? 'dialogue.mp3' : 'dialogue.wav';
      
      return new Response(combinedAudioData, { 
        headers: { 
          'Content-Type': contentType, 
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Access-Control-Allow-Origin': '*' // 允许跨域
        } 
      });
      
    } catch (e) {
      console.error(`[ERROR] Dialogue API error:`, e);
      return new Response(JSON.stringify({ 
        error: "Internal server error", 
        details: e.message,
        stack: e.stack 
      }), { 
        status: 500, 
        headers: { 'Content-Type': 'application/json' } 
      });
    }
  } 
  
  return new Response('Not Found', { status: 404 });
}

const htmlForm = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>文本转语音 (TTS)</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; line-height: 1.6; padding: 20px; max-width: 800px; margin: auto; background-color: #f4f4f9; }
    h1 { color: #333; }
    
    /* 标签页样式 */
    .tabs { display: flex; margin-bottom: 20px; background: #fff; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    .tab { flex: 1; padding: 15px; text-align: center; cursor: pointer; background: #f8f9fa; border-right: 1px solid #ddd; transition: background 0.3s; }
    .tab:last-child { border-right: none; }
    .tab.active { background: #007bff; color: white; }
    .tab:hover:not(.active) { background: #e9ecef; }
    
    /* 标签页内容 */
    .tab-content { display: none; background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    .tab-content.active { display: block; }
    
    /* 表单样式 */
    textarea, select, button, input { width: 100%; padding: 10px; margin-bottom: 15px; border-radius: 4px; border: 1px solid #ddd; box-sizing: border-box; }
    button { background-color: #007bff; color: white; border: none; cursor: pointer; font-size: 16px; }
    button:hover { background-color: #0056b3; }
    button:disabled { background-color: #cccccc; cursor: not-allowed; }
    
    /* 多人对话样式 */
    .dialogue-container { background: #1a1a1a; border-radius: 8px; padding: 20px; margin-bottom: 20px; min-height: 300px; }
    .speaker { display: flex; align-items: flex-start; margin-bottom: 20px; }
    .speaker-avatar { width: 40px; height: 40px; border-radius: 50%; margin-right: 15px; flex-shrink: 0; display: flex; align-items: center; justify-content: center; font-weight: bold; color: white; font-size: 14px; }
    .speaker-content { flex: 1; }
    .speaker-name { color: #fff; font-weight: bold; margin-bottom: 5px; display: flex; align-items: center; justify-content: space-between; }
    .speaker-text { background: #2d2d2d; color: #fff; padding: 12px; border-radius: 8px; }
    .speaker-text textarea { background: transparent; border: none; color: #fff; resize: vertical; min-height: 60px; width: 100%; }
    .speaker-text textarea:focus { outline: none; background: rgba(255,255,255,0.1); }
    .voice-select { width: auto; margin: 0; padding: 5px 10px; font-size: 12px; }
    .remove-speaker { background: #dc3545; padding: 5px 10px; font-size: 12px; margin-left: 10px; width: auto; }
    
    .add-speaker-btn { background: #28a745; margin-bottom: 20px; }
    .add-speaker-btn:hover { background: #218838; }
    
    /* 音频播放器 */
    audio { width: 100%; margin-top: 20px; }
    #status { margin-top: 15px; color: #555; }
    
    /* 响应式 */
    @media (max-width: 600px) {
      .speaker { flex-direction: column; }
      .speaker-avatar { margin-right: 0; margin-bottom: 10px; align-self: center; }
    }
  </style>
</head>
<body>
  <h1>文本转语音 (TTS)</h1>
  
  <!-- 标签页导航 -->
  <div class="tabs">
    <div class="tab active" onclick="switchTab(event, 'single')">单人TTS</div>
    <div class="tab" onclick="switchTab(event, 'dialogue')">多人对话</div>
  </div>
  
  <!-- 单人TTS标签页 -->
  <div id="single-tab" class="tab-content active">
  <p>支持使用 <code>[指令]</code> 来控制情感，例如：<code>[calmly] 这是平静的一段话。 [whispering] 这是耳语。</code></p>
  <form id="speechForm">
    <label for="input">输入文本:</label>
      <textarea id="input" name="input" rows="8" required>[calmly] In the ancient land of Eldoria, where skies shimmered and forests [whispering] whispered secrets to the wind, lived a dragon named Zephyros. [sarcastic] Not the "burn it all down" kind! [exhales] [thoughtful] he was gentle, WISE, with eyes like old stars. [softly] [reverent] Even the birds fell silent when he passed...</textarea><br><br>
    
    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
      <label for="voice" style="flex-shrink: 0;">选择声音:</label>
      <select id="voice" name="voice" required style="flex-grow: 1;">
          <option value="Adam">Adam</option>
          <option value="Alice">Alice</option>
          <option value="Antoni">Antoni</option>
          <option value="Aria">Aria</option>
          <option value="Arnold">Arnold</option>
          <option value="Bill">Bill</option>
          <option value="Brian">Brian</option>
          <option value="Callum">Callum</option>
          <option value="Charlie">Charlie</option>
          <option value="Charlotte">Charlotte</option>
          <option value="Chris">Chris</option>
          <option value="Clyde">Clyde</option>
          <option value="Daniel">Daniel</option>
          <option value="Dave">Dave</option>
          <option value="Domi">Domi</option>
          <option value="Dorothy">Dorothy</option>
          <option value="Drew">Drew</option>
          <option value="Elli">Elli</option>
          <option value="Emily">Emily</option>
          <option value="Eric">Eric</option>
          <option value="Ethan">Ethan</option>
          <option value="Fin">Fin</option>
          <option value="Freya">Freya</option>
          <option value="George">George</option>
          <option value="Gigi">Gigi</option>
          <option value="Giovanni">Giovanni</option>
          <option value="Glinda">Glinda</option>
          <option value="Grace">Grace</option>
          <option value="Grandpa Spuds Oxley">Grandpa Spuds Oxley</option>
          <option value="Harry">Harry</option>
          <option value="James">James</option>
          <option value="Jeremy">Jeremy</option>
          <option value="Jessica">Jessica</option>
          <option value="Jessie">Jessie</option>
          <option value="Joseph">Joseph</option>
          <option value="Josh">Josh</option>
          <option value="Laura">Laura</option>
          <option value="Liam">Liam</option>
          <option value="Lily">Lily</option>
          <option value="Matilda">Matilda</option>
          <option value="Michael">Michael</option>
          <option value="Mimi">Mimi</option>
          <option value="Nicole">Nicole</option>
          <option value="Patrick">Patrick</option>
          <option value="Paul">Paul</option>
          <option value="Rachel">Rachel</option>
          <option value="River">River</option>
          <option value="Roger">Roger</option>
          <option value="Sam">Sam</option>
          <option value="Sarah">Sarah</option>
          <option value="Serena">Serena</option>
          <option value="Thomas">Thomas</option>
          <option value="Will">Will</option>
      </select>
        <button type="button" id="auditionBtn" style="width: auto; padding: 10px 15px;">试听</button>
    </div>

    <button id="submitBtn" type="submit">生成音频</button>
  </form>
  </div>
  
  <!-- 多人对话标签页 -->
  <div id="dialogue-tab" class="tab-content">
    <p>创建多人对话，每个说话者可以选择不同的声音。支持 <code>[excitedly]</code>, <code>[whispering]</code> 等情感指令。</p>
    
    <div class="dialogue-container" id="dialogueContainer">
      <!-- 说话者会动态添加到这里 -->
    </div>
    
    <button type="button" class="add-speaker-btn" onclick="addSpeaker()">+ 添加说话者</button>
    <button type="button" id="generateDialogueBtn" onclick="generateDialogue()">生成对话音频</button>
  </div>
  
  <div id="status"></div>
  <audio id="audioPlayer" controls style="display:none;"></audio>
  
  <script>
    // 确保函数在全局作用域中
    window.speakerCount = 0;
    window.voiceOptions = ['Adam', 'Alice', 'Antoni', 'Aria', 'Arnold', 'Bill', 'Brian', 'Callum', 'Charlie', 'Charlotte', 'Chris', 'Clyde', 'Daniel', 'Dave', 'Domi', 'Dorothy', 'Drew', 'Elli', 'Emily', 'Eric', 'Ethan', 'Fin', 'Freya', 'George', 'Gigi', 'Giovanni', 'Glinda', 'Grace', 'Grandpa Spuds Oxley', 'Harry', 'James', 'Jeremy', 'Jessica', 'Jessie', 'Joseph', 'Josh', 'Laura', 'Liam', 'Lily', 'Matilda', 'Michael', 'Mimi', 'Nicole', 'Patrick', 'Paul', 'Rachel', 'River', 'Roger', 'Sam', 'Sarah', 'Serena', 'Thomas', 'Will'];
    
    // 标签页切换
    window.switchTab = function(event, tabName) {
      document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
      document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
      event.currentTarget.classList.add('active');
      document.getElementById(tabName + '-tab').classList.add('active');
    }
    
    // 添加说话者
    window.addSpeaker = function() {
      window.speakerCount++;
      const container = document.getElementById('dialogueContainer');
      const speakerDiv = document.createElement('div');
      speakerDiv.className = 'speaker';
      speakerDiv.id = 'speaker-' + window.speakerCount;
      
      const colors = ['#007bff', '#28a745', '#dc3545', '#ffc107', '#17a2b8', '#6610f2', '#e83e8c', '#fd7e14'];
      const avatarColor = colors[window.speakerCount % colors.length];
      
      let voiceOptionsHTML = window.voiceOptions.map(voice => {
        let selected = '';
        if (voice === (window.speakerCount === 1 ? 'Grandpa Spuds Oxley' : window.speakerCount === 2 ? 'Jessica' : 'Adam')) {
          selected = 'selected';
        }
        return \`<option value="\${voice}" \${selected}>\${voice}</option>\`;
      }).join('');
      
      let initialText = window.speakerCount === 1 ? '[excitedly] Liam! Have you tried the new ElevenLabs V3?' : window.speakerCount === 2 ? 'Yeah, just got it! The emotion is so amazing. I can actually do whispers now— [whispering] like this!' : '';

      speakerDiv.innerHTML = \`
        <div class="speaker-avatar" style="background-color: \${avatarColor};">
          \${window.speakerCount}
        </div>
        <div class="speaker-content">
          <div class="speaker-name">
            <span>说话者 \${window.speakerCount}</span>
            <div>
              <select class="voice-select" data-speaker="\${window.speakerCount}">
                \${voiceOptionsHTML}
              </select>
              <button type="button" class="remove-speaker" onclick="removeSpeaker(\${window.speakerCount})">删除</button>
            </div>
          </div>
          <div class="speaker-text">
            <textarea placeholder="输入对话内容... 支持 [excitedly], [whispering] 等情感指令" data-speaker="\${window.speakerCount}">\${initialText}</textarea>
          </div>
        </div>
      \`;
      
      container.appendChild(speakerDiv);
    }
    
    // 删除说话者
    window.removeSpeaker = function(speakerId) {
      const speakerElement = document.getElementById('speaker-' + speakerId);
      if (speakerElement) {
        speakerElement.remove();
      }
    }
    
    // 生成多人对话音频
    window.generateDialogue = async function() {
      const speakers = document.querySelectorAll('.speaker');
      if (speakers.length === 0) {
        alert('请至少添加一个说话者');
        return;
      }
      
      const dialogue = [];
      speakers.forEach((speaker, index) => {
        const textarea = speaker.querySelector('textarea');
        const voiceSelect = speaker.querySelector('.voice-select');
        const text = textarea.value.trim();
        
        if (text) {
          dialogue.push({
            text: text,
            voice_name: voiceSelect.value
          });
        }
      });
      
      if (dialogue.length === 0) {
        alert('请输入对话内容');
        return;
      }
      
      const generateBtn = document.getElementById('generateDialogueBtn');
      const statusDiv = document.getElementById('status');
      const audioPlayer = document.getElementById('audioPlayer');
      
      generateBtn.disabled = true;
      generateBtn.textContent = '正在生成对话...';
      statusDiv.textContent = '请求已发送，请稍候...';
      audioPlayer.style.display = 'none';
      
      const requestPayload = {
        dialogue: dialogue,
        model_id: "eleven_v3",
        output_format: "mp3_44100_128"
      };
      
      try {
        const response = await fetch('/dialogue', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(requestPayload)
        });
        
        if (response.ok) {
          const audioBlob = await response.blob();
          const audioUrl = URL.createObjectURL(audioBlob);
          audioPlayer.src = audioUrl;
          audioPlayer.style.display = 'block';
          audioPlayer.play();
          statusDiv.textContent = '对话音频生成成功！';
        } else {
          const errorData = await response.json();
          let errorMessage = '生成对话时出错: ' + (errorData.error || response.statusText);
          if (errorData.details) {
            errorMessage += '\\n详细信息: ' + errorData.details;
          }
          if (errorData.debug_info) {
            errorMessage += '\\n调试信息: ' + JSON.stringify(errorData.debug_info);
          }
          alert(errorMessage);
          statusDiv.textContent = '生成失败 - 查看控制台获取详细信息';
        }
      } catch (error) {
        alert('请求失败，请检查网络连接或联系管理员。\\n错误: ' + error.message);
        statusDiv.textContent = '请求异常 - 查看控制台获取详细信息';
      } finally {
        generateBtn.disabled = false;
        generateBtn.textContent = '生成对话音频';
      }
    }
    
    // 单人TTS功能
    document.getElementById('speechForm').addEventListener('submit', async function(event) {
      event.preventDefault();
      
      const submitBtn = document.getElementById('submitBtn');
      const statusDiv = document.getElementById('status');
      const audioPlayer = document.getElementById('audioPlayer');
      
      submitBtn.disabled = true;
      submitBtn.textContent = '正在生成...';
      statusDiv.textContent = '请求已发送，请稍候...';
      audioPlayer.style.display = 'none';

      const formData = new FormData(event.target);
      const input = formData.get('input');
      const voice = formData.get('voice');
      
      try {
        const response = await fetch('/generate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            input, 
            voice,
            output_format: "mp3_44100_128" 
          })
        });
        
        if (response.ok) {
          const audioBlob = await response.blob();
          const audioUrl = URL.createObjectURL(audioBlob);
          audioPlayer.src = audioUrl;
          audioPlayer.style.display = 'block';
          audioPlayer.play();
          statusDiv.textContent = '音频生成成功！';
        } else {
          const errorData = await response.json();
          alert('生成语音时出错: ' + (errorData.error || response.statusText));
          statusDiv.textContent = '生成失败。';
        }
      } catch (error) {
        alert('请求失败，请检查网络连接或联系管理员。');
        statusDiv.textContent = '请求异常。';
      } finally {
        submitBtn.disabled = false;
        submitBtn.textContent = '生成音频';
      }
    });

    document.getElementById('auditionBtn').addEventListener('click', function() {
      alert('试听功能需要音频预览URL');
    });
    
    // 页面加载时添加默认说话者
    window.addEventListener('load', function() {
      addSpeaker();
      addSpeaker();
    });
  </script>
</body>
</html>
`;
