/**
 * 声音相关的类型定义
 */

export interface Voice {
  id: string;
  name: string;
  gender: 'male' | 'female' | 'neutral';
  language: 'en' | 'ja' | 'es' | 'ko' | 'fr';
  description: string;
  preview: string;
}

export interface VoicesError {
  type: 'network' | 'parse' | 'empty' | 'unknown';
  message: string;
  retryable: boolean;
}

export interface VoicesState {
  voices: Voice[];
  isLoading: boolean;
  error: VoicesError | null;
  voiceIconMapping: Record<string, string>;
}
