// ========== 配置部分 ==========
const getAuthConfig = (env) => ({
  JWT_SECRET: env.JWT_SECRET,
  ACCESS_TOKEN_EXPIRE: 2 * 60 * 60, // 2小时
  REFRESH_TOKEN_EXPIRE: 7 * 24 * 60 * 60, // 7天
  SALT_ROUNDS: 10
});

const getSESConfig = (env) => ({
  TENCENT_SECRET_ID: env.TENCENT_SECRET_ID,
  TENCENT_SECRET_KEY: env.TENCENT_SECRET_KEY,
  SES_REGION: env.SES_REGION || 'ap-guangzhou',
  FROM_EMAIL: env.FROM_EMAIL,
  FROM_EMAIL_NAME: env.FROM_EMAIL_NAME || '验证服务',
  VERIFICATION_TEMPLATE_ID: env.VERIFICATION_TEMPLATE_ID
});

// R2直链下载配置
const R2_DIRECT_DOWNLOAD_CONFIG = {
  DOMAIN: 'r2-assets.aispeak.top',
  PATH_PREFIX: 'audios',
  // 生成完整的R2直链URL
  generateUrl: (taskId) => `https://${R2_DIRECT_DOWNLOAD_CONFIG.DOMAIN}/${R2_DIRECT_DOWNLOAD_CONFIG.PATH_PREFIX}/${taskId}.mp3`
};

// 进度消息配置
const getProgressConfig = (env) => ({
  // 控制是否发送详细进度消息的开关
  ENABLE_PROGRESS_MESSAGES: env.ENABLE_PROGRESS_MESSAGES === 'true' || env.ENABLE_PROGRESS_MESSAGES === true,
  // 可以根据需要添加更多进度相关配置
  ENABLE_DEBUG_PROGRESS: env.DEBUG === 'true' || env.DEBUG === true
});

// ========== DO位置提示配置 ==========
// 【优先级策略】使用分层级的路由优先级，以保证100%兼容性
// 优先级: 1. APAC -> 2. Europe (Random) -> 3. North America (Failover, Random)
const DO_LOCATION_PRIORITY_TIERS = [
  // Tier 1: 最高优先级 - 亚太地区
  ['apac'],
  // Tier 2: 次高优先级 - 欧洲地区 (随机选择)
  ['weur', 'eeur'],
  // Tier 3: 最低优先级/故障转移 - 北美地区 (随机选择)
  ['wnam', 'enam']
];

/**
 * 【最终版 - 分层优先级】根据预设的优先级层次获取DO位置提示。
 * 优先级: 1. APAC -> 2. Europe (Random) -> 3. North America (Failover, Random)
 * @param {object} env - Cloudflare环境变量
 * @param {string[]} excludeLocations - 要动态排除的故障区域列表
 * @returns {string|undefined} 根据优先级策略选择的数据中心代码
 */
function getRandomLocationHint(env, excludeLocations = []) {
  // 检查是否启用DO位置提示功能 (此部分逻辑不变)
  const enableLocationHint = env.ENABLE_DO_LOCATION_HINT !== 'false';
  if (!enableLocationHint) {
    if (env.DEBUG) console.log('[DO-ROUTING] Location hint disabled by environment variable');
    return undefined;
  }

  if (env.DEBUG) console.log(`[DO-ROUTING] Starting priority-based location selection. Excluding: [${excludeLocations.join(', ')}]`);

  // 1. 遍历所有优先级层级
  for (let i = 0; i < DO_LOCATION_PRIORITY_TIERS.length; i++) {
    const tier = DO_LOCATION_PRIORITY_TIERS[i];
    const tierName = i === 0 ? 'Highest (APAC)' : i === 1 ? 'Secondary (Europe)' : 'Failover (North America)';

    // 2. 在当前层级中，找出所有"健康"的区域
    // 即：不在 `excludeLocations` 列表中的区域
    const availableHintsInTier = tier.filter(hint => !excludeLocations.includes(hint));

    // 3. 如果当前层级有可用的健康区域
    if (availableHintsInTier.length > 0) {
      // 从这些健康区域中随机选择一个
      const selectedHint = availableHintsInTier[Math.floor(Math.random() * availableHintsInTier.length)];

      if (env.DEBUG) {
        console.log(`[DO-ROUTING] ✅ Success! Selected hint '${selectedHint}' from Priority Tier ${i+1} (${tierName}).`);
      }

      // 找到了就立即返回，不再继续尝试更低的优先级
      return selectedHint;
    } else {
      // 如果当前层级没有可用区域 (要么是空的，要么都被排除了)
      if (env.DEBUG) {
        console.log(`[DO-ROUTING] ⚠️ No available hints in Priority Tier ${i+1} (${tierName}). Trying next tier...`);
      }
    }
  }

  // 4. 【兜底逻辑】如果所有层级的所有区域都被排除了
  console.warn('[DO-ROUTING] CRITICAL: All location hints in all priority tiers were excluded. Falling back to default Cloudflare routing.');
  // 返回 undefined，让 Cloudflare 使用其默认的"就近"逻辑
  // 这是最安全的做法，保证服务可用性。
  return undefined;
}

/**
 * 【增强】检测是否为数据中心级别的可重试错误
 * @param {Error} error - 错误对象
 * @param {number} status - HTTP状态码
 * @param {object} originalErrorData - 原始错误数据对象
 * @returns {boolean} 是否为可重试的数据中心错误
 */
function isDataCenterRetryableError(error, status, originalErrorData = null) {
  // 1. HTTP 429 (Too Many Requests) - 明确的配额限制
  if (status === 429) {
    return true;
  }

  // 2. HTTP 503 (Service Unavailable) - 服务暂时不可用
  if (status === 503) {
    return true;
  }

  // 3. 【新增】HTTP 401 配额相关错误
  if (status === 401) {
    // 检查是否是配额相关的401错误
    if (originalErrorData?.detail?.status === 'quota_exceeded') {
      return true;
    }
  }

  // 4. 检查错误消息中的关键词
  const errorMessage = error.message?.toLowerCase() || '';
  const retryableKeywords = [
    'quota',
    'quota_exceeded',
    'rate limit',
    'too many requests',
    'service unavailable',
    'temporarily unavailable',
    'capacity',
    'overloaded',
    'reached the limit'
  ];

  const isRetryableByMessage = retryableKeywords.some(keyword =>
    errorMessage.includes(keyword)
  );

  if (isRetryableByMessage) {
    return true;
  }

  // 5. 【新增】检查原始错误数据中的状态字段
  if (originalErrorData?.detail?.status) {
    const detailStatus = originalErrorData.detail.status.toLowerCase();
    const retryableStatuses = ['quota_exceeded', 'rate_limited', 'capacity_exceeded'];
    if (retryableStatuses.includes(detailStatus)) {
      return true;
    }
  }

  // 6. 网络级别错误（可能是数据中心网络问题）
  if (error.name === 'TypeError' && errorMessage.includes('fetch')) {
    return true;
  }

  // 7. 超时错误
  if (errorMessage.includes('timeout') || errorMessage.includes('timed out')) {
    return true;
  }

  return false;
}

// ========== Voice ID映射缓存 ==========
// 全局缓存变量，用于缓存从KV获取的voiceIdMapping
let voiceIdMappingCache = null;
let cacheExpireTime = 0;
const CACHE_TTL_MS = 5 * 60 * 1000; // 缓存5分钟

/**
 * 从KV获取并缓存Voice ID映射
 * @param {object} env - Cloudflare环境变量
 * @returns {Promise<object>}
 */
async function getVoiceIdMapping(env) {
  const now = Date.now();

  // 1. 检查内存缓存是否有效
  if (voiceIdMappingCache && now < cacheExpireTime) {
    if (env.DEBUG) {
      console.log('[CACHE] Hit memory cache for voiceIdMapping.');
    }
    return voiceIdMappingCache;
  }

  // 2. 缓存无效，从KV获取
  try {
    if (env.DEBUG) {
      console.log('[KV-FETCH] Fetching voiceIdMapping from KV store.');
    }

    const mappingJson = await env.VOICE_MAPPINGS.get("voices_v1", {
      type: "text",
      cacheTtl: 60 // CF边缘缓存60秒
    });

    if (!mappingJson) {
      console.error('CRITICAL: voiceIdMapping not found in KV namespace "VOICE_MAPPINGS" with key "voices_v1"');
      return {};
    }

    const mapping = JSON.parse(mappingJson);

    // 3. 更新内存缓存和过期时间
    voiceIdMappingCache = mapping;
    cacheExpireTime = now + CACHE_TTL_MS;

    if (env.DEBUG) {
      console.log(`[CACHE] Updated memory cache for voiceIdMapping. Next refresh in ${CACHE_TTL_MS / 1000}s.`);
    }

    return mapping;
  } catch (error) {
    console.error('Failed to get or parse voiceIdMapping from KV:', error);
    // 在出错时，如果缓存中还有旧数据，返回旧数据保证服务韧性
    if (voiceIdMappingCache) {
      console.warn('Returning stale voiceIdMapping cache due to KV fetch error.');
      return voiceIdMappingCache;
    }
    return {}; // 最终回退
  }
}

/**
 * 根据声音名称获取其对应的Voice ID
 * 封装了查找逻辑和回退机制
 * @param {string} voiceName - 要查找的声音名称 (e.g., "Adam")
 * @param {object} env - Cloudflare环境变量
 * @returns {Promise<string>} - 返回找到的Voice ID，如果找不到则返回原始名称
 */
async function getVoiceId(voiceName, env) {
  const mapping = await getVoiceIdMapping(env);
  // 如果在映射中找到，则返回ID；否则，返回原始输入（假定它本身就是ID）
  return mapping[voiceName] || voiceName;
}

// 【已迁移到KV】硬编码的voiceIdMapping已迁移到KV存储
// 现在通过getVoiceIdMapping()和getVoiceId()函数动态获取

// 生成基于年月日时分秒的文件名
function generateDateBasedFilename() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  return `tts_${year}${month}${day}_${hours}${minutes}${seconds}.mp3`;
}

// 【新增】获取下个月第一天的时间戳，用于月度重置
function getNextMonthResetTimestamp() {
  const now = new Date();
  // 设置为下个月的第一天的 0 点 0 分 0 秒
  const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
  return nextMonth.getTime();
}

// 【新增】检查管理员权限
async function checkAdminPermission(username, env) {
  // 从环境变量获取管理员列表，默认为空数组
  const adminList = env.ADMIN_USERS?.split(',').map(u => u.trim()).filter(u => u) || [];

  if (adminList.length === 0) {
    console.warn('[ADMIN-CHECK] No admin users configured in ADMIN_USERS environment variable');
    throw new Error('管理员功能未配置');
  }

  if (!adminList.includes(username)) {
    console.warn(`[ADMIN-CHECK] User ${username} attempted to access admin function`);
    throw new Error('需要管理员权限');
  }

  console.log(`[ADMIN-CHECK] Admin access granted for user: ${username}`);
}

// 【新增】批量获取所有用户的用量数据
async function getAllUsersUsage(env, limit = 100, cursor = null) {
  try {
    console.log(`[ADMIN-USAGE] Starting to fetch users usage data. Limit: ${limit}, Cursor: ${cursor || 'null'}`);

    // 获取用户列表
    const listOptions = { prefix: 'user:', limit };
    if (cursor) {
      listOptions.cursor = cursor;
    }

    const userKeys = await env.USERS.list(listOptions);
    console.log(`[ADMIN-USAGE] Found ${userKeys.keys.length} user keys`);

    // 并行获取用户数据
    const promises = userKeys.keys.map(async (key) => {
      try {
        const userDataString = await env.USERS.get(key.name);
        if (!userDataString) {
          console.warn(`[ADMIN-USAGE] No data found for key: ${key.name}`);
          return null;
        }

        const userData = JSON.parse(userDataString);
        const username = key.name.replace('user:', '');

        // 确保usage数据存在（向后兼容）
        const usage = userData.usage || {
          totalChars: 0,
          monthlyChars: 0,
          monthlyResetAt: getNextMonthResetTimestamp()
        };

        // 检查月度重置（但不写回，避免大量写操作）
        const now = Date.now();
        if (now >= usage.monthlyResetAt) {
          usage.monthlyChars = 0;
          usage.monthlyResetAt = getNextMonthResetTimestamp();
        }

        return {
          username,
          usage,
          // 可选：添加一些基本用户信息
          createdAt: userData.createdAt,
          vip: userData.vip || { expireAt: 0, type: null }
        };
      } catch (error) {
        console.error(`[ADMIN-USAGE] Error processing user ${key.name}:`, error);
        return null;
      }
    });

    const results = await Promise.all(promises);
    const usageData = results.filter(item => item !== null);

    console.log(`[ADMIN-USAGE] Successfully processed ${usageData.length} users`);

    return {
      users: usageData,
      pagination: {
        limit,
        hasMore: !userKeys.list_complete,
        cursor: userKeys.cursor || null,
        total: usageData.length
      },
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('[ADMIN-USAGE] Error fetching users usage:', error);
    throw error;
  }
}

// 添加在配置部分之后
function corsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };
}

// 处理 OPTIONS 请求
function handleOptions(request) {
  return new Response(null, {
    headers: corsHeaders()
  });
}

// 【新增】统一的认证错误处理函数
// 根据错误消息生成结构化的错误响应，便于前端识别和处理
function createAuthErrorResponse(error) {
  // 根据错误消息判断错误类型
  let errorCode = 'AUTH_ERROR';
  let errorMessage = error.message;

  if (error.message === 'Token expired') {
    errorCode = 'TOKEN_EXPIRED';
  } else if (error.message === 'Invalid token' || error.message === 'Invalid signature') {
    errorCode = 'TOKEN_INVALID';
  } else if (error.message === 'Invalid token type') {
    errorCode = 'TOKEN_TYPE_INVALID';
  }

  return new Response(JSON.stringify({
    error: errorMessage,
    code: errorCode // 新增错误码字段，便于前端统一处理
  }), {
    status: 401,
    headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
  });
}

// 【新增】判断是否为认证相关错误的辅助函数
function isAuthError(error) {
  return error.message === 'Token expired' ||
         error.message === 'Invalid token' ||
         error.message === 'Invalid signature' ||
         error.message === 'Invalid token type';
}

// ========== TTS Task Durable Object ==========
// 【完整功能】TTS任务处理的Durable Object
// 每个TTS任务都会创建一个独立的DO实例，支持WebSocket实时通信
export class TtsTaskDo {
  constructor(state, env) {
    this.state = state;
    this.env = env;
    this.sessions = []; // 存储连接到此DO的WebSocket会话
    this.taskData = {}; // 在内存中存储当前任务的数据

    // 从持久化存储中恢复任务数据，以防DO被唤醒
    // 这对于长时间运行的任务很重要
    this.state.storage.get('taskData').then(data => {
        if (data) this.taskData = data;
    });
  }

  // 【新增】alarm 方法，用于DO的自我清理
  async alarm() {
    console.log(`[DO-ALARM] Triggered for task: ${this.state.id.toString()}.`);

    // 1. 防御性地关闭所有可能残留的 WebSocket 连接
    this.sessions.forEach(session => {
        try {
            // 使用标准关闭码 1000 (Normal Closure)
            session.close(1000, "Task object is being garbage collected.");
        } catch (e) {
            // 忽略错误，因为会话可能已经被客户端关闭
        }
    });
    console.log(`[DO-ALARM] Closed any lingering WebSocket sessions.`);

    // 2. 删除此DO实例的所有持久化状态
    await this.state.storage.deleteAll();
    console.log(`[DO-ALARM] State for task ${this.state.id.toString()} has been deleted.`);
  }

  /**
   * 【新增】获取当前DO实例的数据中心位置
   * @returns {string|null} 当前数据中心代码
   */
  getCurrentLocation() {
    // 尝试从任务数据中获取位置信息
    if (this.taskData && this.taskData.currentLocation) {
      return this.taskData.currentLocation;
    }

    // 如果没有存储位置信息，返回null
    // 在实际部署中，可以通过CF-RAY header或其他方式获取
    return null;
  }

  async fetch(request) {
    // 【新增】记录DO实际运行位置到Analytics Engine
    const taskId = this.state.id.toString();
    const actualColo = request.cf?.colo || "unknown";

    try {
      // 检查是否已经记录过，避免重复记录
      const alreadyRecorded = await this.state.storage.get('analytics_recorded');
      if (!alreadyRecorded && this.env.DO_ANALYTICS) {
        this.env.DO_ANALYTICS.writeDataPoint({
          blobs: [
            taskId,
            "actual_location",
            actualColo,
            "do_created",
            request.cf?.country || "unknown",
            new Date().toISOString()
          ],
          doubles: [1],
          indexes: [taskId, actualColo]
        });

        // 设置标志位，防止重复记录
        await this.state.storage.put('analytics_recorded', true);

        if (this.env.DEBUG) {
          console.log(`[ANALYTICS] Recorded actual location for task ${taskId}, colo: ${actualColo}`);
        }
      }
    } catch (analyticsError) {
      if (this.env.DEBUG) {
        console.error('[ANALYTICS] Failed to record actual location:', analyticsError);
      }
      // 不影响主流程，继续执行
    }

    const upgradeHeader = request.headers.get('Upgrade');
    if (upgradeHeader !== 'websocket') {
      return new Response('Expected Upgrade: websocket', { status: 426 });
    }

    const [client, server] = Object.values(new WebSocketPair());

    // 将 server-side WebSocket 交给我们自己处理
    server.accept();
    this.handleSession(server);

    // 将 client-side WebSocket 返回给运行时
    return new Response(null, {
      status: 101,
      webSocket: client,
    });
  }

  async handleSession(webSocket) {
    this.sessions.push(webSocket);

    webSocket.addEventListener('message', async (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.action === 'start') {
          // 【改进】检查任务是否已在运行
          if (this.taskData && this.taskData.status === 'processing') {
            webSocket.send(JSON.stringify({ type: 'error', message: 'Task is already running.' }));
            return;
          }

          // 将任务数据存储在DO的内存中
          this.taskData = {
            ...data,
            status: 'processing', // 添加一个内部状态
            taskId: this.state.id.toString(), // DO的ID就是我们的任务ID
            model: data.model || "eleven_turbo_v2" // 默认模型
          };
          // 持久化存储，以备不时之需
          await this.state.storage.put('taskData', this.taskData);

          // 【新增】根据 taskType 分发任务
          if (data.taskType === 'dialogue') {
            await this.runDialogueTtsProcess();
          } else {
            // 默认处理单人TTS任务
            await this.runSingleTtsProcess();
          }
        } else if (data.action === 'retry') {
          // 【新增】处理重试请求，支持任务状态恢复
          if (this.taskData && this.taskData.status === 'processing') {
            webSocket.send(JSON.stringify({ type: 'error', message: 'Task is already running.' }));
            return;
          }

          // 从重试数据中恢复任务状态
          if (data.recoveryData) {
            this.taskData = {
              ...data.recoveryData,
              status: 'processing',
              taskId: this.state.id.toString(),
              isRetry: true,
              retryCount: (data.recoveryData.retryCount || 0) + 1
            };
          } else {
            // 如果没有恢复数据，使用当前数据
            this.taskData = {
              ...data,
              status: 'processing',
              taskId: this.state.id.toString(),
              model: data.model || "eleven_turbo_v2",
              isRetry: true,
              retryCount: 1
            };
          }

          // 持久化存储
          await this.state.storage.put('taskData', this.taskData);

          if (this.env.DEBUG) {
            console.log(`[DO-RETRY] Starting retry attempt ${this.taskData.retryCount} for task ${this.taskData.taskId}`);
          }

          // 根据任务类型分发
          if (this.taskData.taskType === 'dialogue') {
            await this.runDialogueTtsProcess();
          } else {
            await this.runSingleTtsProcess();
          }
        }
      } catch (error) {
        console.error('WebSocket message processing error:', error);
        this.broadcast({ type: 'error', message: 'Invalid message format' });
      }
    });

    webSocket.addEventListener('close', () => {
      this.sessions = this.sessions.filter(ws => ws !== webSocket);
    });

    webSocket.addEventListener('error', (err) => {
        console.error("WebSocket error:", err);
    });
  }

  // 【核心修改】将 processAudioAsync 的逻辑移入此处，并增加 alarm 清理机制
  // 【名称变更】runTtsProcess -> runSingleTtsProcess
  async runSingleTtsProcess() {
    // 【优化】定义不同的清理延迟时间
    const SUCCESS_CLEANUP_DELAY_MS = 1 * 24 * 60 * 60 * 1000; // 成功任务状态保留1天
    const FAILURE_CLEANUP_DELAY_MS = 1 * 60 * 60 * 1000;     // 失败任务状态保留1小时
    let cleanupDelay = FAILURE_CLEANUP_DELAY_MS; // 默认为失败时的清理延迟

    // 从 this.taskData 获取所需参数
    const { taskId, input, voice, stability, similarity_boost, style, speed, model, token } = this.taskData;

    // 【修复】智能处理 voice 参数
    // 优先尝试通过名称在映射中查找ID（兼容旧版客户端）
    const voiceId = await getVoiceId(voice, this.env);

    try {
        // 【安全增强】在DO内部直接从token获取可信的用户名
        const username = await verifyToken(token, this.env);

        // --- START: 调整顺序 ---
        // 1. 先计算字符数
        const charCount = input.length;

        // 2. 再调用增强后的 checkVip 进行检查（包含配额检查）
        await checkVip(username, this.env, 'STANDARD', charCount);
        // --- END: 调整顺序 ---

        // -------------------------------------------------------------
        // ↓↓↓ 以下是将 processAudioAsync 的逻辑粘贴并改造 ↓↓↓
        // -------------------------------------------------------------

        // 【改进】使用智能进度消息广播，可通过环境变量控制
        this.broadcastProgress('任务初始化...');

        // 步骤2: 文本分割
        const chunks = await splitText(input);
        this.broadcastProgress(`文本已分割为 ${chunks.length} 个片段`);

        // 步骤3: TTS音频生成
        this.broadcastProgress(`正在生成 ${chunks.length} 个音频片段...`);
        const audioDataList = await processChunks(chunks, voiceId, model, stability, similarity_boost, style, speed, this.env);

        // 步骤4: 音频合并
        this.broadcastProgress('正在合并音频...');
        const totalLength = audioDataList.reduce((acc, curr) => acc + curr.byteLength, 0);
        const combinedAudioData = new Uint8Array(totalLength);
        let offset = 0;
        for (const audioData of audioDataList) {
          combinedAudioData.set(new Uint8Array(audioData), offset);
          offset += audioData.byteLength;
        }

        // 步骤5: R2存储
        this.broadcastProgress('正在将文件存入云存储...');
        await storeAudioFile(taskId, combinedAudioData.buffer, this.env);

        // 步骤6: 任务完成
        const r2DirectUrl = R2_DIRECT_DOWNLOAD_CONFIG.generateUrl(taskId);
        const finalStatus = {
            status: 'complete',
            downloadUrl: r2DirectUrl,
            audioSize: totalLength,
            username: username,
            completedAt: Date.now(),
        };

        // 【新增】更新用户字符数统计
        await this.updateUserUsage(username, charCount);

        // 将最终结果存入KV，用于历史记录查询
        await storeStatusKV(this.env, taskId, finalStatus);

        // 通过WebSocket发送最终成功结果
        this.broadcast({ type: 'complete', ...finalStatus });

        // 更新内部状态为完成
        this.taskData.status = 'complete';
        await this.state.storage.put('taskData', this.taskData);

        // 【优化】任务成功，设置较长的清理延迟
        cleanupDelay = SUCCESS_CLEANUP_DELAY_MS;

    } catch (error) {
        console.error(`[DO-TASK] ${taskId} failed:`, error);

        // 【新增】检查是否为数据中心级别的可重试错误
        if (error.isDataCenterRetryable) {
            console.log(`[DO-TASK] ${taskId} failed with retryable error, suggesting datacenter switch`);

            // 获取当前DO的位置信息（从环境或状态中）
            const currentLocation = this.getCurrentLocation();
            const excludeLocations = currentLocation ? [currentLocation] : [];

            const retryablePayload = {
                type: 'error_retryable',
                message: '当前数据中心暂时不可用，正在尝试切换到其他区域...',
                error: error.message,
                excludeLocations: excludeLocations,
                taskData: {
                    // 保存任务的关键信息供重试使用
                    input: this.taskData.input,
                    voice: this.taskData.voice,
                    model: this.taskData.model,
                    stability: this.taskData.stability,
                    similarity_boost: this.taskData.similarity_boost,
                    style: this.taskData.style,
                    speed: this.taskData.speed,
                    username: this.taskData.username,
                    taskType: this.taskData.taskType
                }
            };

            this.broadcast(retryablePayload);

            // 更新内部状态为可重试失败
            this.taskData.status = 'retryable_failed';
            await this.state.storage.put('taskData', this.taskData);

            // 将可重试失败状态存入KV
            await storeStatusKV(this.env, taskId, {
                status: 'retryable_failed',
                error: error.message,
                username: this.taskData.username,
                excludeLocations: excludeLocations
            });
        } else {
            // 不可重试的错误，按原有逻辑处理
            const errorPayload = {
                type: 'error',
                message: error.message || '任务处理失败'
            };
            // 检查是否是会员错误
            if (error.cause === 'quota') {
                errorPayload.message = '会员权限不足或已过期，请充值。';
            }
            this.broadcast(errorPayload);

            // 更新内部状态为失败
            this.taskData.status = 'failed';
            await this.state.storage.put('taskData', this.taskData);

            // 同样将失败状态存入KV
            await storeStatusKV(this.env, taskId, { status: 'failed', error: error.message, username: this.taskData.username });
        }
    } finally {
        // 【优化】无论成功或失败，都在此执行最终的资源释放和清理调度

        // 1. 关闭所有 WebSocket 连接，通知客户端任务已结束
        console.log(`[DO-TASK] Closing all WebSocket sessions for task ${this.state.id.toString()}.`);
        this.sessions.forEach(s => {
            try {
                s.close(1000, "Task finished.");
            } catch (e) { /* 忽略错误 */ }
        });

        // 2. 计算最终的清理时间
        const cleanupTime = Date.now() + cleanupDelay;

        // 3. 使用 blockConcurrencyWhile 安全地设置闹钟，防止并发问题
        console.log(`[DO-TASK] Task ${this.state.id.toString()} finished. Scheduling cleanup for ${new Date(cleanupTime).toISOString()}.`);
        try {
            await this.state.blockConcurrencyWhile(async () => {
                await this.state.storage.setAlarm(cleanupTime);
            });
            console.log(`[DO-TASK] Cleanup alarm set successfully.`);
        } catch (alarmError) {
            // 记录设置闹钟失败的错误，这是需要关注的重要运维问题
            console.error(`[DO-TASK] CRITICAL: Failed to set cleanup alarm for task ${this.state.id.toString()}:`, alarmError);
        }
    }
  }

  /**
   * 【新增】处理多人对话音频生成的函数
   * 编排整个多人对话的生成过程，并充分利用现有工具函数
   */
  async runDialogueTtsProcess() {
    const SUCCESS_CLEANUP_DELAY_MS = 1 * 24 * 60 * 60 * 1000;
    const FAILURE_CLEANUP_DELAY_MS = 1 * 60 * 60 * 1000;
    let cleanupDelay = FAILURE_CLEANUP_DELAY_MS;

    const { taskId, dialogue, model, stability, similarity_boost, style, speed, token } = this.taskData;

    try {
      const username = await verifyToken(token, this.env);

      // --- START: 调整顺序 ---
      // 1. 先计算总字符数
      const charCount = dialogue.reduce((sum, speaker) => sum + (speaker.text ? speaker.text.length : 0), 0);

      // 2. 再调用增强后的 checkVip 进行检查 (PRO权限 + 配额检查)
      await checkVip(username, this.env, 'PRO', charCount);
      // --- END: 调整顺序 ---

      this.broadcastProgress('初始化多人对话任务...');

      if (!Array.isArray(dialogue) || dialogue.length === 0) {
        throw new Error('请求中的 "dialogue" 必须是一个非空数组。');
      }

      const allSpeakersAudioBuffers = [];

      for (let i = 0; i < dialogue.length; i++) {
        const speaker = dialogue[i];
        const speakerNumber = i + 1;
        const totalSpeakers = dialogue.length;

        this.broadcastProgress(`处理第 ${speakerNumber}/${totalSpeakers} 位说话者 (${speaker.voice})...`);

        const voiceId = await getVoiceId(speaker.voice, this.env);

        const chunks = await splitText(speaker.text);
        this.broadcastProgress(`第 ${speakerNumber} 位说话者的文本已分割为 ${chunks.length} 个片段，开始并发生成...`);

        const speakerAudioList = await processChunks(
          chunks, voiceId, model, stability, similarity_boost, style, speed, this.env
        );

        if (speakerAudioList.length === 0) {
          throw new Error(`未能为第 ${speakerNumber} 位说话者 (${speaker.voice}) 生成任何音频。`);
        }

        const speakerCombinedAudio = combineAudio(speakerAudioList);
        allSpeakersAudioBuffers.push(speakerCombinedAudio);

        this.broadcastProgress(`第 ${speakerNumber}/${totalSpeakers} 位说话者音频生成完毕。`);
      }

      this.broadcastProgress('正在合并所有对话音频...');
      const finalAudio = combineAudio(allSpeakersAudioBuffers);

      this.broadcastProgress('正在将文件存入云存储...');
      await storeAudioFile(taskId, finalAudio, this.env);

      const r2DirectUrl = R2_DIRECT_DOWNLOAD_CONFIG.generateUrl(taskId);
      const finalStatus = {
        status: 'complete',
        downloadUrl: r2DirectUrl,
        audioSize: finalAudio.byteLength,
        username: username,
        completedAt: Date.now(),
      };

      // 【新增】更新用户字符数统计
      await this.updateUserUsage(username, charCount);

      await storeStatusKV(this.env, taskId, finalStatus);
      this.broadcast({ type: 'complete', ...finalStatus });

      this.taskData.status = 'complete';
      await this.state.storage.put('taskData', this.taskData);
      cleanupDelay = SUCCESS_CLEANUP_DELAY_MS;

    } catch (error) {
      console.error(`[DO-DIALOGUE-TASK] ${taskId} failed:`, error);
      const errorPayload = {
        type: 'error',
        message: error.message || '多人对话任务处理失败'
      };
      if (error.cause === 'quota') {
        // 根据错误信息判断是权限等级不足还是会员过期
        if (error.message.includes('PRO会员权限')) {
          errorPayload.message = '多人对话功能需要PRO会员权限，请升级到PRO套餐后使用。';
        } else {
          errorPayload.message = '会员权限不足或已过期，请充值。';
        }
      }
      this.broadcast(errorPayload);

      this.taskData.status = 'failed';
      await this.state.storage.put('taskData', this.taskData);
      await storeStatusKV(this.env, taskId, { status: 'failed', error: error.message, username: this.taskData.username });
    } finally {
      console.log(`[DO-TASK] Closing all WebSocket sessions for task ${this.state.id.toString()}.`);
      this.sessions.forEach(s => {
        try { s.close(1000, "Task finished."); } catch (e) { /* 忽略错误 */ }
      });

      const cleanupTime = Date.now() + cleanupDelay;
      console.log(`[DO-TASK] Task ${this.state.id.toString()} finished. Scheduling cleanup for ${new Date(cleanupTime).toISOString()}.`);
      try {
        await this.state.blockConcurrencyWhile(async () => {
          await this.state.storage.setAlarm(cleanupTime);
        });
        console.log(`[DO-TASK] Cleanup alarm set successfully.`);
      } catch (alarmError) {
        console.error(`[DO-TASK] CRITICAL: Failed to set cleanup alarm for task ${this.state.id.toString()}:`, alarmError);
      }
    }
  }

  broadcast(message) {
    const serializedMessage = JSON.stringify(message);
    this.sessions.forEach(session => {
      try {
        session.send(serializedMessage);
      } catch (error) {
        // 如果发送失败，可能是会话已关闭，可以安全地忽略或记录日志
        console.error('Broadcast error to a session:', error);
      }
    });
  }

  /**
   * 【新增】更新用户的字符使用量
   * @param {string} username - 用户名
   * @param {number} charCount - 本次任务使用的字符数
   */
  async updateUserUsage(username, charCount) {
    if (!username || charCount <= 0) {
      return;
    }

    try {
      const userKey = `user:${username}`;
      // 注意：直接从 env.USERS 获取最新数据，而不是依赖可能过时的 taskData
      const userDataString = await this.env.USERS.get(userKey);
      if (!userDataString) {
        console.error(`[USAGE-UPDATE] User not found: ${username}`);
        return;
      }

      const userData = JSON.parse(userDataString);

      // --- START: 新增逻辑 ---
      // 更新VIP套餐内的已用配额（仅对受配额限制的用户）
      if (userData.vip && userData.vip.quotaChars !== undefined) {
        // 只有受配额限制的用户才更新已用配额
        // 真正的老用户（quotaChars为undefined）不更新，保持无限字符权益
        if (userData.vip.usedChars === undefined) {
          userData.vip.usedChars = 0;
        }
        userData.vip.usedChars += charCount;
        console.log(`[USAGE-UPDATE] Updated quota usage for user ${username}: ${userData.vip.usedChars}/${userData.vip.quotaChars}`);
      } else if (userData.vip) {
        console.log(`[USAGE-UPDATE] Legacy user ${username} - skipping quota tracking`);
      }
      // --- END: 新增逻辑 ---

      // 初始化 usage 对象（向后兼容老用户）
      if (!userData.usage) {
        userData.usage = {
          totalChars: 0,
          monthlyChars: 0,
          monthlyResetAt: getNextMonthResetTimestamp()
        };
      }

      const now = Date.now();
      // 检查月度数据是否需要重置
      if (now >= userData.usage.monthlyResetAt) {
        console.log(`[USAGE-UPDATE] Resetting monthly usage for user: ${username}`);
        userData.usage.monthlyChars = 0;
        userData.usage.monthlyResetAt = getNextMonthResetTimestamp();
      }

      // 累加字符数
      userData.usage.totalChars += charCount;
      userData.usage.monthlyChars += charCount;

      // 将更新后的用户数据写回 KV
      await this.env.USERS.put(userKey, JSON.stringify(userData));

      console.log(`[USAGE-UPDATE] Successfully updated usage for ${username}. Added: ${charCount}, VIP Used: ${userData.vip?.usedChars || 0}, Monthly total: ${userData.usage.monthlyChars}`);

    } catch (error) {
      console.error(`[USAGE-UPDATE] Failed to update usage for user ${username}:`, error);
      // 即使更新失败，也不应该影响主任务流程，所以只记录错误
    }
  }

  // 【新增】智能进度消息广播方法
  // 根据环境变量控制是否发送详细进度消息
  broadcastProgress(message) {
    const progressConfig = getProgressConfig(this.env);

    // 如果启用了进度消息，则发送
    if (progressConfig.ENABLE_PROGRESS_MESSAGES) {
      this.broadcast({ type: 'progress', message });
    }

    // 如果启用了调试模式，总是在控制台输出
    if (progressConfig.ENABLE_DEBUG_PROGRESS) {
      console.log(`[PROGRESS] ${this.taskData?.taskId || 'unknown'}: ${message}`);
    }
  }
}

// ========== Utils 函数 ==========
// 从 utils.js

/**
 * 解析 HTTP Range 请求头
 * @param {string} header - Range 请求头的值 (e.g., "bytes=0-1023")
 * @param {number} totalSize - 文件的总大小
 * @returns {{start: number, end: number, length: number}|null} - 解析后的范围对象或 null
 */
function parseRange(header, totalSize) {
  if (!header) return null;

  const match = header.match(/bytes=(\d+)-(\d*)/);
  if (!match) {
    return null;
  }

  const start = parseInt(match[1], 10);
  // 如果末尾为空（如 "bytes=100-"），则表示到文件末尾
  const end = match[2] ? parseInt(match[2], 10) : totalSize - 1;

  // 范围有效性检查
  if (isNaN(start) || isNaN(end) || start > end || start >= totalSize) {
    return null;
  }

  const length = end - start + 1;
  return { start, end, length };
}

/**
 * 高效处理音频下载请求，支持 Range 请求（断点续传）
 * @param {Request} request - 原始请求对象
 * @param {object} env - Cloudflare Worker 的环境对象
 * @param {string} username - 用户名（用于日志记录）
 * @returns {Promise<Response>}
 */
async function handleDownload(request, env, username) {
  const url = new URL(request.url);
  const taskId = url.pathname.split('/download/')[1];

  if (env.DEBUG) {
    console.log(`[DOWNLOAD] 📥 Received Worker proxy download request (backup method):`, {
      taskId: taskId,
      username: username,
      url: request.url,
      method: request.method,
      downloadMethod: 'WORKER_PROXY_BACKUP',
      note: 'This is backup download method, R2 direct link is preferred',
      headers: Object.fromEntries(request.headers.entries()),
      timestamp: new Date().toISOString()
    });
  }

  if (!taskId) {
    console.error(`[DOWNLOAD] ❌ Missing task ID in download request`);
    return new Response(JSON.stringify({ error: 'Task ID is required' }), {
      status: 400,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });
  }

  // 检查任务状态
  if (env.DEBUG) {
    console.log(`[DOWNLOAD] 📋 Checking task status for download: ${taskId}`);
  }
  const taskStatus = await getStatusKV(env, taskId);

  if (!taskStatus || taskStatus.status !== 'complete') {
    console.warn(`[DOWNLOAD] ⚠️ Audio not ready for download: ${taskId}, status: ${taskStatus?.status || 'not_found'}`);
    return new Response(JSON.stringify({ error: 'Audio not ready or task not found' }), {
      status: 404,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });
  }

  const key = `audios/${taskId}.mp3`;

  if (env.DEBUG) {
    console.log(`[DOWNLOAD] 📁 Fetching audio file from R2:`, {
      taskId: taskId,
      key: key,
      bucketName: 'AUDIOS',
      envAUDIOS: !!env.AUDIOS,
      envType: typeof env.AUDIOS
    });
  }

  // 1. 获取文件元数据 (大小) - 优化：使用head而不是get
  const objectMetadata = await env.AUDIOS.head(key);
  if (objectMetadata === null) {
    console.error(`[DOWNLOAD] ❌ Audio file not found in R2: ${taskId}`);
    return new Response(JSON.stringify({ error: 'Audio file not found' }), {
      status: 404,
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });
  }
  const totalSize = objectMetadata.size;

  if (env.DEBUG) {
    console.log(`[DOWNLOAD] ✅ Successfully found audio file:`, {
      taskId: taskId,
      fileSize: totalSize,
      fileSizeMB: (totalSize / 1024 / 1024).toFixed(2),
      contentType: 'audio/mpeg',
      timestamp: new Date().toISOString()
    });
  }

  // 2. 解析 Range 请求头
  const rangeHeader = request.headers.get('range');
  if (env.DEBUG) {
    console.log(`[DOWNLOAD] 📊 Request headers analysis:`, {
      taskId: taskId,
      rangeHeader: rangeHeader,
      hasRange: !!rangeHeader,
      userAgent: request.headers.get('User-Agent'),
      accept: request.headers.get('Accept')
    });
  }

  const range = parseRange(rangeHeader, totalSize);

  // 3. 根据是否存在 Range 决定响应方式
  if (range === null) {
    // --- 情况 A: 完整文件下载 ---
    if (env.DEBUG) {
      console.log(`[DOWNLOAD] 📤 Serving complete file for task ${taskId}, size: ${totalSize}`);
    }
    const object = await env.AUDIOS.get(key);
    if (object === null) {
        return new Response('Audio file could not be retrieved.', { status: 404, headers: corsHeaders() });
    }

    return new Response(object.body, {
      status: 200,
      headers: {
        ...corsHeaders(),
        'Content-Type': 'audio/mpeg',
        'Content-Disposition': `attachment; filename="${generateDateBasedFilename()}"`,
        'Content-Length': totalSize.toString(),
        'Accept-Ranges': 'bytes', // 告知浏览器支持范围请求
        'Cache-Control': 'public, max-age=3600' // 缓存1小时
      }
    });

  } else {
    // --- 情况 B: 部分文件下载 (Range 请求) - 优化：使用R2的原生Range支持 ---
    if (env.DEBUG) {
      console.log(`[DOWNLOAD] 📏 Processing Range request for task ${taskId}: bytes ${range.start}-${range.end}`);
    }
    const object = await env.AUDIOS.get(key, {
      range: { offset: range.start, length: range.length }
    });
     if (object === null) {
        return new Response('Audio file range could not be retrieved.', { status: 404, headers: corsHeaders() });
    }

    return new Response(object.body, {
      status: 206, // Partial Content
      headers: {
        ...corsHeaders(),
        'Content-Type': 'audio/mpeg',
        'Content-Length': range.length.toString(),
        'Content-Range': `bytes ${range.start}-${range.end}/${totalSize}`,
        'Accept-Ranges': 'bytes',
        'Content-Disposition': `attachment; filename="${generateDateBasedFilename()}"`,
        'Cache-Control': 'public, max-age=3600'
      }
    });
  }
}

// ========== KV 状态管理函数 ==========
// 任务状态在 KV 中的 TTL (秒)，24小时
const TASK_STATUS_TTL = 1 * 24 * 60 * 60;

/**
 * 将任务状态写入或覆盖到 KV
 * @param {object} env - The environment object.
 * @param {string} taskId - The ID of the task.
 * @param {object} data - The status data to store.
 */
async function storeStatusKV(env, taskId, data) {
  try {
    const key = `status:${taskId}`;
    const statusData = {
      ...data,
      updatedAt: Date.now()
    };

    if (env.DEBUG) {
      console.log(`[KV-STATUS] Storing status for task ${taskId}:`, {
        status: statusData.status,
        currentStep: statusData.currentStep,
        dataSize: JSON.stringify(statusData).length,
        key: key
      });
    }

    await env.TTS_STATUS.put(key, JSON.stringify(statusData), {
      expirationTtl: TASK_STATUS_TTL
    });

    if (env.DEBUG) {
      console.log(`[KV-STATUS] ✅ Successfully stored status for task ${taskId}`);
    }
  } catch (error) {
    console.error(`[KV-STATUS] ❌ Failed to store status for task ${taskId}:`, {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

/**
 * 从 KV 读取任务状态
 * @param {object} env - The environment object.
 * @param {string} taskId - The ID of the task.
 * @returns {Promise<object|null>} - The status data object, or null if not found.
 */
async function getStatusKV(env, taskId) {
  try {
    const key = `status:${taskId}`;

    if (env.DEBUG) {
      console.log(`[KV-STATUS] Getting status for task ${taskId}:`, { key: key });
    }

    // .get(key, 'json') 会自动处理 JSON.parse 和 null 的情况
    const statusData = await env.TTS_STATUS.get(key, 'json');

    if (statusData) {
      if (env.DEBUG) {
        console.log(`[KV-STATUS] ✅ Successfully retrieved status for task ${taskId}:`, {
          status: statusData.status,
          currentStep: statusData.currentStep,
          createdAt: statusData.createdAt ? new Date(statusData.createdAt).toISOString() : 'unknown'
        });
      }
    } else {
      if (env.DEBUG) {
        console.log(`[KV-STATUS] ⚠️ Status not found for task ${taskId}`);
      }
    }

    return statusData;
  } catch (error) {
    console.error(`[KV-STATUS] ❌ Failed to get status for task ${taskId}:`, {
      error: error.message,
      stack: error.stack
    });
    return null;
  }
}

/**
 * 局部更新 KV 中的任务状态 (非原子性，但对于此场景足够)
 * @param {object} env - The environment object.
 * @param {string} taskId - The ID of the task.
 * @param {object} fieldsToUpdate - The fields to merge into the existing status.
 */
async function patchStatusKV(env, taskId, fieldsToUpdate) {
  try {
    const currentStatus = (await getStatusKV(env, taskId)) || {};
    const newStatus = {
      ...currentStatus,
      ...fieldsToUpdate,
      updatedAt: Date.now()
    };
    await storeStatusKV(env, taskId, newStatus);

    if (env.DEBUG) {
      console.log(`[KV-STATUS] ✅ Successfully patched status for task ${taskId}:`, {
        updatedFields: Object.keys(fieldsToUpdate),
        newStatus: newStatus.status,
        newStep: newStatus.currentStep
      });
    }
  } catch (error) {
    console.error(`[KV-STATUS] ❌ Failed to patch status for task ${taskId}:`, {
      error: error.message,
      fieldsToUpdate: Object.keys(fieldsToUpdate)
    });
    throw error;
  }
}

export async function generateToken(username, env, isRefreshToken = false) {
  const AUTH_CONFIG = getAuthConfig(env);
  const expireTime = isRefreshToken ?
    AUTH_CONFIG.REFRESH_TOKEN_EXPIRE :
    AUTH_CONFIG.ACCESS_TOKEN_EXPIRE;

  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
  const payload = btoa(JSON.stringify({
    sub: username,
    exp: Date.now() + expireTime * 1000,
    type: isRefreshToken ? 'refresh' : 'access'
  }));
  const signature = btoa(
    await hmacSha256(`${header}.${payload}`, AUTH_CONFIG.JWT_SECRET)
  );
  return `${header}.${payload}.${signature}`;
}

export async function verifyToken(token, env, allowRefresh = false) {
  const AUTH_CONFIG = getAuthConfig(env);
  try {
    const [header, payload, signature] = token.split('.');
    const expectedSignature = btoa(
      await hmacSha256(`${header}.${payload}`, AUTH_CONFIG.JWT_SECRET)
    );

    if (signature !== expectedSignature) {
      throw new Error('Invalid signature');
    }

    const decoded = JSON.parse(atob(payload));

    // 检查 token 类型
    if (!allowRefresh && decoded.type === 'refresh') {
      throw new Error('Invalid token type');
    }

    if (Date.now() > decoded.exp) {
      throw new Error('Token expired');
    }

    return decoded.sub;
  } catch (error) {
    throw new Error('Invalid token');
  }
}

export async function bcrypt(password, env) {
  const AUTH_CONFIG = getAuthConfig(env);
  const encoder = new TextEncoder();
  const data = encoder.encode(password + AUTH_CONFIG.JWT_SECRET);
  const hash = await crypto.subtle.digest('SHA-256', data);
  return btoa(String.fromCharCode(...new Uint8Array(hash)));
}

async function hmacSha256(message, key) {
  const encoder = new TextEncoder();
  const keyData = await crypto.subtle.importKey(
    'raw',
    encoder.encode(key),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );
  const signature = await crypto.subtle.sign(
    'HMAC',
    keyData,
    encoder.encode(message)
  );
  return String.fromCharCode(...new Uint8Array(signature));
}

// 腾讯云 API 签名相关函数
async function sha256Hex(message) {
  const encoder = new TextEncoder();
  const data = encoder.encode(message);
  const hash = await crypto.subtle.digest('SHA-256', data);
  return Array.from(new Uint8Array(hash))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

async function hmacSha256Hex(message, key) {
  const encoder = new TextEncoder();
  const keyData = await crypto.subtle.importKey(
    'raw',
    typeof key === 'string' ? encoder.encode(key) : key,
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );
  const signature = await crypto.subtle.sign(
    'HMAC',
    keyData,
    encoder.encode(message)
  );
  return Array.from(new Uint8Array(signature))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

async function hmacSha256Binary(message, key) {
  const encoder = new TextEncoder();
  const keyData = await crypto.subtle.importKey(
    'raw',
    typeof key === 'string' ? encoder.encode(key) : key,
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );
  const signature = await crypto.subtle.sign(
    'HMAC',
    keyData,
    encoder.encode(message)
  );
  return new Uint8Array(signature);
}

// 腾讯云 API v3 签名算法
async function generateTencentCloudSignature(secretId, secretKey, service, region, action, payload, timestamp) {
  const date = new Date(timestamp * 1000).toISOString().substring(0, 10);

  // 步骤 1: 拼接规范请求串
  const httpRequestMethod = 'POST';
  const canonicalUri = '/';
  const canonicalQueryString = '';
  const canonicalHeaders = `content-type:application/json; charset=utf-8\nhost:${service}.tencentcloudapi.com\nx-tc-action:${action.toLowerCase()}\n`;
  const signedHeaders = 'content-type;host;x-tc-action';
  const hashedRequestPayload = await sha256Hex(payload);

  const canonicalRequest = [
    httpRequestMethod,
    canonicalUri,
    canonicalQueryString,
    canonicalHeaders,
    signedHeaders,
    hashedRequestPayload
  ].join('\n');

  // 步骤 2: 拼接待签名字符串
  const algorithm = 'TC3-HMAC-SHA256';
  const requestTimestamp = timestamp.toString();
  const credentialScope = `${date}/${service}/tc3_request`;
  const hashedCanonicalRequest = await sha256Hex(canonicalRequest);

  const stringToSign = [
    algorithm,
    requestTimestamp,
    credentialScope,
    hashedCanonicalRequest
  ].join('\n');

  // 步骤 3: 计算签名
  const secretDate = await hmacSha256Binary(date, `TC3${secretKey}`);
  const secretService = await hmacSha256Binary(service, secretDate);
  const secretSigning = await hmacSha256Binary('tc3_request', secretService);
  const signature = await hmacSha256Hex(stringToSign, secretSigning);

  // 步骤 4: 拼接 Authorization
  const authorization = `${algorithm} Credential=${secretId}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;

  return {
    authorization,
    timestamp: requestTimestamp,
    hashedRequestPayload
  };
}

// 调用腾讯云 SES API 发送邮件
async function sendEmailViaTencentSES(toEmail, templateData, env) {
  const sesConfig = getSESConfig(env);
  const service = 'ses';
  const action = 'SendEmail';
  const version = '2020-10-02';
  const timestamp = Math.floor(Date.now() / 1000);

  // 调试日志
  console.log('SES Config check:', {
    hasSecretId: !!sesConfig.TENCENT_SECRET_ID,
    hasSecretKey: !!sesConfig.TENCENT_SECRET_KEY,
    region: sesConfig.SES_REGION,
    fromEmail: sesConfig.FROM_EMAIL,
    templateId: sesConfig.VERIFICATION_TEMPLATE_ID
  });

  // 构建请求体
  const payload = JSON.stringify({
    FromEmailAddress: `${sesConfig.FROM_EMAIL_NAME} <${sesConfig.FROM_EMAIL}>`,
    Destination: [toEmail],
    Subject: '邮箱验证码',
    Template: {
      TemplateID: parseInt(sesConfig.VERIFICATION_TEMPLATE_ID),
      TemplateData: JSON.stringify(templateData)
    },
    TriggerType: 1 // 触发类邮件
  });

  // 生成签名
  const signatureInfo = await generateTencentCloudSignature(
    sesConfig.TENCENT_SECRET_ID,
    sesConfig.TENCENT_SECRET_KEY,
    service,
    sesConfig.SES_REGION,
    action,
    payload,
    timestamp
  );

  // 构建请求头
  const headers = {
    'Authorization': signatureInfo.authorization,
    'Content-Type': 'application/json; charset=utf-8',
    'Host': `${service}.tencentcloudapi.com`,
    'X-TC-Action': action,
    'X-TC-Timestamp': signatureInfo.timestamp,
    'X-TC-Version': version,
    'X-TC-Region': sesConfig.SES_REGION
  };

  // 发送请求
  const response = await fetch(`https://${service}.tencentcloudapi.com/`, {
    method: 'POST',
    headers: headers,
    body: payload
  });

  const result = await response.json();

  if (!response.ok || result.Response.Error) {
    throw new Error(result.Response.Error?.Message || 'Failed to send email');
  }

  return result.Response.MessageId;
}

// 验证码相关函数
function generateVerificationCode() {
  return Math.floor(100000 + Math.random() * 900000).toString(); // 6位数字验证码
}

async function storeVerificationCode(email, code, env) {
  const expireTime = Date.now() + 10 * 60 * 1000; // 10分钟过期
  const verificationData = {
    code: code,
    expireTime: expireTime,
    attempts: 0,
    maxAttempts: 5
  };

  // 存储验证码，key格式：verification:email:<EMAIL>
  await env.USERS.put(`verification:email:${email}`, JSON.stringify(verificationData), {
    expirationTtl: 600 // 10分钟TTL
  });
}

async function verifyEmailCode(email, inputCode, env) {
  const verificationData = await env.USERS.get(`verification:email:${email}`);

  if (!verificationData) {
    throw new Error('验证码不存在或已过期');
  }

  const data = JSON.parse(verificationData);

  // 检查是否过期
  if (Date.now() > data.expireTime) {
    await env.USERS.delete(`verification:email:${email}`);
    throw new Error('验证码已过期');
  }

  // 检查尝试次数
  if (data.attempts >= data.maxAttempts) {
    await env.USERS.delete(`verification:email:${email}`);
    throw new Error('验证码尝试次数过多，请重新获取');
  }

  // 验证码错误，增加尝试次数
  if (data.code !== inputCode) {
    data.attempts += 1;
    await env.USERS.put(`verification:email:${email}`, JSON.stringify(data), {
      expirationTtl: Math.max(0, Math.floor((data.expireTime - Date.now()) / 1000))
    });
    throw new Error('验证码错误');
  }

  // 验证成功，删除验证码
  await env.USERS.delete(`verification:email:${email}`);
  return true;
}

// 检查邮箱发送频率限制
async function checkEmailSendLimit(email, env) {
  const limitKey = `email_limit:${email}`;
  const lastSendTime = await env.USERS.get(limitKey);

  if (lastSendTime) {
    const timeDiff = Date.now() - parseInt(lastSendTime);
    if (timeDiff < 60000) { // 1分钟内不能重复发送
      const remainingTime = Math.ceil((60000 - timeDiff) / 1000);
      throw new Error(`请等待 ${remainingTime} 秒后再试`);
    }
  }

  // 记录发送时间
  await env.USERS.put(limitKey, Date.now().toString(), {
    expirationTtl: 60 // 1分钟TTL
  });
}

// 添加 refreshAccessToken 函数
async function refreshAccessToken(refreshToken, env) {
  try {
    const username = await verifyToken(refreshToken, env, true);
    const newAccessToken = await generateToken(username, env);
    // 生成新的 refresh token
    const newRefreshToken = await generateToken(username, env, true);

    return {
      token: newAccessToken,
      refresh_token: newRefreshToken,
      username,
      expires_in: getAuthConfig(env).ACCESS_TOKEN_EXPIRE
    };
  } catch (error) {
    throw new Error('Invalid refresh token');
  }
}

// ========== Auth 处理函数 ==========
// 从 auth.js
async function handleAuth(request, env) {
  const url = new URL(request.url);

  if (request.method === 'POST' && url.pathname === '/api/auth/login') {
    const { username, password } = await request.json();

    try {
      let actualUsername = username;

      // 检查输入是否为邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (emailRegex.test(username)) {
        // 如果是邮箱，先查找对应的用户名
        const emailMapping = await env.USERS.get(`email:${username}`);
        if (!emailMapping) {
          return new Response(JSON.stringify({ error: '用户名或密码错误' }), {
            status: 400,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
          });
        }
        actualUsername = emailMapping;
      }

      const userData = await env.USERS.get(`user:${actualUsername}`);
      if (!userData) {
        return new Response(JSON.stringify({ error: '用户名或密码错误' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      const user = JSON.parse(userData);
      const hashedPassword = await bcrypt(password, env);

      if (hashedPassword !== user.passwordHash) {
        return new Response(JSON.stringify({ error: '用户名或密码错误' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 使用实际的用户名生成token
      const accessToken = await generateToken(actualUsername, env);
      const refreshToken = await generateToken(actualUsername, env, true);

      return new Response(JSON.stringify({
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in: getAuthConfig(env).ACCESS_TOKEN_EXPIRE
      }), {
        status: 200,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    } catch (error) {
      return new Response(JSON.stringify({ error: '登录失败' }), {
        status: 400,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }
  }

  // 添加刷新 token 接口
  if (request.method === 'POST' && url.pathname === '/api/auth/refresh') {
    try {
      const { refresh_token } = await request.json();
      const { token, refresh_token: newRefreshToken, expires_in } = await refreshAccessToken(refresh_token, env);

      return new Response(JSON.stringify({
        access_token: token,
        refresh_token: newRefreshToken,
        expires_in: expires_in
      }), {
        status: 200,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    } catch (error) {
      return new Response(JSON.stringify({
        error: 'Invalid refresh token',
        message: error.message
      }), {
        status: 401,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }
  }

  if (request.method === 'POST' && url.pathname === '/api/auth/register') {
    const { username, password } = await request.json();

    const existingUser = await env.USERS.get(`user:${username}`);
    if (existingUser) {
        return new Response(JSON.stringify({ error: '用户名已存在' }), {
            status: 400,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
    }

    const userData = {
        username,
        passwordHash: await bcrypt(password, env),
        createdAt: Date.now(),
        quota: {
            daily: 100,
            used: 0,
            resetAt: Date.now()
        },
        // 【新增】初始化VIP信息
        vip: {
            expireAt: 0,
            type: null,
            quotaChars: 0,    // 新用户初始配额为0
            usedChars: 0      // 新用户已用配额为0
        },
        // 【新增】初始化用量统计
        usage: {
            totalChars: 0,
            monthlyChars: 0,
            monthlyResetAt: getNextMonthResetTimestamp()
        }
    };

    // 先生成认证信息
    const accessToken = await generateToken(username, env);
    const refreshToken = await generateToken(username, env, true);

    // 存储用户数据
    await env.USERS.put(`user:${username}`, JSON.stringify(userData));

    // 返回与登录接口相同的数据结构
    return new Response(JSON.stringify({
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in: getAuthConfig(env).ACCESS_TOKEN_EXPIRE
    }), {
        status: 200,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });
  }

  // 新增：发送邮箱验证码接口
  if (request.method === 'POST' && url.pathname === '/api/auth/send-verification') {
    try {
      const { email, username, password } = await request.json();

      // 基本参数验证
      if (!email || !username || !password) {
        return new Response(JSON.stringify({ error: '邮箱、用户名和密码不能为空' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 邮箱格式验证
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return new Response(JSON.stringify({ error: '邮箱格式不正确' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 检查用户名是否已存在
      const existingUser = await env.USERS.get(`user:${username}`);
      if (existingUser) {
        return new Response(JSON.stringify({ error: '用户名已存在' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 检查邮箱是否已被注册
      const existingEmail = await env.USERS.get(`email:${email}`);
      if (existingEmail) {
        return new Response(JSON.stringify({ error: '该邮箱已被注册' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 检查发送频率限制
      await checkEmailSendLimit(email, env);

      // 生成验证码
      const verificationCode = generateVerificationCode();

      // 存储验证码
      await storeVerificationCode(email, verificationCode, env);

      // 临时存储用户注册信息（10分钟过期）
      const tempUserData = {
        username,
        passwordHash: await bcrypt(password, env),
        email,
        createdAt: Date.now()
      };
      await env.USERS.put(`pending:user:${username}`, JSON.stringify(tempUserData), {
        expirationTtl: 600 // 10分钟TTL
      });

      // 发送验证码邮件
      const templateData = {
        code: verificationCode,
        username: username
      };

      await sendEmailViaTencentSES(email, templateData, env);

      return new Response(JSON.stringify({
        message: '验证码已发送到您的邮箱，请查收',
        email: email
      }), {
        status: 200,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('Send verification error:', error);
      return new Response(JSON.stringify({
        error: error.message || '发送验证码失败，请稍后重试'
      }), {
        status: 500,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }
  }

  // 新增：验证邮箱并完成注册接口
  if (request.method === 'POST' && url.pathname === '/api/auth/verify-email') {
    try {
      const { username, email, code } = await request.json();

      // 基本参数验证
      if (!username || !email || !code) {
        return new Response(JSON.stringify({ error: '用户名、邮箱和验证码不能为空' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 验证验证码
      await verifyEmailCode(email, code, env);

      // 获取临时存储的用户数据
      const tempUserData = await env.USERS.get(`pending:user:${username}`);
      if (!tempUserData) {
        return new Response(JSON.stringify({ error: '注册信息已过期，请重新注册' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      const userData = JSON.parse(tempUserData);

      // 验证邮箱是否匹配
      if (userData.email !== email) {
        return new Response(JSON.stringify({ error: '邮箱信息不匹配' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 再次检查用户名是否已存在（防止并发注册）
      const existingUser = await env.USERS.get(`user:${username}`);
      if (existingUser) {
        return new Response(JSON.stringify({ error: '用户名已存在' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 创建正式用户数据
      const finalUserData = {
        username: userData.username,
        passwordHash: userData.passwordHash,
        email: userData.email,
        emailVerified: true,
        createdAt: userData.createdAt,
        quota: {
          daily: 100,
          used: 0,
          resetAt: Date.now()
        },
        // 【新增】初始化VIP信息
        vip: {
          expireAt: 0,
          type: null,
          quotaChars: 0,    // 新用户初始配额为0
          usedChars: 0      // 新用户已用配额为0
        },
        // 【新增】初始化用量统计
        usage: {
          totalChars: 0,
          monthlyChars: 0,
          monthlyResetAt: getNextMonthResetTimestamp()
        }
      };

      // 生成认证token
      const accessToken = await generateToken(username, env);
      const refreshToken = await generateToken(username, env, true);

      // 保存用户数据和邮箱映射
      await env.USERS.put(`user:${username}`, JSON.stringify(finalUserData));
      await env.USERS.put(`email:${email}`, username); // 邮箱到用户名的映射

      // 清理临时数据
      await env.USERS.delete(`pending:user:${username}`);

      return new Response(JSON.stringify({
        message: '注册成功',
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in: getAuthConfig(env).ACCESS_TOKEN_EXPIRE
      }), {
        status: 200,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('Verify email error:', error);
      return new Response(JSON.stringify({
        error: error.message || '验证失败，请重试'
      }), {
        status: 400,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }
  }

  // 新增：修改密码接口
  if (request.method === 'POST' && url.pathname === '/api/auth/change-password') {
    try {
      const { currentPassword, newPassword } = await request.json();
      const token = request.headers.get('Authorization')?.replace('Bearer ', '');

      // 验证请求参数
      if (!currentPassword || !newPassword) {
        return new Response(JSON.stringify({ error: '当前密码和新密码不能为空' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 验证新密码强度
      if (newPassword.length < 6) {
        return new Response(JSON.stringify({ error: '新密码长度不能少于6位' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 验证token
      if (!token) {
        return new Response(JSON.stringify({
          error: '未授权访问',
          code: 'NO_TOKEN'
        }), {
          status: 401,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      const username = await verifyToken(token, env);

      // 获取用户数据
      const userData = await env.USERS.get(`user:${username}`);
      if (!userData) {
        return new Response(JSON.stringify({ error: '用户不存在' }), {
          status: 404,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      const user = JSON.parse(userData);

      // 验证当前密码
      const currentPasswordHash = await bcrypt(currentPassword, env);
      if (currentPasswordHash !== user.passwordHash) {
        return new Response(JSON.stringify({ error: '当前密码错误' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 检查新密码是否与当前密码相同
      const newPasswordHash = await bcrypt(newPassword, env);
      if (newPasswordHash === user.passwordHash) {
        return new Response(JSON.stringify({ error: '新密码不能与当前密码相同' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 更新密码
      user.passwordHash = newPasswordHash;
      user.passwordUpdatedAt = Date.now();

      // 保存更新后的用户数据
      await env.USERS.put(`user:${username}`, JSON.stringify(user));

      return new Response(JSON.stringify({
        message: '密码修改成功'
      }), {
        status: 200,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('Change password error:', error);

      // 【关键修改】处理token验证失败，使用统一的认证错误处理函数
      if (isAuthError(error)) {
        return createAuthErrorResponse(error);
      }

      return new Response(JSON.stringify({
        error: error.message || '修改密码失败，请重试'
      }), {
        status: 500,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }
  }

  // 新增：忘记密码 - 发送重置验证码接口
  if (request.method === 'POST' && url.pathname === '/api/auth/forgot-password') {
    try {
      const { email } = await request.json();

      // 基本参数验证
      if (!email) {
        return new Response(JSON.stringify({ error: '邮箱不能为空' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 邮箱格式验证
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return new Response(JSON.stringify({ error: '邮箱格式不正确' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 检查邮箱是否已注册
      const emailMapping = await env.USERS.get(`email:${email}`);
      if (!emailMapping) {
        return new Response(JSON.stringify({ error: '该邮箱未注册' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 检查发送频率限制
      await checkEmailSendLimit(email, env);

      // 生成验证码
      const verificationCode = generateVerificationCode();

      // 存储重置密码验证码（使用不同的key前缀）
      await env.USERS.put(`reset:email:${email}`, JSON.stringify({
        code: verificationCode,
        expireTime: Date.now() + 10 * 60 * 1000, // 10分钟过期
        attempts: 0,
        maxAttempts: 5,
        username: emailMapping
      }), {
        expirationTtl: 600 // 10分钟TTL
      });

      // 发送验证码邮件
      const templateData = {
        code: verificationCode,
        username: emailMapping
      };

      await sendEmailViaTencentSES(email, templateData, env);

      return new Response(JSON.stringify({
        message: '重置密码验证码已发送到您的邮箱，请查收',
        email: email
      }), {
        status: 200,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('Forgot password error:', error);
      return new Response(JSON.stringify({
        error: error.message || '发送验证码失败，请稍后重试'
      }), {
        status: 500,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }
  }

  // 新增：重置密码接口
  if (request.method === 'POST' && url.pathname === '/api/auth/reset-password') {
    try {
      const { email, code, newPassword } = await request.json();

      // 基本参数验证
      if (!email || !code || !newPassword) {
        return new Response(JSON.stringify({ error: '邮箱、验证码和新密码不能为空' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 验证新密码强度
      if (newPassword.length < 6) {
        return new Response(JSON.stringify({ error: '新密码长度不能少于6位' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 获取重置密码验证码数据
      const resetData = await env.USERS.get(`reset:email:${email}`);
      if (!resetData) {
        return new Response(JSON.stringify({ error: '验证码不存在或已过期' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      const data = JSON.parse(resetData);

      // 检查是否过期
      if (Date.now() > data.expireTime) {
        await env.USERS.delete(`reset:email:${email}`);
        return new Response(JSON.stringify({ error: '验证码已过期' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 检查尝试次数
      if (data.attempts >= data.maxAttempts) {
        await env.USERS.delete(`reset:email:${email}`);
        return new Response(JSON.stringify({ error: '验证码尝试次数过多，请重新获取' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 验证码错误，增加尝试次数
      if (data.code !== code) {
        data.attempts += 1;
        await env.USERS.put(`reset:email:${email}`, JSON.stringify(data), {
          expirationTtl: Math.max(0, Math.floor((data.expireTime - Date.now()) / 1000))
        });
        return new Response(JSON.stringify({ error: '验证码错误' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 获取用户数据
      const username = data.username;
      const userData = await env.USERS.get(`user:${username}`);
      if (!userData) {
        return new Response(JSON.stringify({ error: '用户不存在' }), {
          status: 404,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      const user = JSON.parse(userData);

      // 检查新密码是否与当前密码相同
      const newPasswordHash = await bcrypt(newPassword, env);
      if (newPasswordHash === user.passwordHash) {
        return new Response(JSON.stringify({ error: '新密码不能与当前密码相同' }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 更新密码
      user.passwordHash = newPasswordHash;
      user.passwordUpdatedAt = Date.now();

      // 保存更新后的用户数据
      await env.USERS.put(`user:${username}`, JSON.stringify(user));

      // 删除重置验证码
      await env.USERS.delete(`reset:email:${email}`);

      return new Response(JSON.stringify({
        message: '密码重置成功，请使用新密码登录'
      }), {
        status: 200,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('Reset password error:', error);
      return new Response(JSON.stringify({
        error: error.message || '重置密码失败，请重试'
      }), {
        status: 500,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }
  }

  return new Response('Not Found', { status: 404, headers: corsHeaders() });
}

// ========== TTS 处理函数 ==========
// 从 tts.js
/**
 * 【SSML增强版】智能分割文本，支持SSML指令识别
 * 这个函数会将文本分割成不超过maxLength的块，同时确保 [...] 形式的SSML指令不会被破坏。
 * 保持现有的所有智能分割和性能优化逻辑。
 * @param {string} text - 输入的文本，可能包含SSML指令
 * @returns {Promise<string[]>} - 分割后的文本块数组
 */
async function splitText(text) {
  const maxLength = 490;  // 每个片段的最大长度，保持与原版一致

  // 【SSML功能】检测文本中是否包含SSML指令
  const hasSSMLDirectives = /\[.*?\]/.test(text);

  if (hasSSMLDirectives) {
    // 【SSML处理路径】使用SSML感知的分割逻辑
    console.log('[SSML-SPLIT] Detected SSML directives, using SSML-aware splitting');
    return await splitTextWithSSML(text, maxLength);
  } else {
    // 【传统处理路径】使用原有的智能分割逻辑，保持100%兼容性
    return await splitTextTraditional(text, maxLength);
  }
}

/**
 * 【新增】SSML感知的文本分割函数
 * 确保SSML指令（如[calmly], [whispering]）不会被分割破坏
 */
async function splitTextWithSSML(text, maxLength) {
  // 使用正则表达式将文本分割成普通文本片段和SSML指令片段的数组
  // 例如 "[calmly] Hello world. [whispering] Secret." 会被分割成:
  // ["", "[calmly]", " Hello world. ", "[whispering]", " Secret."]
  const parts = text.split(/(\[.*?\])/g).filter(Boolean); // filter(Boolean) 用于移除空字符串

  const chunks = [];
  let currentChunk = "";

  for (const part of parts) {
    // 如果当前块加上新片段会超过最大长度
    if (currentChunk.length + part.length > maxLength) {
      // 如果当前块有内容，则推入chunks数组
      if (currentChunk.trim()) {
        chunks.push(currentChunk.trim());
      }

      // 如果单个片段本身就超长，需要进一步处理
      if (part.length > maxLength) {
        // 检查是否是SSML指令
        if (/^\[.*?\]$/.test(part.trim())) {
          // 如果是SSML指令但超长，保持完整（这种情况极少见）
          console.warn('[SSML-SPLIT] Warning: SSML directive exceeds maxLength:', part.substring(0, 50) + '...');
          chunks.push(part.trim());
          currentChunk = "";
        } else {
          // 如果是普通文本超长，使用智能分割
          const subChunks = smartSplitLongText(part, maxLength);
          chunks.push(...subChunks);
          currentChunk = "";
        }
      } else {
        // 开始一个新的块
        currentChunk = part;
      }
    } else {
      // 否则，将新片段添加到当前块
      currentChunk += part;
    }
  }

  // 推入最后一个块
  if (currentChunk.trim()) {
    chunks.push(currentChunk.trim());
  }

  // 如果没有任何内容，返回原文本作为单个块
  if (chunks.length === 0 && text.length > 0) {
    chunks.push(text);
  }

  console.log(`[SSML-SPLIT] Split text with SSML into ${chunks.length} chunks`);
  return chunks;
}

/**
 * 【保持原有】传统的智能分割函数，保持100%向后兼容
 * 这是原有的splitText函数逻辑，确保非SSML文本的处理完全不变
 */
async function splitTextTraditional(text, maxLength) {
  let sentences;
  try {
    const segmenter = new Intl.Segmenter(['en', 'zh'], { granularity: 'sentence' });
    const iterator = segmenter.segment(text);
    sentences = Array.from(iterator).map(s => s.segment);
  } catch (e) {
    // 如果 Intl.Segmenter 在极端情况下失败或环境不支持，回退到正则表达式方案
    console.error("Intl.Segmenter failed, falling back to regex:", e);
    const sentencePattern = /(?<=[。！？!?；;:：…]{1,2})\s*/;
    sentences = text.split(sentencePattern);
  }

  const chunks = [];
  let currentChunk = "";

  for (const sentence of sentences) {
    // 如果单个句子就超过了最大长度，需要进一步分割
    if (sentence.length > maxLength) {
      if (currentChunk) {
        chunks.push(currentChunk.trim());
        currentChunk = "";
      }
      // 使用智能分割替代原来的硬切分
      const subChunks = smartSplitLongSentence(sentence, maxLength);
      chunks.push(...subChunks);
    }
    // 如果当前块加上新句子会超过最大长度
    else if (currentChunk.length + sentence.length > maxLength) {
      chunks.push(currentChunk.trim());
      currentChunk = sentence;
    } else {
      currentChunk += sentence;
    }
  }

  if (currentChunk) {
    chunks.push(currentChunk.trim());
  }

  return chunks;
}

/**
 * 【新增】智能分割超长文本的通用函数
 * 用于处理SSML和传统文本的超长片段分割
 * 保持原有的所有智能分割逻辑和性能优化
 */
function smartSplitLongText(text, maxLength) {
  const subChunks = [];
  let remainingText = text;

  while (remainingText.length > maxLength) {
    let splitPos = -1;
    const searchRange = remainingText.substring(0, maxLength);

    // 优化：使用单次遍历查找最佳分割点，按优先级从高到低
    // 1. 次要标点符号（逗号、分号、冒号等） - 使用lastIndexOf一次查找
    const punctuationChars = '，,;；:：';
    for (let i = 0; i < punctuationChars.length; i++) {
      const pos = searchRange.lastIndexOf(punctuationChars[i]);
      if (pos > splitPos) {
        splitPos = pos + 1; // 在标点后分割
      }
    }

    // 2. 如果没找到次要标点，寻找空格（主要用于英文）
    if (splitPos === -1) {
      splitPos = searchRange.lastIndexOf(' ');
    }

    // 3. 寻找连接符 - 优化：直接使用lastIndexOf
    if (splitPos === -1) {
      const connectorChars = '-–—';
      for (let i = 0; i < connectorChars.length; i++) {
        const pos = searchRange.lastIndexOf(connectorChars[i]);
        if (pos > splitPos) {
          splitPos = pos + 1;
        }
      }
    }

    // 4. 寻找换行符或制表符
    if (splitPos === -1) {
      splitPos = Math.max(
        searchRange.lastIndexOf('\n'),
        searchRange.lastIndexOf('\r'),
        searchRange.lastIndexOf('\t')
      );
    }

    // 5. 如果都找不到合适的分割点，使用硬切分，但尽量避免切断单词
    if (splitPos === -1 || splitPos < maxLength * 0.7) {
      splitPos = maxLength;

      // 优化：简化英文单词边界检测
      if (splitPos < remainingText.length) {
        const charAtSplit = remainingText[splitPos];
        const charBeforeSplit = remainingText[splitPos - 1];

        // 如果切分点在英文字母中间，向前寻找单词边界
        if (/[a-zA-Z]/.test(charAtSplit) && /[a-zA-Z]/.test(charBeforeSplit)) {
          // 优化：使用lastIndexOf查找空格而不是while循环
          const wordBoundary = remainingText.lastIndexOf(' ', splitPos);
          if (wordBoundary > maxLength * 0.8) {
            splitPos = wordBoundary;
          }
        }
      }
    }

    // 确保分割位置有效
    splitPos = Math.max(1, Math.min(splitPos, remainingText.length));

    const chunk = remainingText.substring(0, splitPos).trim();
    if (chunk) {
      subChunks.push(chunk);
    }

    remainingText = remainingText.substring(splitPos).trim();
  }

  // 添加剩余部分
  if (remainingText.trim()) {
    subChunks.push(remainingText.trim());
  }

  return subChunks;
}

/**
 * 【保持原有】智能分割超长句子函数 - 用于传统文本处理
 * 这是原有的smartSplitLongSentence函数，保持100%兼容性
 */
function smartSplitLongSentence(sentence, maxLength) {
  // 直接复用通用的智能分割函数
  return smartSplitLongText(sentence, maxLength);
}

async function generateSpeech(text, voiceId, modelId, stability, similarity_boost, style, speed, env) {
  const url = `https://api.elevenlabs.io/v1/text-to-speech/${voiceId}?allow_unauthenticated=1`;
  const headers = { 'Content-Type': 'application/json' };

  // 根据不同模型构建相应的 voice_settings
  let voice_settings = {};

  if (modelId === 'eleven_v3') {
    // Eleven v3 模型只支持 stability 参数
    voice_settings = {
      stability: stability || 0.5,  // eleven_v3 默认使用 0.5
      use_speaker_boost: true       // 启用Speaker Boost增强音质
    };
  } else if (modelId === 'eleven_turbo_v2' || modelId === 'eleven_turbo_v2_5') {
    // Eleven Turbo v2/v2.5 模型不支持 style 参数
    voice_settings = {
      stability: stability || 0.58,
      similarity_boost: similarity_boost || 0.75,
      speed: speed || 1.00,
      use_speaker_boost: true       // 启用Speaker Boost增强音质
    };
  } else {
    // 其他模型支持完整参数
    voice_settings = {
      stability: stability || 0.58,
      similarity_boost: similarity_boost || 0.75,
      style: style || 0.50,
      speed: speed || 1.00,
      use_speaker_boost: true       // 启用Speaker Boost增强音质
    };
  }

  const payload = {
    text: text,
    model_id: modelId,
    voice_settings: voice_settings
  };

  // 【新增】记录ElevenLabs API请求详情
  if (env && env.DEBUG) {
    console.log(`[ELEVENLABS-API] 🚀 TTS Request Details:`, {
      url: url,
      method: 'POST',
      headers: headers,
      voiceId: voiceId,
      modelId: modelId,
      textLength: text.length,
      textPreview: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
      voice_settings: voice_settings,
      payload: payload,
      payloadSize: JSON.stringify(payload).length + ' bytes',
      timestamp: new Date().toISOString()
    });
  }

  let retries = 3;
  while (retries > 0) {
    try {
      // 【新增】记录每次请求尝试
      if (env && env.DEBUG) {
        console.log(`[ELEVENLABS-API] 📤 Sending request (attempt ${4 - retries}/3) to ElevenLabs:`, {
          attemptNumber: 4 - retries,
          totalAttempts: 3,
          remainingRetries: retries - 1,
          url: url,
          timestamp: new Date().toISOString()
        });
      }

      const response = await fetch(url, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(payload)
      });

      if (response.ok) {
        const audioBuffer = await response.arrayBuffer();

        // 【新增】记录成功响应详情
        if (env && env.DEBUG) {
          console.log(`[ELEVENLABS-API] ✅ Request successful:`, {
            status: response.status,
            statusText: response.statusText,
            audioSize: audioBuffer.byteLength + ' bytes',
            audioSizeKB: (audioBuffer.byteLength / 1024).toFixed(2) + ' KB',
            contentType: response.headers.get('content-type'),
            textToAudioRatio: (audioBuffer.byteLength / text.length).toFixed(2) + ' bytes/char',
            timestamp: new Date().toISOString()
          });
        }

        return audioBuffer;
      } else {
        const errorData = await response.json();

        // 【修复】从 detail 对象中正确提取错误消息
        const errorMessage = errorData?.detail?.message || errorData?.message || JSON.stringify(errorData) || 'Failed to generate speech';

        // 【新增】记录错误响应详情
        if (env && env.DEBUG) {
          console.error(`[ELEVENLABS-API] ❌ Request failed:`, {
            status: response.status,
            statusText: response.statusText,
            errorMessage: errorMessage,
            errorDetails: errorData,
            detailStatus: errorData?.detail?.status,
            attemptNumber: 4 - retries,
            willRetry: retries > 1,
            timestamp: new Date().toISOString()
          });
        }

        // 【修复】创建增强的错误对象，使用正确提取的消息
        const enhancedError = new Error(errorMessage);
        enhancedError.status = response.status;
        enhancedError.isDataCenterRetryable = isDataCenterRetryableError(enhancedError, response.status, errorData);
        enhancedError.originalError = errorData;

        throw enhancedError;
      }
    } catch (error) {
      retries--;

      // 【新增】记录重试信息
      if (env && env.DEBUG) {
        console.warn(`[ELEVENLABS-API] ⚠️ Request error, ${retries > 0 ? 'retrying' : 'giving up'}:`, {
          error: error.message,
          errorType: error.constructor.name,
          attemptNumber: 4 - retries - 1,
          remainingRetries: retries,
          willRetry: retries > 0,
          retryDelay: retries > 0 ? '1000ms' : 'none',
          timestamp: new Date().toISOString()
        });
      }

      if (retries === 0) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒后重试
    }
  }
}

// ========== 并发计算函数 ==========

// 备份：原始的复杂并发计算函数（已废弃，保留用于回滚）
function calculateOptimalConcurrency_BACKUP_COMPLEX(chunkCount) {
  // ❶ 以 "50 个子请求" 为硬上限，扣除 5 作缓冲
  const maxSubRequests = 45;
  const retryBuffer = Math.ceil(chunkCount * 0.1);     // 10% 留给重试
  const hardCap = Math.floor((maxSubRequests - retryBuffer) / chunkCount);

  // ❂ 经验阶梯（确保中小任务速度）
  const softCap =
    chunkCount <= 5  ? 6 :
    chunkCount <= 15 ? 4 :
    chunkCount <= 30 ? 2 : 1;

  // ❸ 取两者较小值，再 clamp 到 1‒6
  const optimalConcurrency = Math.max(1, Math.min(6, Math.min(hardCap, softCap)));

  console.log(`[CONCURRENCY-BACKUP] Calculated optimal concurrency for ${chunkCount} chunks:`, {
    chunkCount: chunkCount,
    retryBuffer: retryBuffer,
    hardCap: hardCap,
    softCap: softCap,
    finalConcurrency: optimalConcurrency,
    estimatedSubRequests: chunkCount + retryBuffer,
    safetyMargin: maxSubRequests - (chunkCount + retryBuffer)
  });

  return optimalConcurrency;
}

// 优化后的并发计算函数 - 简化最优方案
function calculateOptimalConcurrency(chunkCount, env) {
  const CF_CONN_LIMIT = 6; // Cloudflare Workers 每请求并发连接限制

  // 兜底安全检查（在当前约束下几乎不会触发，但保留是好习惯）
  const estimatedSubRequests = chunkCount + Math.ceil(chunkCount * 0.1); // 估算 chunk + 10% 重试
  if (estimatedSubRequests >= 45) { // 留一些余量给KV操作
      console.warn(`[CONCURRENCY] High chunk count (${chunkCount}) is approaching subrequest limit. Consider splitting the task.`);
  }

  // 核心逻辑：直接使用最大并发，但不超过任务本身的数量
  const concurrency = Math.min(CF_CONN_LIMIT, chunkCount);

  if (env.DEBUG) {
    console.log(`[CONCURRENCY] Calculated optimal concurrency for ${chunkCount} chunks: ${concurrency} (Strategy: Simple Max)`, {
      chunkCount: chunkCount,
      maxConcurrency: CF_CONN_LIMIT,
      finalConcurrency: concurrency,
      estimatedSubRequests: estimatedSubRequests,
      estimatedRounds: Math.ceil(chunkCount / concurrency),
      performanceGain: chunkCount > 6 ? `~${Math.round((1 - Math.ceil(chunkCount / concurrency) / Math.ceil(chunkCount / Math.min(chunkCount <= 5 ? 6 : chunkCount <= 15 ? 4 : chunkCount <= 30 ? 2 : 1, chunkCount))) * 100)}% faster` : 'baseline'
    });
  } else {
    // 在非DEBUG模式下，只打印最关键的信息
    console.log(`[CONCURRENCY] Using ${concurrency} concurrent requests for ${chunkCount} chunks`);
  }

  return concurrency;
}

// 轻量级并发控制器 - 无外部依赖
function createConcurrencyLimiter(maxConcurrent) {
  let running = 0;
  const queue = [];

  return function limit(fn) {
    return new Promise((resolve, reject) => {
      const execute = async () => {
        running++;
        try {
          const result = await fn();
          resolve(result);
        } catch (error) {
          reject(error);
        } finally {
          running--;
          if (queue.length > 0) {
            const next = queue.shift();
            next();
          }
        }
      };

      if (running < maxConcurrent) {
        execute();
      } else {
        queue.push(execute);
      }
    });
  };
}

// 优化后的并发处理函数 - 支持部分失败容错和动态并发
async function processChunks(chunks, voiceId, modelId, stability, similarity_boost, style, speed, env) {
  console.log(`[CONCURRENCY] Processing ${chunks.length} chunks with dynamic concurrency...`);

  // 动态计算最优并发数，智能守卫子请求限制
  const optimalConcurrency = calculateOptimalConcurrency(chunks.length, env);
  const limiter = createConcurrencyLimiter(optimalConcurrency);

  if (env.DEBUG) {
    console.log(`[CONCURRENCY] Using ${optimalConcurrency} concurrent requests for ${chunks.length} chunks`);
  }

  // 创建所有任务，每个任务包含索引以保证顺序
  const tasks = chunks.map((chunk, index) =>
    limiter(async () => {
      try {
        if (env.DEBUG) {
          console.log(`Processing chunk ${index + 1}/${chunks.length}, length: ${chunk.length}`);
        }
        const audioData = await generateSpeech(chunk, voiceId, modelId, stability, similarity_boost, style, speed, env);
        if (!audioData) {
          throw new Error(`Failed to generate audio for chunk ${index + 1}`);
        }
        return { index, audioData, success: true };
      } catch (error) {
        console.error(`Error processing chunk ${index + 1}:`, error);
        return { index, error: error.message, success: false };
      }
    })
  );

  // 使用 Promise.allSettled 确保所有任务都完成（成功或失败）
  const results = await Promise.allSettled(tasks);

  // 提取实际结果，处理 Promise.allSettled 的包装
  const processedResults = results.map(result => {
    if (result.status === 'fulfilled') {
      return result.value;
    } else {
      // 这种情况很少见，通常是limiter内部错误
      console.error('Task execution failed:', result.reason);
      return { index: -1, error: result.reason.message, success: false };
    }
  });

  // 按索引排序确保音频顺序正确
  processedResults.sort((a, b) => a.index - b.index);

  // 分离成功和失败的结果
  const successfulResults = processedResults.filter(result => result.success);
  const failedResults = processedResults.filter(result => !result.success);

  console.log(`Processing completed: ${successfulResults.length} successful, ${failedResults.length} failed`);

  // 如果有失败的chunk，记录详细信息
  if (failedResults.length > 0) {
    console.warn('Failed chunks:', failedResults.map(r => `Chunk ${r.index + 1}: ${r.error}`));
  }

  // 【修复】检查是否有任何一个失败是可重试的数据中心错误
  if (failedResults.length > 0) {
    const firstRetryableError = failedResults.find(r => r.error?.isDataCenterRetryable)?.error;

    if (firstRetryableError) {
      // 如果找到了可重试的错误，就优先把它抛出去，并附加上下文信息
      console.log(`[PROCESSSCHUNKS] Found retryable datacenter error in chunk processing, propagating for retry`);
      const overallError = new Error(`Data center retryable error detected: ${firstRetryableError.message}`);
      overallError.isDataCenterRetryable = true; // 明确传递标志
      overallError.originalStatus = firstRetryableError.status;
      overallError.originalError = firstRetryableError.originalError;
      throw overallError;
    }
  }

  // 根据失败比例决定处理策略
  const failureRate = failedResults.length / chunks.length;

  if (failureRate >= 0.5) {
    // 如果失败率很高，但没有一个是可重试的错误，则抛出通用错误
    throw new Error(`Too many chunks failed (${failedResults.length}/${chunks.length}). This may indicate a non-retryable system issue. Failed chunks: ${failedResults.map(r => r.index + 1).join(', ')}`);
  } else if (failedResults.length > 0) {
    // 如果有部分失败，但失败率 < 50%，尝试重试失败的chunk
    console.log(`Retrying ${failedResults.length} failed chunks...`);
    const retryResults = await retryFailedChunks(failedResults, chunks, voiceId, modelId, stability, similarity_boost, style, speed, limiter, env);

    // 合并重试结果
    const finalFailedResults = [];
    retryResults.forEach(retryResult => {
      if (retryResult.success) {
        successfulResults.push(retryResult);
        if (env.DEBUG) {
          console.log(`Retry successful for chunk ${retryResult.index + 1}`);
        }
      } else {
        console.warn(`Retry failed for chunk ${retryResult.index + 1}: ${retryResult.error}`);
        // 收集最终失败的结果
        finalFailedResults.push(retryResult);
      }
    });

    // 重新排序
    successfulResults.sort((a, b) => a.index - b.index);

    // 【修复】检查重试后是否还有可重试的错误
    if (finalFailedResults.length > 0) {
      const retryableErrorAfterRetry = finalFailedResults.find(r => r.error?.isDataCenterRetryable)?.error;

      if (retryableErrorAfterRetry) {
        console.log(`[PROCESSSCHUNKS] Found retryable datacenter error even after retry, propagating for datacenter switch`);
        const overallError = new Error(`Data center retryable error persists after retry: ${retryableErrorAfterRetry.message}`);
        overallError.isDataCenterRetryable = true;
        overallError.originalStatus = retryableErrorAfterRetry.status;
        overallError.originalError = retryableErrorAfterRetry.originalError;
        throw overallError;
      }
    }
  }

  // 检查是否还有失败的chunk
  const finalFailedCount = chunks.length - successfulResults.length;
  if (finalFailedCount > 0) {
    const missingChunks = [];
    for (let i = 0; i < chunks.length; i++) {
      if (!successfulResults.find(r => r.index === i)) {
        missingChunks.push(i + 1);
      }
    }
    console.warn(`Final result: ${finalFailedCount} chunks still failed after retry: ${missingChunks.join(', ')}`);

    // 如果最终失败的chunk太多，抛出错误
    if (finalFailedCount / chunks.length >= 0.3) {
      throw new Error(`Too many chunks failed even after retry (${finalFailedCount}/${chunks.length}). Failed chunks: ${missingChunks.join(', ')}`);
    }
  }

  // 提取音频数据
  const audioDataList = successfulResults.map(result => result.audioData);

  console.log(`Successfully processed ${audioDataList.length}/${chunks.length} chunks concurrently`);
  return audioDataList;
}

// 重试失败chunk的辅助函数
async function retryFailedChunks(failedResults, originalChunks, voiceId, modelId, stability, similarity_boost, style, speed, limiter, env) {
  const retryTasks = failedResults.map(failedResult =>
    limiter(async () => {
      try {
        if (env.DEBUG) {
          console.log(`Retrying chunk ${failedResult.index + 1}...`);
        }
        const chunk = originalChunks[failedResult.index];
        const audioData = await generateSpeech(chunk, voiceId, modelId, stability, similarity_boost, style, speed, env);
        if (!audioData) {
          throw new Error(`Retry failed to generate audio for chunk ${failedResult.index + 1}`);
        }
        return { index: failedResult.index, audioData, success: true };
      } catch (error) {
        console.error(`Retry failed for chunk ${failedResult.index + 1}:`, error);
        // 【修复】保留错误对象的完整信息，包括isDataCenterRetryable标志
        return { index: failedResult.index, error: error, success: false };
      }
    })
  );

  const retryResults = await Promise.allSettled(retryTasks);
  return retryResults.map(result => result.status === 'fulfilled' ? result.value : { success: false, error: result.reason.message });
}

// ========== R2异步任务处理相关函数 ==========

// 生成UUID
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * 计算用户配额详细信息
 * @param {object} userData - 用户数据
 * @returns {object} 配额详细信息
 */
function calculateQuotaDetails(userData) {
  const vip = userData.vip || { expireAt: 0 };

  // 判断是否为老用户（没有quotaChars字段）
  const isLegacyUser = vip.quotaChars === undefined;

  if (isLegacyUser) {
    // 老用户：无限制
    return {
      isLegacyUser: true,
      quotaChars: undefined,
      usedChars: undefined,
      remainingChars: undefined,
      usagePercentage: 0
    };
  }

  // 新用户：计算具体配额信息
  const totalQuota = vip.quotaChars || 0;
  const usedQuota = vip.usedChars || 0;
  const remainingQuota = Math.max(0, totalQuota - usedQuota);
  const usagePercentage = totalQuota > 0 ? Math.min(100, (usedQuota / totalQuota) * 100) : 0;

  return {
    isLegacyUser: false,
    quotaChars: totalQuota,
    usedChars: usedQuota,
    remainingChars: remainingQuota,
    usagePercentage: Math.round(usagePercentage * 100) / 100 // 保留2位小数
  };
}







// 存储音频文件到R2
async function storeAudioFile(taskId, audioBuffer, env) {
  const key = `audios/${taskId}.mp3`;
  const audioSize = audioBuffer.byteLength;

  if (env.DEBUG) {
    console.log(`[R2-AUDIO] Storing audio for task ${taskId}:`, {
      audioSize: audioSize,
      audioType: audioBuffer.constructor.name,
      bucketName: 'AUDIOS',
      key: key,
      sizeInMB: (audioSize / 1024 / 1024).toFixed(2)
    });
  }

  try {
    const startTime = Date.now();

    const result = await env.AUDIOS.put(key, audioBuffer, {
      httpMetadata: {
        contentType: 'audio/mpeg',
        cacheControl: 'public, max-age=86400', // 1天缓存
        contentDisposition: `attachment; filename="${generateDateBasedFilename()}"` // 强制下载
      }
    });

    const uploadTime = Date.now() - startTime;

    if (env.DEBUG) {
      console.log(`[R2-AUDIO] ✅ Successfully stored audio for task ${taskId}:`, {
        audioSize: audioSize,
        uploadTimeMs: uploadTime,
        sizeInMB: (audioSize / 1024 / 1024).toFixed(2),
        timestamp: new Date().toISOString()
      });
    } else {
      console.log(`[R2-AUDIO] ✅ Audio stored for task ${taskId}, size: ${(audioSize / 1024 / 1024).toFixed(2)}MB`);
    }

    return result;
  } catch (error) {
    console.error(`[R2-AUDIO] ❌ Failed to store audio for task ${taskId}:`, {
      error: error.message,
      audioSize: audioSize,
      key: key
    });
    throw error;
  }
}



/**
 * 【最终版】升级版VIP权限检查 (兼容老用户无限字符)
 * @param {string} username - 用户名
 * @param {object} env - Cloudflare环境变量
 * @param {('STANDARD'|'PRO')} [requiredTier='STANDARD'] - 要求最低的会员等级
 * @param {number} [requestedChars=0] - 本次任务请求的字符数，0表示不检查配额
 *
 * 核心逻辑：
 * - 老用户（vip.quotaChars === undefined）：享受无限字符权益
 * - 新用户（vip.quotaChars !== undefined）：严格配额限制
 * - 过渡机制：老用户续费时通过useCard函数自动获得quotaChars字段
 */
async function checkVip(username, env, requiredTier = 'STANDARD', requestedChars = 0) {
  const userDataString = await env.USERS.get(`user:${username}`);
  if (!userDataString) {
    throw new Error('用户不存在', { cause: 'quota' });
  }
  const userData = JSON.parse(userDataString);
  const vip = userData.vip;

  // 1. 基础检查：是否有会员资格
  if (!vip) {
    throw new Error('请先开通会员', { cause: 'quota' });
  }

  // 2. 时间检查：会员是否已过期
  if (Date.now() > vip.expireAt) {
    throw new Error('会员已过期，请续费', { cause: 'quota' });
  }

  // --- START: 老新用户区分的字符数配额检查 ---

  // 3. 字符数配额检查 (仅对新规则用户生效)
  // 通过检查 vip.quotaChars 是否存在，来判断是否为新规则用户
  const isNewRuleUser = vip.quotaChars !== undefined;

  if (isNewRuleUser && requestedChars > 0) {
    // 这是一名受配额限制的用户（新用户或已续费的老用户）
    console.log(`[QUOTA-CHECK] User ${username} is under new quota rule. Checking quota...`);

    const currentUsed = vip.usedChars || 0;
    const totalQuota = vip.quotaChars || 0; // totalQuota 必然存在

    if (currentUsed + requestedChars > totalQuota) {
      const remaining = Math.max(0, totalQuota - currentUsed);
      throw new Error(`字符数配额不足。剩余 ${remaining} 字符，本次需要 ${requestedChars} 字符。请升级或续费套餐。`, { cause: 'quota' });
    }
  } else if (requestedChars > 0) {
    // 这是老用户，享受无限字符权益
    console.log(`[QUOTA-CHECK] User ${username} is a legacy user. Skipping quota check.`);
  }

  // --- END: 老新用户区分的字符数配额检查 ---

  // 3. 等级检查：如果要求PRO权限
  if (requiredTier === 'PRO') {
    const userTier = vip.type;
    if (!userTier || !userTier.startsWith('P')) {
      throw new Error('此功能需要PRO会员权限', { cause: 'quota' });
    }
  }

  // 4. 测试套餐的特殊逻辑 (保持不变)
  if (vip.type === 'T') {
    const remainingTime = Math.max(0, vip.expireAt - Date.now()) / 1000;
    if (remainingTime <= 0) {
      throw new Error('测试时间已用完，请充值', { cause: 'quota' });
    }
    console.log(`测试套餐剩余时间: ${remainingTime.toFixed(1)}秒`);
  }
}

// 任务执行统计和监控
function logTaskMetrics(taskId, step, startTime, additionalMetrics = {}, env = null) {
  const currentTime = Date.now();
  const stepDuration = currentTime - startTime;
  const totalDuration = currentTime - (additionalMetrics.taskStartTime || startTime);

  if (env && env.DEBUG) {
    console.log(`[TASK-METRICS] 📊 Task ${taskId} - Step: ${step}`, {
      taskId: taskId,
      step: step,
      stepDurationMs: stepDuration,
      stepDurationSec: (stepDuration / 1000).toFixed(2),
      totalDurationMs: totalDuration,
      totalDurationSec: (totalDuration / 1000).toFixed(2),
      timestamp: new Date().toISOString(),
      ...additionalMetrics
    });
  }

  // 性能警告 - 始终保留
  if (stepDuration > 30000) { // 超过30秒
    console.warn(`[TASK-METRICS] ⚠️ Performance warning - Step '${step}' took ${(stepDuration / 1000).toFixed(2)}s for task ${taskId}`);
  }

  return currentTime;
}

/**
 * 轻量级、高效的系统健康检查。
 * 通过对一个预置的探测文件执行 head() 操作来验证 R2 连接。
 * @param {object} env - Cloudflare Worker 的环境对象
 * @returns {Promise<object>} - 健康检查结果
 */
async function performHealthCheck(env) {
  const healthMetrics = {
    timestamp: new Date().toISOString(),
    checks: {}
  };
  // 确保这个探测文件已预先上传到 R2 存储桶的根目录
  const probeFileName = 'health-probe.txt';

  // --- R2 连接健康检查 ---
  const r2StartTime = Date.now();
  try {
    const objectMetadata = await env.AUDIOS.head(probeFileName);

    if (objectMetadata === null) {
      // 如果能执行到这里但文件不存在，说明连接是通的，但配置有问题。
      // 这也应被视为一种"不健康"状态，因为它会导致后续操作失败。
      throw new Error(`Health probe file '${probeFileName}' not found in the R2 bucket. Please upload it.`);
    }

    // head() 操作成功，说明连接和权限都正常
    healthMetrics.checks.r2 = {
      status: 'healthy',
      responseTimeMs: Date.now() - r2StartTime
    };
    console.log(`[HEALTH-CHECK] ✅ R2 storage is healthy. Response time: ${healthMetrics.checks.r2.responseTimeMs}ms`);

  } catch (r2Error) {
    // 任何错误 (如网络问题、权限问题、文件不存在) 都表示不健康
    healthMetrics.checks.r2 = {
      status: 'unhealthy',
      error: r2Error.message,
      responseTimeMs: Date.now() - r2StartTime
    };
    console.error(`[HEALTH-CHECK] ❌ R2 storage is unhealthy:`, {
        error: r2Error.message,
        responseTime: healthMetrics.checks.r2.responseTimeMs
    });
  }

  // --- 其他健康检查 (如果未来需要添加) ---
  // 例如：检查 KV 存储连接等

  return healthMetrics;
}

// 异步音频生成处理函数
async function processAudioAsync(taskId, input, voiceId, model, stability, similarity_boost, style, speed, username, env) {
  const taskStartTime = Date.now();
  let currentStep = 'initialization';
  let stepStartTime = taskStartTime;

  try {
    console.log(`[ASYNC-TASK] 🚀 Starting async audio processing for task ${taskId}:`, {
      taskId: taskId,
      username: username,
      inputLength: input.length,
      voice: voiceId,
      model: model,
      parameters: { stability, similarity_boost, style, speed },
      timestamp: new Date().toISOString()
    });

    // 执行系统健康检查
    const healthCheck = await performHealthCheck(env);
    if (healthCheck.checks.r2?.status !== 'healthy') {
      throw new Error(`System health check failed: R2 storage is ${healthCheck.checks.r2?.status || 'unavailable'}`);
    }

    // 步骤1: 存储初始状态
    currentStep = 'storing_initial_status';
    stepStartTime = logTaskMetrics(taskId, 'health_check_completed', stepStartTime, { taskStartTime }, env);

    if (env.DEBUG) {
      console.log(`[ASYNC-TASK] 📝 Storing initial processing status for task ${taskId} in KV`);
    }
    try {
      await storeStatusKV(env, taskId, {
        status: 'processing',
        username: username,
        inputPreview: input.substring(0, 100) + (input.length > 100 ? '...' : ''), // 存储前100字符用于调试
        voice: voiceId,
        createdAt: taskStartTime,
        startTime: taskStartTime,
        currentStep: currentStep,
        progress: 'Initializing task...'
      });
      stepStartTime = logTaskMetrics(taskId, 'initial_status_stored', stepStartTime, { taskStartTime }, env);
      if (env.DEBUG) {
        console.log(`[ASYNC-TASK] ✅ Initial status stored successfully for task ${taskId}`);
      }
    } catch (statusError) {
      console.error(`[ASYNC-TASK] ❌ CRITICAL: Failed to store initial status for task ${taskId}:`, {
        error: statusError.message,
        stack: statusError.stack,
        stepDuration: Date.now() - stepStartTime
      });
      throw new Error(`Failed to initialize task: ${statusError.message}`);
    }

    // 步骤2: 文本分割处理
    currentStep = 'text_processing';
    console.log(`[ASYNC-TASK] 📝 Starting text processing for task ${taskId}`);
    let chunks;
    try {
      // 更新状态：文本处理中
      await patchStatusKV(env, taskId, {
        currentStep: currentStep,
        progress: 'Processing text and splitting into chunks...'
      });

      if (env.DEBUG) {
        console.log(`[ASYNC-TASK] 🔤 Starting text splitting for task ${taskId}`, {
          inputLength: input.length,
          inputPreview: input.substring(0, 50) + (input.length > 50 ? '...' : '')
        });
      }

      const textProcessStartTime = Date.now();
      chunks = await splitText(input);
      stepStartTime = logTaskMetrics(taskId, 'text_split_completed', stepStartTime, {
        taskStartTime,
        inputLength: input.length,
        chunksGenerated: chunks?.length || 0,
        textProcessingTime: Date.now() - textProcessStartTime
      }, env);

      if (env.DEBUG) {
        console.log(`[ASYNC-TASK] ✅ Text split into ${chunks.length} chunks for task ${taskId}`, {
          chunks: chunks.map((chunk, index) => ({
            index: index,
            length: chunk.length,
            preview: chunk.substring(0, 30) + (chunk.length > 30 ? '...' : '')
          }))
        });
      } else {
        console.log(`[ASYNC-TASK] ✅ Text split into ${chunks.length} chunks for task ${taskId}`);
      }

      if (!chunks || chunks.length === 0) {
        throw new Error('Text splitting failed - no chunks generated');
      }
    } catch (textError) {
      console.error(`[ASYNC-TASK] ❌ Text processing failed for task ${taskId}:`, {
        error: textError.message,
        stack: textError.stack,
        inputLength: input.length,
        stepDuration: Date.now() - stepStartTime
      });
      throw new Error(`Text processing failed: ${textError.message}`);
    }

    // 步骤3: TTS音频生成
    currentStep = 'audio_generation';
    console.log(`[ASYNC-TASK] 🎵 Starting audio generation for task ${taskId}`);
    let audioDataList;
    try {
      // 更新状态：音频生成中
      await patchStatusKV(env, taskId, {
        currentStep: currentStep,
        progress: `Generating audio for ${chunks.length} text chunks...`,
        totalChunks: chunks.length
      });

      if (env.DEBUG) {
        console.log(`[ASYNC-TASK] 🎵 Starting TTS API calls for task ${taskId}`, {
          totalChunks: chunks.length,
          voiceId: voiceId,
          model: model,
          parameters: { stability, similarity_boost, style, speed }
        });
      }

      const audioGenStartTime = Date.now();
      console.time(`api_calls_${taskId}`);
      audioDataList = await processChunks(
        chunks,
        voiceId,
        model,
        stability,
        similarity_boost,
        style,
        speed,
        env
      );
      console.timeEnd(`api_calls_${taskId}`);

      const audioGenDuration = Date.now() - audioGenStartTime;
      stepStartTime = logTaskMetrics(taskId, 'audio_generation_completed', stepStartTime, {
        taskStartTime,
        totalChunks: chunks.length,
        successfulChunks: audioDataList?.length || 0,
        audioGenerationTime: audioGenDuration,
        avgTimePerChunk: audioDataList?.length ? (audioGenDuration / audioDataList.length).toFixed(2) : 'N/A'
      }, env);

      // 检查音频生成结果
      if (!audioDataList || audioDataList.length === 0) {
        throw new Error('No audio chunks were successfully generated');
      }

      // 计算音频数据统计
      const audioSizes = audioDataList.map(data => data.byteLength);
      const totalAudioSize = audioSizes.reduce((sum, size) => sum + size, 0);
      const avgAudioSize = totalAudioSize / audioDataList.length;

      if (env.DEBUG) {
        console.log(`[ASYNC-TASK] ✅ Audio generation completed for task ${taskId}:`, {
          successfulChunks: audioDataList.length,
          totalChunks: chunks.length,
          successRate: ((audioDataList.length / chunks.length) * 100).toFixed(1) + '%',
          totalAudioSizeMB: (totalAudioSize / 1024 / 1024).toFixed(2),
          avgAudioSizeKB: (avgAudioSize / 1024).toFixed(2),
          generationTimeMs: audioGenDuration,
          avgTimePerChunkMs: (audioGenDuration / audioDataList.length).toFixed(2)
        });
      } else {
        console.log(`[ASYNC-TASK] ✅ Audio generation completed for task ${taskId}: ${audioDataList.length}/${chunks.length} chunks, ${(totalAudioSize / 1024 / 1024).toFixed(2)}MB`);
      }
    } catch (audioError) {
      console.error(`[ASYNC-TASK] ❌ Audio generation failed for task ${taskId}:`, {
        error: audioError.message,
        stack: audioError.stack,
        totalChunks: chunks.length,
        stepDuration: Date.now() - stepStartTime,
        failedAtChunk: audioDataList?.length || 0
      });
      throw new Error(`Audio generation failed: ${audioError.message}`);
    }

    // 步骤4: 音频合并处理
    currentStep = 'audio_merging';
    console.log(`[ASYNC-TASK] 🔗 Starting audio merging for task ${taskId}`);
    let combinedAudioData;
    try {
      // 更新状态：音频合并中
      await patchStatusKV(env, taskId, {
        currentStep: currentStep,
        progress: 'Merging audio chunks...',
        chunksGenerated: audioDataList.length,
        totalChunks: chunks.length
      });

      // 记录处理结果统计
      const originalChunkCount = chunks.length;
      const successfulChunkCount = audioDataList.length;
      if (successfulChunkCount < originalChunkCount) {
        console.warn(`[ASYNC-TASK] ⚠️ Partial success for task ${taskId}: ${successfulChunkCount}/${originalChunkCount} chunks processed successfully`);
      }

      // 计算总长度并分配内存
      const totalLength = audioDataList.reduce((acc, curr) => acc + curr.byteLength, 0);
      combinedAudioData = new Uint8Array(totalLength);

      // 合并音频数据
      let offset = 0;
      for (const audioData of audioDataList) {
        combinedAudioData.set(new Uint8Array(audioData), offset);
        offset += audioData.byteLength;
      }

      console.log(`[ASYNC-TASK] ✅ Audio merging completed for task ${taskId}: ${successfulChunkCount}/${originalChunkCount} chunks, total size: ${totalLength} bytes`);
    } catch (mergeError) {
      console.error(`[ASYNC-TASK] ❌ Audio merging failed for task ${taskId}:`, {
        error: mergeError.message,
        stack: mergeError.stack,
        chunksToMerge: audioDataList?.length || 0
      });
      throw new Error(`Audio merging failed: ${mergeError.message}`);
    }

    // 步骤5: R2存储处理
    currentStep = 'r2_storage';
    console.log(`[ASYNC-TASK] 💾 Starting R2 audio storage for task ${taskId}`);
    const totalLength = combinedAudioData.byteLength;
    const originalChunkCount = chunks.length;
    const successfulChunkCount = audioDataList.length;

    try {
      // 更新状态：存储中
      await patchStatusKV(env, taskId, {
        currentStep: currentStep,
        progress: 'Storing audio file to cloud storage...',
        audioSize: totalLength,
        chunksProcessed: successfulChunkCount,
        totalChunks: originalChunkCount
      });

      console.time(`r2_store_${taskId}`);
      await storeAudioFile(taskId, combinedAudioData.buffer, env);
      console.timeEnd(`r2_store_${taskId}`);
      console.log(`[ASYNC-TASK] ✅ Audio file stored successfully for task ${taskId}`);
    } catch (storageError) {
      console.error(`[ASYNC-TASK] ❌ R2 storage failed for task ${taskId}:`, {
        error: storageError.message,
        stack: storageError.stack,
        audioSize: totalLength
      });
      throw new Error(`Audio storage failed: ${storageError.message}`);
    }

    // 步骤6: 最终状态更新
    currentStep = 'finalizing';
    console.log(`[ASYNC-TASK] 📝 Updating status to complete for task ${taskId}`);
    try {
      // 生成R2直链下载URL
      const r2DirectUrl = R2_DIRECT_DOWNLOAD_CONFIG.generateUrl(taskId);

      await storeStatusKV(env, taskId, {
        status: 'complete',
        username: username,
        completedAt: Date.now(),
        audioSize: totalLength,
        chunksProcessed: successfulChunkCount,
        totalChunks: originalChunkCount,
        totalProcessingTime: Date.now() - taskStartTime,
        currentStep: 'completed',
        progress: 'Task completed successfully!',
        downloadUrl: r2DirectUrl, // 新增：R2直链下载URL
        createdAt: taskStartTime
      });

      const totalTime = Date.now() - taskStartTime;
      logTaskMetrics(taskId, 'task_completed_successfully', stepStartTime, {
        taskStartTime,
        totalProcessingTime: totalTime,
        audioSizeMB: (totalLength / 1024 / 1024).toFixed(2),
        chunksProcessed: successfulChunkCount,
        totalChunks: originalChunkCount,
        successRate: ((successfulChunkCount / originalChunkCount) * 100).toFixed(1) + '%'
      }, env);

      if (env.DEBUG) {
        console.log(`[ASYNC-TASK] 🎉 Task ${taskId} completed successfully:`, {
          totalTimeMs: totalTime,
          totalTimeSec: (totalTime / 1000).toFixed(2),
          audioSizeMB: (totalLength / 1024 / 1024).toFixed(2),
          chunksProcessed: successfulChunkCount,
          totalChunks: originalChunkCount,
          successRate: ((successfulChunkCount / originalChunkCount) * 100).toFixed(1) + '%',
          avgProcessingTimePerChunk: (totalTime / successfulChunkCount).toFixed(2) + 'ms',
          downloadUrl: R2_DIRECT_DOWNLOAD_CONFIG.generateUrl(taskId),
          downloadMethod: 'R2_DIRECT_LINK',
          timestamp: new Date().toISOString()
        });
      } else {
        console.log(`[ASYNC-TASK] 🎉 Task ${taskId} completed successfully in ${(totalTime / 1000).toFixed(2)}s, ${(totalLength / 1024 / 1024).toFixed(2)}MB`);
      }
    } catch (finalStatusError) {
      console.error(`[ASYNC-TASK] ❌ Failed to update final status for task ${taskId}:`, {
        error: finalStatusError.message,
        stack: finalStatusError.stack
      });
      // 即使最终状态更新失败，任务实际上已经完成，所以不抛出错误
      console.warn(`[ASYNC-TASK] ⚠️ Task ${taskId} completed but final status update failed - audio file is available`);
    }
  } catch (error) {
    const totalTime = Date.now() - taskStartTime;
    console.error(`[ASYNC-TASK] ❌ Task ${taskId} failed after ${totalTime}ms at step '${currentStep}':`, {
      error: error.message,
      stack: error.stack,
      totalTimeMs: totalTime,
      username: username,
      currentStep: currentStep,
      timestamp: new Date().toISOString()
    });

    // 更新状态为失败 - 使用try-catch确保状态更新不会再次失败
    console.log(`[ASYNC-TASK] 📝 Updating status to failed for task ${taskId}`);
    try {
      await storeStatusKV(env, taskId, {
        status: 'failed',
        username: username,
        failedAt: Date.now(),
        error: error.message,
        errorStack: error.stack?.substring(0, 1000) || '', // 限制stack长度避免存储问题
        totalProcessingTime: totalTime,
        failedAtStep: currentStep,
        progress: `Failed at step: ${currentStep}`,
        createdAt: taskStartTime
      });
      console.log(`[ASYNC-TASK] ✅ Failed status stored successfully for task ${taskId}`);
    } catch (statusUpdateError) {
      console.error(`[ASYNC-TASK] ❌ CRITICAL: Failed to update failed status for task ${taskId}:`, {
        originalError: error.message,
        statusUpdateError: statusUpdateError.message,
        taskId: taskId,
        username: username
      });
      // 即使状态更新失败，也要记录原始错误
    }
  }
}

// 在生成语音前添加配额检查
async function handleTTS(request, username, env, event = null) {
  await checkVip(username, env);

  if (request.method === 'POST' && request.url.endsWith('/generate')) {
    const { input, voice, stability, similarity_boost, style, speed, model } = await request.json();
    const selectedModel = model || "eleven_turbo_v2"; // 使用传递的模型或默认值

    const voiceId = await getVoiceId(voice, env);
    // 注意：getVoiceId总是返回值（原始输入或映射的ID），所以这里不需要额外验证

    // 生成任务ID
    const taskId = generateUUID();
    console.log(`[TASK-START] 🆔 Created new TTS task:`, {
      taskId: taskId,
      username: username,
      inputLength: input.length,
      voice: voiceId,
      model: model,
      parameters: { stability, similarity_boost, style, speed },
      timestamp: new Date().toISOString()
    });

    // 立即返回任务ID给客户端
    console.log(`[TASK-START] ✅ Returning task ID to client:`, {
      taskId: taskId,
      status: 'processing',
      responseCode: 202
    });

    const response = new Response(JSON.stringify({
      taskId: taskId,
      status: 'processing',
      message: 'Audio generation started. Use the taskId to check status.'
    }), {
      status: 202, // 202 Accepted - 请求已接受，正在处理
      headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
    });

    // 启动异步处理（在响应后立即开始）
    console.log(`[TASK-START] 🚀 Starting async processing for task: ${taskId}`);

    // 创建异步任务Promise
    const asyncTask = processAudioAsync(taskId, input, voiceId, model, stability, similarity_boost, style, speed, username, env)
      .catch(error => {
        console.error(`[TASK-START] ❌ Async task ${taskId} failed:`, {
          error: error.message,
          stack: error.stack,
          taskId: taskId,
          username: username
        });
      });

    // 使用event.waitUntil确保异步任务不会被中断
    if (event && event.waitUntil) {
      console.log(`[TASK-START] 📌 Using event.waitUntil() to ensure task completion for: ${taskId}`);
      event.waitUntil(asyncTask);
    } else {
      console.warn(`[TASK-START] ⚠️ event.waitUntil() not available, task may be interrupted: ${taskId}`);
      // 如果没有event对象，仍然启动任务但可能会被中断
      asyncTask;
    }

    return response;
  }

  // 处理状态检查请求 GET /api/tts/status/{taskId}
  if (request.method === 'GET' && request.url.includes('/status/')) {
    const url = new URL(request.url);
    const taskId = url.pathname.split('/status/')[1];

    if (env.DEBUG) {
      console.log(`[STATUS-CHECK] 📋 Received status check request:`, {
        taskId: taskId,
        username: username,
        url: request.url,
        timestamp: new Date().toISOString()
      });
    }

    if (!taskId) {
      console.error(`[STATUS-CHECK] ❌ Missing task ID in request`);
      return new Response(JSON.stringify({ error: 'Task ID is required' }), {
        status: 400,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }

    try {
      const taskStatus = await getStatusKV(env, taskId);

      if (!taskStatus) {
        console.warn(`[STATUS-CHECK] ⚠️ Task not found: ${taskId}`);
        return new Response(JSON.stringify({ error: 'Task not found' }), {
          status: 404,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 智能超时检测机制
      const now = Date.now();
      const taskAge = now - taskStatus.createdAt;

      // 根据任务阶段设置不同的超时时间
      let timeoutMs;
      let timeoutReason;

      switch (taskStatus.currentStep) {
        case 'initialization':
        case 'storing_initial_status':
          timeoutMs = 30 * 1000; // 30秒 - 初始化应该很快
          timeoutReason = 'Task initialization timeout';
          break;
        case 'text_processing':
          timeoutMs = 60 * 1000; // 1分钟 - 文本处理
          timeoutReason = 'Text processing timeout';
          break;
        case 'audio_generation':
          // 根据文本长度动态计算超时时间
          const estimatedTime = Math.max(2 * 60 * 1000, (taskStatus.totalChunks || 1) * 30 * 1000); // 最少2分钟，每个chunk 30秒
          timeoutMs = Math.min(estimatedTime, 10 * 60 * 1000); // 最多10分钟
          timeoutReason = 'Audio generation timeout';
          break;
        case 'audio_merging':
          timeoutMs = 2 * 60 * 1000; // 2分钟 - 音频合并
          timeoutReason = 'Audio merging timeout';
          break;
        case 'r2_storage':
          timeoutMs = 3 * 60 * 1000; // 3分钟 - R2存储
          timeoutReason = 'Storage timeout';
          break;
        default:
          timeoutMs = 5 * 60 * 1000; // 默认5分钟
          timeoutReason = 'Task timeout';
      }

      if (taskStatus.status === 'processing' && taskAge > timeoutMs) {
        console.warn(`[STATUS-CHECK] ⏰ Task ${taskId} timeout detected:`, {
          taskAge: taskAge,
          timeoutMs: timeoutMs,
          currentStep: taskStatus.currentStep,
          reason: timeoutReason
        });

        // 更新状态为超时失败
        try {
          await storeStatusKV(env, taskId, {
            ...taskStatus,
            status: 'failed',
            error: timeoutReason,
            failedAt: now,
            failedAtStep: taskStatus.currentStep || 'unknown',
            timeoutDetails: {
              taskAge: taskAge,
              timeoutMs: timeoutMs,
              currentStep: taskStatus.currentStep
            }
          });
          console.log(`[STATUS-CHECK] ✅ Timeout status updated for task ${taskId}`);
        } catch (timeoutUpdateError) {
          console.error(`[STATUS-CHECK] ❌ Failed to update timeout status for task ${taskId}:`, timeoutUpdateError);
        }

        return new Response(JSON.stringify({
          taskId: taskId,
          status: 'failed',
          error: timeoutReason,
          failedAtStep: taskStatus.currentStep || 'unknown',
          timeoutDetails: {
            taskAge: Math.round(taskAge / 1000),
            timeoutSeconds: Math.round(timeoutMs / 1000)
          }
        }), {
          status: 200,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 如果任务完成，返回音频文件URL
      if (taskStatus.status === 'complete') {
        // 优先使用R2直链，如果没有则回退到Worker代理下载
        const audioUrl = taskStatus.downloadUrl || `/api/tts/download/${taskId}`;

        if (env.DEBUG) {
          console.log(`[STATUS-CHECK] ✅ Task completed, returning download URL:`, {
            taskId: taskId,
            audioSize: taskStatus.audioSize,
            chunksProcessed: taskStatus.chunksProcessed,
            totalChunks: taskStatus.totalChunks,
            downloadMethod: taskStatus.downloadUrl ? 'R2_DIRECT' : 'WORKER_PROXY',
            audioUrl: audioUrl
          });
        } else {
          console.log(`[STATUS-CHECK] ✅ Task ${taskId} completed, returning download URL`);
        }
        return new Response(JSON.stringify({
          taskId: taskId,
          status: 'complete',
          audioUrl: audioUrl,
          audioSize: taskStatus.audioSize,
          chunksProcessed: taskStatus.chunksProcessed,
          totalChunks: taskStatus.totalChunks,
          completedAt: taskStatus.completedAt
        }), {
          status: 200,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 返回当前状态 - 包含详细进度信息
      if (env.DEBUG) {
        console.log(`[STATUS-CHECK] 📊 Returning current status:`, {
          taskId: taskId,
          status: taskStatus.status,
          currentStep: taskStatus.currentStep,
          hasError: !!taskStatus.error
        });
      }

      // 构建详细的状态响应
      const statusResponse = {
        taskId: taskId,
        status: taskStatus.status,
        createdAt: taskStatus.createdAt,
        ...(taskStatus.currentStep && { currentStep: taskStatus.currentStep }),
        ...(taskStatus.progress && { progress: taskStatus.progress }),
        ...(taskStatus.totalChunks && { totalChunks: taskStatus.totalChunks }),
        ...(taskStatus.chunksGenerated && { chunksGenerated: taskStatus.chunksGenerated }),
        ...(taskStatus.chunksProcessed && { chunksProcessed: taskStatus.chunksProcessed }),
        ...(taskStatus.audioSize && { audioSize: taskStatus.audioSize }),
        ...(taskStatus.error && { error: taskStatus.error }),
        ...(taskStatus.failedAtStep && { failedAtStep: taskStatus.failedAtStep })
      };

      return new Response(JSON.stringify(statusResponse), {
        status: 200,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error(`Error checking task status for ${taskId}:`, error);
      return new Response(JSON.stringify({ error: 'Internal server error' }), {
        status: 500,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }
  }

  // 处理R2音频文件列表请求 GET /api/tts/list-audios（调试用）
  if (request.method === 'GET' && request.url.includes('/list-audios')) {
    if (env.DEBUG) {
      console.log(`[R2-LIST] 🗂️ Listing audio files for debugging`);
    }

    try {
      const objects = await env.AUDIOS.list({ prefix: 'audios/' });

      const audioFiles = objects.objects.map(obj => ({
        key: obj.key,
        size: obj.size,
        uploaded: obj.uploaded,
        etag: obj.etag
      }));

      return new Response(JSON.stringify({
        success: true,
        message: 'Audio files listed successfully',
        files: audioFiles,
        totalFiles: audioFiles.length
      }), {
        status: 200,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    } catch (error) {
      console.error(`[R2-LIST] ❌ Failed to list audio files:`, error);
      return new Response(JSON.stringify({
        success: false,
        message: 'Failed to list audio files',
        error: error.message
      }), {
        status: 500,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }
  }

  // 处理音频下载请求 GET /api/tts/download/{taskId}
  // 注意：此接口保留作为备用下载方式，新任务优先使用R2直链下载
  if (request.method === 'GET' && request.url.includes('/download/')) {
    try {
        // 直接调用新的、高效的下载处理器
        return await handleDownload(request, env, username);
    } catch (error) {
        console.error(`Error during download for request ${request.url}:`, error);
        return new Response(JSON.stringify({ error: 'Internal server error during download' }), {
            status: 500,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
    }
  }

  return new Response('Not Found', { status: 404, headers: corsHeaders() });
}

// 卡密套餐配置
const PACKAGES = {
  // --- 标准套餐 ---
  'M': { days: 30, price: 25, chars: 80000 },     // 月套餐，8万字符
  'Q': { days: 90, price: 55, chars: 250000 },    // 季度套餐，25万字符
  'H': { days: 180, price: 99, chars: 550000 },   // 半年套餐，55万字符

  // --- 新增：PRO套餐 ---
  'PM': { days: 30, price: 45, chars: 250000 },   // 月度PRO，25万字符
  'PQ': { days: 90, price: 120, chars: 800000 },  // 季度PRO，80万字符
  'PH': { days: 180, price: 220, chars: 2000000 }, // 半年PRO，200万字符

  // --- 特殊套餐 ---
  'PT': { days: 0.0208, price: 0, chars: 5000 }   // 30分钟测试套餐，5千字符
};

// 验证卡密
async function verifyCard(code, env) {
  const card = await env.CARDS.get(`card:${code}`);
  if (!card) return null;
  try {
    return JSON.parse(card);
  } catch (error) {
    return null;
  }
}

// 使用卡密
async function useCard(code, username, env) {
  const card = await verifyCard(code, env);

  if (!card) {
    throw new Error('无效的卡密');
  }

  if (card.s === 'used') {
    throw new Error('该卡密已被使用');
  }

  // 如果是测试套餐，检查是否已有其他有效套餐
  if (card.t === 'T') {
    const userData = JSON.parse(await env.USERS.get(`user:${username}`));
    if (userData.vip && Date.now() < userData.vip.expireAt && userData.vip.type !== 'T') {
      throw new Error('已有正式会员，无需使用测试套餐');
    }
  }

  // 先标记卡密为使用中
  card.s = 'using';
  await env.CARDS.put(`card:${code}`, JSON.stringify(card));

  try {
    const userData = JSON.parse(await env.USERS.get(`user:${username}`));

    // 获取新套餐的配置
    const newPackage = PACKAGES[card.t];
    if (!newPackage) {
      throw new Error('未知的套餐类型');
    }

    // 初始化VIP对象（如果不存在）
    if (!userData.vip) {
      userData.vip = {
        expireAt: 0,
        type: null,
        quotaChars: 0, // 新增：总配额
        usedChars: 0   // 新增：已用配额
      };
    } else {
      // 【关键修复】区分真正的老用户和新注册用户
      // 真正的老用户：没有quotaChars字段且已经有过VIP记录（expireAt > 0 或 type 不为null）
      // 新注册用户：没有quotaChars字段但从未购买过套餐（expireAt = 0 且 type = null）
      const isRealLegacyUser = userData.vip.quotaChars === undefined &&
                               (userData.vip.expireAt > 0 || userData.vip.type !== null);

      if (userData.vip.quotaChars === undefined) {
        if (isRealLegacyUser) {
          // 真正的老用户：保持undefined，享受无限字符权益
          console.log(`[CARD-USE] User ${username} is a real legacy user, maintaining unlimited chars`);
          // 不设置quotaChars和usedChars，保持undefined
        } else {
          // 新注册用户首次购买：初始化为0，开始受配额限制
          console.log(`[CARD-USE] User ${username} is a new user, initializing quota system`);
          userData.vip.quotaChars = 0;
          userData.vip.usedChars = 0;
        }
      }
    }

    // 1. 计算新的到期时间
    const baseTime = Math.max(userData.vip.expireAt || 0, Date.now());
    userData.vip.expireAt = baseTime + (newPackage.days * 86400000);

    // 2. 叠加字符数配额（仅对受配额限制的用户）
    if (userData.vip.quotaChars !== undefined) {
      // 受配额限制的用户：叠加配额
      const isExpired = Date.now() > (userData.vip.expireAt || 0);
      const oldRemainingChars = isExpired ? 0 : Math.max(0, userData.vip.quotaChars - userData.vip.usedChars);
      userData.vip.quotaChars = oldRemainingChars + newPackage.chars;
      userData.vip.usedChars = 0; // 每次充值后，已用计数器清零，额度合并到总额度
      console.log(`[CARD-USE] Updated quota for user ${username}: ${userData.vip.quotaChars} chars`);
    } else {
      // 真正的老用户：保持无限字符权益，不设置配额
      console.log(`[CARD-USE] Legacy user ${username} maintains unlimited chars privilege`);
    }

    // 3. 更新套餐类型
    userData.vip.type = card.t;

    // 标记卡密为已使用
    card.s = 'used';
    card.u = username;
    card.a = Date.now();

    // 保存更新
    await env.USERS.put(`user:${username}`, JSON.stringify(userData));
    await env.CARDS.put(`card:${code}`, JSON.stringify(card));

    return userData.vip;
  } catch (error) {
    // 如果出错，恢复卡密状态
    card.s = 'unused';
    await env.CARDS.put(`card:${code}`, JSON.stringify(card));
    throw error;
  }
}

// 添加预览音频处理路由
async function handlePreview(request, env) {
  const url = new URL(request.url);
  const filename = url.pathname.split('/').pop();

  if (!filename) {
    return new Response('File not found', {
      status: 404,
      headers: corsHeaders()
    });
  }

  try {
    // 从 R2 存储桶获取音频文件
    const object = await env.AUDIO_BUCKET.get(`preview-audio-kf/${filename}`);

    if (!object) {
      return new Response('File not found', {
        status: 404,
        headers: corsHeaders()
      });
    }

    // 返回音频文件
    return new Response(object.body, {
      headers: {
        ...corsHeaders(),
        'Content-Type': 'audio/mpeg',
        'Content-Length': object.size,
        'Cache-Control': 'public, max-age=31536000'
      }
    });
  } catch (error) {
    console.error('Error fetching preview audio:', error);
    return new Response('Internal Server Error', {
      status: 500,
      headers: corsHeaders()
    });
  }
}

// ========== 主处理函数 ==========
// 【ES Modules格式】删除了addEventListener，改用export default作为唯一入口

async function handleRequest(request, env, event = null) {
  // 处理 CORS 预检请求
  if (request.method === 'OPTIONS') {
    return handleOptions(request);
  }

  const url = new URL(request.url);

  // 处理预览音频请求
  if (url.pathname.startsWith('/preview-audio-kf/')) {
    const audioName = url.pathname.split('/preview-audio-kf/')[1];
    // 从 R2 获取音频文件
    const object = await env.AUDIO_BUCKET.get(audioName);

    if (object === null) {
      return new Response('Audio not found', {
        status: 404,
        headers: corsHeaders()
      });
    }

    const headers = new Headers();
    headers.set('Content-Type', 'audio/mpeg');
    headers.set('Cache-Control', 'public, max-age=31536000');
    // 添加 CORS 头
    Object.entries(corsHeaders()).forEach(([key, value]) => {
      headers.set(key, value);
    });

    return new Response(object.body, { headers });
  }

  // --- START: WebSocket 路由处理 (高优先级) ---

  // 【修复】单人 TTS WebSocket 连接请求 - 使用精确匹配
  if (url.pathname === '/api/tts/ws/generate') {
    // 【关键改动】为每个任务创建一个唯一的 DO ID
    // 这个 ID 之后会成为任务的 taskId
    const taskId = generateUUID();
    const doId = env.TTS_TASK_DO.idFromName(taskId);

    // 【新增】支持排除特定数据中心的位置提示
    // 从查询参数中获取要排除的位置列表
    const excludeLocations = url.searchParams.get('excludeLocations')?.split(',') || [];
    const locationHint = getRandomLocationHint(env, excludeLocations);

    // 【新增】使用 Analytics Engine 记录DO创建意图
    try {
      if (env.DO_ANALYTICS) {
        env.DO_ANALYTICS.writeDataPoint({
          blobs: [
            taskId,
            "creation_intent",
            "single_tts",
            locationHint || "auto",
            request.cf?.country || "unknown",
            new Date().toISOString()
          ],
          doubles: [1],
          indexes: [taskId, locationHint || "auto"]
        });
        if (env.DEBUG) {
          console.log(`[ANALYTICS] Recorded creation intent for single TTS task ${taskId}, locationHint: ${locationHint || "auto"}`);
        }
      }
    } catch (analyticsError) {
      if (env.DEBUG) {
        console.error('[ANALYTICS] Failed to record creation intent:', analyticsError);
      }
      // 不影响主流程，继续执行
    }

    let stub;
    try {
      stub = locationHint
        ? env.TTS_TASK_DO.get(doId, { locationHint })
        : env.TTS_TASK_DO.get(doId);

      if (env.DEBUG && locationHint) {
        console.log(`[DO-ROUTING] Single TTS task ${taskId} assigned to location: ${locationHint}${excludeLocations.length > 0 ? ` (excluded: [${excludeLocations.join(', ')}])` : ''}`);
      }
    } catch (error) {
      // 【修复】如果locationHint不支持，回退到不使用locationHint
      if (error.message?.includes('unsupported locationHint')) {
        console.warn(`[DO-ROUTING] LocationHint ${locationHint} not supported, falling back to default routing`);
        stub = env.TTS_TASK_DO.get(doId);
      } else {
        throw error;
      }
    }

    // 【简化】直接将原始请求转发给 DO，不需要任何检查
    // DO 的 fetch 方法会处理 Upgrade header 的检查
    return stub.fetch(request);
  }

  // 【修复】多人对话 WebSocket 连接请求 - 移到这里确保优先处理
  if (url.pathname === '/api/tts/ws/dialogue/generate') {
    const dialogueTaskId = generateUUID();
    const doId = env.TTS_TASK_DO.idFromName(dialogueTaskId);

    // 【新增】支持排除特定数据中心的位置提示
    // 从查询参数中获取要排除的位置列表
    const excludeLocations = url.searchParams.get('excludeLocations')?.split(',') || [];
    const locationHint = getRandomLocationHint(env, excludeLocations);

    // 【新增】使用 Analytics Engine 记录DO创建意图
    try {
      if (env.DO_ANALYTICS) {
        env.DO_ANALYTICS.writeDataPoint({
          blobs: [
            dialogueTaskId,
            "creation_intent",
            "dialogue_tts",
            locationHint || "auto",
            request.cf?.country || "unknown",
            new Date().toISOString()
          ],
          doubles: [1],
          indexes: [dialogueTaskId, locationHint || "auto"]
        });
        if (env.DEBUG) {
          console.log(`[ANALYTICS] Recorded creation intent for dialogue TTS task ${dialogueTaskId}, locationHint: ${locationHint || "auto"}`);
        }
      }
    } catch (analyticsError) {
      if (env.DEBUG) {
        console.error('[ANALYTICS] Failed to record creation intent:', analyticsError);
      }
      // 不影响主流程，继续执行
    }

    let stub;
    try {
      stub = locationHint
        ? env.TTS_TASK_DO.get(doId, { locationHint })
        : env.TTS_TASK_DO.get(doId);

      if (env.DEBUG && locationHint) {
        console.log(`[DO-ROUTING] Dialogue TTS task ${dialogueTaskId} assigned to location: ${locationHint}${excludeLocations.length > 0 ? ` (excluded: [${excludeLocations.join(', ')}])` : ''}`);
      }
    } catch (error) {
      // 【修复】如果locationHint不支持，回退到不使用locationHint
      if (error.message?.includes('unsupported locationHint')) {
        console.warn(`[DO-ROUTING] LocationHint ${locationHint} not supported, falling back to default routing`);
        stub = env.TTS_TASK_DO.get(doId);
      } else {
        throw error;
      }
    }

    return stub.fetch(request);
  }

  // --- END: WebSocket 路由处理 ---

  // --- START: HTTP API 路由处理 (低优先级) ---

  // 其他原有的路由处理...
  if (url.pathname.startsWith('/api/auth/')) {
    const response = await handleAuth(request, env);
    const headers = {...corsHeaders(), ...response.headers};
    return new Response(response.body, {
      status: response.status,
      headers: headers
    });
  }

  // 【重要】保留旧的/api/tts/路由，但主要是为了查询历史任务状态
  // 【修复】现在不会错误拦截 WebSocket 请求了
  if (url.pathname.startsWith('/api/tts/')) {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '');
    if (!token) {
        return new Response(JSON.stringify({
          error: 'Unauthorized',
          code: 'NO_TOKEN'
        }), {
          status: 401,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
    }

    try {
        const username = await verifyToken(token, env);
        // 【关键改动】不再处理 /generate 请求，因为新流程走WebSocket
        if (request.method === 'POST' && request.url.endsWith('/generate')) {
            return new Response(JSON.stringify({ error: 'This endpoint is deprecated. Please use WebSocket connection.' }), {
                status: 400,
                headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
            });
        }

        // 保留 handleTTS 以处理 /status 和 /download
        const response = await handleTTS(request, username, env, event);
        const headers = {...corsHeaders(), ...response.headers};
        return new Response(response.body, {
            status: response.status,
            headers: headers
        });
    } catch (error) {
        if (error.cause === 'quota') {
          return new Response(JSON.stringify({ error: error.message, type: 'quota' }), {
            status: 403,
            headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
          });
        }
        // 【关键修改】区分认证错误，使用统一的认证错误处理函数
        if (isAuthError(error)) {
          return createAuthErrorResponse(error);
        }
        // 其他未知错误保持原有处理
        return new Response(JSON.stringify({ error: 'Unauthorized' }), {
          status: 401,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
    }
  }

  if (url.pathname === '/api/card/use') {
    const { code } = await request.json();
    const token = request.headers.get('Authorization')?.replace('Bearer ', '');
    if (!token) {
      return new Response(JSON.stringify({
        error: 'Unauthorized',
        code: 'NO_TOKEN'
      }), {
        status: 401,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }

    try {
      const username = await verifyToken(token, env);
      const quota = await useCard(code, username, env);
      return new Response(JSON.stringify({ quota }), {
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    } catch (error) {
      // 区分认证错误和业务错误
      if (isAuthError(error)) {
        // 认证相关错误，使用统一的认证错误处理
        return createAuthErrorResponse(error);
      } else {
        // 业务逻辑错误（如卡密无效等），保持原有的400状态码
        return new Response(JSON.stringify({ error: error.message }), {
          status: 400,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }
    }
  }

  if (url.pathname === '/api/user/quota') {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '');
    if (!token) {
      return new Response(JSON.stringify({
        error: 'Unauthorized',
        code: 'NO_TOKEN'
      }), {
        status: 401,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }

    try {
      const username = await verifyToken(token, env);
      const userData = JSON.parse(await env.USERS.get(`user:${username}`));
      const vip = userData.vip || { expireAt: 0 };

      // 计算配额详细信息
      const quotaDetails = calculateQuotaDetails(userData);

      const response = {
        // 原有字段（保持向后兼容）
        isVip: Date.now() < vip.expireAt,
        expireAt: vip.expireAt,
        type: vip.type,
        // 如果是测试套餐，添加剩余时间信息
        remainingTime: vip.type === 'T' ?
          Math.max(0, (vip.expireAt - Date.now()) / 1000).toFixed(1) : null,

        // 新增配额相关字段
        quotaChars: quotaDetails.quotaChars,           // 总配额（老用户为undefined）
        usedChars: quotaDetails.usedChars,             // 已用配额（老用户为undefined）
        remainingChars: quotaDetails.remainingChars,   // 剩余配额（老用户为undefined）
        usagePercentage: quotaDetails.usagePercentage, // 使用百分比
        isLegacyUser: quotaDetails.isLegacyUser        // 是否为老用户
      };

      return new Response(JSON.stringify(response), {
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    } catch (error) {
      // 【关键修改】使用统一的认证错误处理函数
      return createAuthErrorResponse(error);
    }
  }

  // 【新增】用户字符数用量查询接口
  if (url.pathname === '/api/user/usage') {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '');
    if (!token) {
      return new Response(JSON.stringify({
        error: 'Unauthorized',
        code: 'NO_TOKEN'
      }), {
        status: 401,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }

    try {
      const username = await verifyToken(token, env);
      const userDataString = await env.USERS.get(`user:${username}`);
      if (!userDataString) {
        return new Response(JSON.stringify({ error: 'User not found' }), {
          status: 404,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      const userData = JSON.parse(userDataString);

      // 确保老用户也能正确返回（向后兼容）
      const usage = userData.usage || {
        totalChars: 0,
        monthlyChars: 0,
        monthlyResetAt: getNextMonthResetTimestamp()
      };

      // 检查月度重置
      if (Date.now() >= usage.monthlyResetAt) {
        usage.monthlyChars = 0;
        usage.monthlyResetAt = getNextMonthResetTimestamp();

        // 如果需要重置，更新用户数据
        userData.usage = usage;
        await env.USERS.put(`user:${username}`, JSON.stringify(userData));
      }

      return new Response(JSON.stringify(usage), {
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    } catch (error) {
      return createAuthErrorResponse(error);
    }
  }

  // 【新增】管理员用量查询接口
  if (url.pathname === '/api/admin/users/usage') {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '');
    if (!token) {
      return new Response(JSON.stringify({
        error: 'Unauthorized',
        code: 'NO_TOKEN'
      }), {
        status: 401,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }

    try {
      // 验证token并获取用户名
      const username = await verifyToken(token, env);

      // 检查管理员权限
      await checkAdminPermission(username, env);

      // 获取查询参数
      const limit = Math.min(parseInt(url.searchParams.get('limit')) || 100, 500); // 最大500
      const cursor = url.searchParams.get('cursor') || null;

      console.log(`[ADMIN-API] Admin ${username} requesting users usage. Limit: ${limit}, Cursor: ${cursor || 'null'}`);

      // 获取所有用户用量数据
      const result = await getAllUsersUsage(env, limit, cursor);

      console.log(`[ADMIN-API] Successfully returned ${result.users.length} users usage data to admin ${username}`);

      return new Response(JSON.stringify(result), {
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    } catch (error) {
      console.error('[ADMIN-API] Error in admin users usage endpoint:', error);

      // 区分权限错误和其他错误
      if (error.message.includes('管理员') || error.message.includes('权限') || error.message.includes('配置')) {
        return new Response(JSON.stringify({
          error: error.message,
          code: 'ADMIN_PERMISSION_DENIED'
        }), {
          status: 403,
          headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
        });
      }

      // 认证错误
      if (isAuthError(error)) {
        return createAuthErrorResponse(error);
      }

      // 其他服务器错误
      return new Response(JSON.stringify({
        error: '服务器内部错误',
        code: 'INTERNAL_ERROR'
      }), {
        status: 500,
        headers: { ...corsHeaders(), 'Content-Type': 'application/json' }
      });
    }
  }

  // --- END: HTTP API 路由处理 ---

  return new Response('Not Found', {
    status: 404,
    headers: corsHeaders()
  });
}

// 【ES Modules格式】这是整个文件的唯一入口点
export default {
  fetch(request, env, ctx) {
    // ctx包含waitUntil方法，用于异步任务处理
    // handleRequest已经是async函数，会返回Promise
    return handleRequest(request, env, ctx);
  }
};

/**
 * 【新增】合并多个音频 ArrayBuffer
 * @param {Array<ArrayBuffer>} audioDataList - 包含多个 ArrayBuffer 的数组
 * @returns {ArrayBuffer} - 合并后的单个 ArrayBuffer
 */
function combineAudio(audioDataList) {
  if (!audioDataList || audioDataList.length === 0) {
    return new ArrayBuffer(0);
  }
  if (audioDataList.length === 1) {
    return audioDataList[0];
  }

  const totalLength = audioDataList.reduce((acc, buffer) => acc + (buffer.byteLength || 0), 0);
  const combined = new Uint8Array(totalLength);

  let offset = 0;
  for (const buffer of audioDataList) {
    if (buffer && buffer.byteLength > 0) {
      combined.set(new Uint8Array(buffer), offset);
      offset += buffer.byteLength;
    }
  }

  return combined.buffer;
}