# ElevenLabs API 请求日志测试指南

## 功能说明

我们已经在 `generateSpeech` 函数中添加了详细的 ElevenLabs API 请求日志记录功能，包括：

### 1. 请求前日志
- 记录完整的请求参数
- 显示文本预览和长度
- 记录语音设置和模型信息

### 2. 请求过程日志
- 记录每次重试尝试
- 显示请求URL和时间戳

### 3. 成功响应日志
- 记录音频大小和格式信息
- 计算文本到音频的转换比率
- 显示响应状态

### 4. 错误响应日志
- 详细的错误信息
- 重试状态和剩余次数
- 错误类型分析

## 启用调试日志

### 方法1: 通过 Cloudflare 控制台
1. 登录 Cloudflare 控制台
2. 进入 Workers & Pages
3. 选择你的 Worker 服务
4. 点击 Settings → Variables
5. 添加环境变量：
   - Variable name: `DEBUG`
   - Value: `true`
6. 保存并等待部署生效（约30秒）

### 方法2: 通过 wrangler.toml 文件
```toml
[vars]
DEBUG = true
```

然后重新部署：
```bash
cd 后端
wrangler deploy
```

## 日志输出示例

### 请求开始日志
```
[ELEVENLABS-API] 🚀 TTS Request Details: {
  url: "https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1",
  method: "POST",
  headers: { "Content-Type": "application/json" },
  voiceId: "pNInz6obpgDQGcFmaJgB",
  modelId: "eleven_turbo_v2",
  textLength: 156,
  textPreview: "Hello, this is a test message for TTS generation. We want to see how the API handles this request...",
  voice_settings: {
    stability: 0.58,
    similarity_boost: 0.75,
    speed: 1.0
  },
  payload: { /* 完整请求体 */ },
  payloadSize: "234 bytes",
  timestamp: "2025-01-XX..."
}
```

### 请求发送日志
```
[ELEVENLABS-API] 📤 Sending request (attempt 1/3) to ElevenLabs: {
  attemptNumber: 1,
  totalAttempts: 3,
  remainingRetries: 2,
  url: "https://api.elevenlabs.io/v1/text-to-speech/...",
  timestamp: "2025-01-XX..."
}
```

### 成功响应日志
```
[ELEVENLABS-API] ✅ Request successful: {
  status: 200,
  statusText: "OK",
  audioSize: "45678 bytes",
  audioSizeKB: "44.61 KB",
  contentType: "audio/mpeg",
  textToAudioRatio: "292.81 bytes/char",
  timestamp: "2025-01-XX..."
}
```

### 错误响应日志
```
[ELEVENLABS-API] ❌ Request failed: {
  status: 429,
  statusText: "Too Many Requests",
  errorMessage: "Rate limit exceeded",
  errorDetails: { /* 完整错误对象 */ },
  attemptNumber: 1,
  willRetry: true,
  timestamp: "2025-01-XX..."
}
```

### 重试日志
```
[ELEVENLABS-API] ⚠️ Request error, retrying: {
  error: "Rate limit exceeded",
  errorType: "Error",
  attemptNumber: 1,
  remainingRetries: 2,
  willRetry: true,
  retryDelay: "1000ms",
  timestamp: "2025-01-XX..."
}
```

## 查看日志的方法

### 1. Cloudflare 实时日志
1. 在 Cloudflare 控制台中进入你的 Worker
2. 点击 "Logs" 标签
3. 启用 "Real-time Logs"
4. 发起 TTS 请求，实时查看日志

### 2. wrangler tail 命令
```bash
cd 后端
wrangler tail
```

### 3. 浏览器开发者工具
如果是本地开发环境，日志会显示在终端中。

## 测试建议

### 1. 正常请求测试
- 使用短文本（<100字符）
- 选择常用语音（如 Adam）
- 使用默认参数

### 2. 长文本测试
- 使用超过500字符的文本
- 观察文本分割和并发请求日志

### 3. 错误场景测试
- 使用无效的语音ID
- 发送过长的文本
- 快速连续发送请求（测试重试机制）

## 注意事项

1. **生产环境**: 记得在生产环境中关闭 DEBUG 模式以减少日志量
2. **敏感信息**: 日志中不包含 API 密钥等敏感信息
3. **性能影响**: DEBUG 模式会增加少量性能开销，主要是日志记录
4. **日志保留**: Cloudflare 的实时日志有时间限制，重要信息建议导出保存

## 故障排查

如果没有看到预期的日志：

1. 确认 DEBUG 环境变量已正确设置
2. 检查 Worker 是否已重新部署
3. 确认请求确实到达了 `generateSpeech` 函数
4. 检查是否有其他错误阻止了函数执行

通过这些详细的日志，你可以清楚地了解每个 ElevenLabs API 请求的完整过程，包括请求参数、响应状态、错误信息和重试机制的工作情况。
