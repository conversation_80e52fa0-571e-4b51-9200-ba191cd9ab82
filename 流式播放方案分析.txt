# 核心问题诊断

您遇到的问题不是典型的CORS（跨域资源共享）错误，而是浏览器对于跨域链接下载行为的安全限制，其根本原因在于 R2 直接提供的文件缺少一个关键的HTTP头：Content-Disposition。

让我为您详细解释：

浏览器行为分析：

当您在前端页面 (tts.aispeak.top) 点击下载时，您的代码创建了一个 <a> 标签，其 href 指向 R2 直链 (https://r2-assets.aispeak.top/...)，并且设置了 download 属性。

关键点：出于安全考虑，现代浏览器（如Chrome、Firefox）通常会忽略跨域（Cross-Origin）链接的 download 属性。您的前端在 tts.aispeak.top，而资源在 r2-assets.aispeak.top，这是两个不同的子域名，因此构成了跨域。

由于 download 属性被忽略，浏览器会执行默认操作：对于它能识别并播放的文件类型（如 audio/mpeg），它会选择直接在浏览器内打开并播放，这就导致了您看到的“跳转”而非“下载”。

Content-Disposition 的作用：

要强制浏览器下载文件，无论它是否能播放，服务器在响应时必须发送一个HTTP头：Content-Disposition: attachment; filename="your-file-name.mp3"。

这个头明确告诉浏览器：“这个资源是作为附件（attachment）提供的，请提示用户下载，并使用这个建议的文件名”。

代码分析：

前端 page.tsx：您的 handleDownload 函数实现是正确的，它尽力通过 link.download 属性来触发下载。问题不在前端。

后端 worker.js：

在您的备用下载方式（通过Worker代理，即 handleDownload 函数）中，您正确地设置了 Content-Disposition 头。如果下载走的是 /api/tts/download/... 这个路径，下载行为会是正常的。

然而，在您的主要流程（processAudioAsync 函数）中，当您将音频文件存入R2时（storeAudioFile 函数），您只设置了 contentType 和 cacheControl，没有设置 contentDisposition。

我们来看一下 storeAudioFile 函数：

// worker.js - 当前的代码
async function storeAudioFile(taskId, audioBuffer, env) {
  // ...
  const result = await env.AUDIOS.put(key, audioBuffer, {
    httpMetadata: {
      contentType: 'audio/mpeg',
      cacheControl: 'public, max-age=86400' // 1天缓存
    }
  });
  // ...
}


这就是问题的根源。当浏览器直接请求 R2 上的这个文件时，R2 返回的响应头里没有 Content-Disposition: attachment，因此浏览器选择播放它。

解决方案：在上传到 R2 时添加 Content-Disposition

要解决这个问题，我们只需要在将文件上传到 R2 时，把 Content-Disposition 作为 httpMetadata 的一部分加进去。这样，R2 就会在每次提供这个文件的直接链接时，都带上这个强制下载的头。

修改步骤：

请打开您的 worker.js 文件，找到 storeAudioFile 函数，并进行如下修改：

修改前：

// worker.js -> storeAudioFile
async function storeAudioFile(taskId, audioBuffer, env) {
  const key = `audios/${taskId}.mp3`;
  // ...
  try {
    // ...
    const result = await env.AUDIOS.put(key, audioBuffer, {
      httpMetadata: {
        contentType: 'audio/mpeg',
        cacheControl: 'public, max-age=86400' // 1天缓存
      }
    });
    // ...
  } catch (error) {
    // ...
  }
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

修改后：

// worker.js -> storeAudioFile
async function storeAudioFile(taskId, audioBuffer, env) {
  const key = `audios/${taskId}.mp3`;
  const audioSize = audioBuffer.byteLength;

  console.log(`[R2-AUDIO] Storing audio for task ${taskId}:`, {
    audioSize: audioSize,
    audioType: audioBuffer.constructor.name,
    bucketName: 'AUDIOS',
    key: key,
    sizeInMB: (audioSize / 1024 / 1024).toFixed(2)
  });

  try {
    const startTime = Date.now();

    const result = await env.AUDIOS.put(key, audioBuffer, {
      httpMetadata: {
        contentType: 'audio/mpeg',
        cacheControl: 'public, max-age=86400', // 1天缓存
        // --- ✨ 新增的关键行 ---
        contentDisposition: `attachment; filename="aispeak-tts-${taskId}.mp3"`
      }
    });

    const uploadTime = Date.now() - startTime;

    console.log(`[R2-AUDIO] ✅ Successfully stored audio for task ${taskId}:`, {
      audioSize: audioSize,
      uploadTimeMs: uploadTime,
      sizeInMB: (audioSize / 1024 / 1024).toFixed(2),
      timestamp: new Date().toISOString()
    });

    return result;
  } catch (error) {
    console.error(`[R2-AUDIO] ❌ Failed to store audio for task ${taskId}:`, {
      error: error.message,
      audioSize: audioSize,
      key: key
    });
    throw error;
  }
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
解释：

contentDisposition: 这是 httpMetadata 中用于设置 Content-Disposition 头的正确字段名。

attachment: 告诉浏览器这是一个附件，需要下载。

filename="...": 为下载的文件提供一个默认的文件名。我这里用 aispeak-tts-${taskId}.mp3 作为示例，您可以自定义。

操作流程

修改代码：将上述修改应用到您的 worker.js 文件中。

重新部署：使用 wrangler deploy 或 wrangler publish 命令将更新后的 Worker 部署到 Cloudflare。

测试：

在您的前端应用 tts.aispeak.top 上，生成一个新的音频文件。

当音频生成后，点击下载按钮。

现在，浏览器应该会直接弹出下载提示，而不是跳转到新页面。